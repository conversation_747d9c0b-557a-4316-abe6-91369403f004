import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  AIModelConfig,
  AIModelStore,
  AIModelValidationResult,
  AIModelProvider,
  OpenAIModelInfo,
  GeminiModelInfo,
  CachedModelData,
  ValidationState
} from '@/shared/types/aiModel';
import { aiProviderManager } from '../../../infrastructure/ai/providers/AIProviderManager';
// {{ AURA-X: Add - 导入AI提供商管理器. Approval: 寸止(ID:**********). }}

// {{ AURA-X: Add - 工具函数：生成配置哈希. Approval: 寸止(ID:**********). }}
function generateConfigHash(config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl' | 'modelName'>): string {
  const { provider, apiKey, baseUrl, modelName } = config;
  return btoa(`${provider}-${apiKey}-${baseUrl}-${modelName || ''}`).replace(/[^a-zA-Z0-9]/g, '');
}

// {{ AURA-X: Add - 生成UUID的兼容性函数. Approval: 寸止(ID:**********). }}
function generateUUID(): string {
  // 优先使用crypto.randomUUID()
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // 降级方案：使用时间戳和随机数
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function generateCacheKey(config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl'>): string {
  const { provider, apiKey, baseUrl } = config;
  return btoa(`${provider}-${apiKey}-${baseUrl}`).replace(/[^a-zA-Z0-9]/g, '');
}

export const useAIModelStore = create<AIModelStore>()(
  persist(
    (set, get) => ({
      models: [],
      activeModelId: null,
      // {{ AURA-X: Add - 初始化缓存和验证状态. Approval: 寸止(ID:**********). }}
      cachedModelData: {},
      validationStates: {},

      addModel: (config) => {
        const newModel: AIModelConfig = {
          ...config,
          id: generateUUID(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        // {{ AURA-X: Modify - 使用兼容性UUID生成函数. Approval: 寸止(ID:**********). }}

        set((state) => ({
          models: [...state.models, newModel],
          // 如果是第一个模型，自动设为活跃
          activeModelId: state.models.length === 0 ? newModel.id : state.activeModelId,
        }));
      },

      updateModel: (id, updates) => {
        set((state) => ({
          models: state.models.map((model) =>
            model.id === id
              ? { ...model, ...updates, updatedAt: new Date().toISOString() }
              : model
          ),
        }));
      },

      deleteModel: (id) => {
        set((state) => ({
          models: state.models.filter((model) => model.id !== id),
          activeModelId: state.activeModelId === id ? null : state.activeModelId,
        }));
      },

      setActiveModel: (id) => {
        set({ activeModelId: id });
      },

      getActiveModel: () => {
        const state = get();
        return state.models.find((model) => model.id === state.activeModelId) || null;
      },

      // 修复现有模型的视觉支持检测
      fixModelVisionSupport: () => {
        const checkModelSupportsVision = (provider: string, modelName: string): boolean | null => {
          if (!modelName || modelName.trim() === '') {
            return null;
          }

          const lowerModelName = modelName.toLowerCase();

          switch (provider) {
            case 'openai':
              // OpenAI: 包含 'o' 或以 'gpt' 开头的都认为是视觉模型
              if (lowerModelName.includes('o') || lowerModelName.startsWith('gpt')) {
                return true;
              }

              // {{ AURA-X: Modify - 简化状态，移除仅文本状态，统一为无法判断. Approval: 寸止(ID:**********). }}
              // 其他 OpenAI 模型无法判断
              return null;

            case 'gemini':
              // Gemini: 包含 'gemini' 的都支持视觉
              if (lowerModelName.includes('gemini')) {
                return true;
              }
              
              // 其他 Gemini 模型无法判断
              return null;

            default:
              // 第三方模型无法判断
              return null;
          }
        };

        set((state) => ({
          models: state.models.map((model) => ({
            ...model,
            supportsVision: checkModelSupportsVision(model.provider, model.modelName),
            updatedAt: new Date().toISOString()
          })),
        }));
      },

      validateModel: async (config) => {
        try {
          if (!config.provider || !config.apiKey || !config.baseUrl || !config.modelName) {
            return {
              isValid: false,
              error: '请填写完整的配置信息',
            };
          }

          const result = await aiProviderManager.validateProviderConfig(config.provider, {
            apiKey: config.apiKey,
            baseUrl: config.baseUrl,
            modelName: config.modelName
          });

          return {
            isValid: result.isValid,
            error: result.error,
            modelList: result.modelList
          };
        } catch (error) {
          return {
            isValid: false,
            error: error instanceof Error ? error.message : '验证失败',
          };
        }
      },
      // {{ AURA-X: Modify - 使用AI提供商管理器进行验证. Approval: 寸止(ID:**********). }}

      getModelList: async (provider, apiKey, baseUrl) => {
        try {
          return await aiProviderManager.getProviderModels(provider, {
            apiKey,
            baseUrl,
            modelName: '' // 获取模型列表时不需要指定模型名
          });
        } catch (error) {
          console.error('获取模型列表失败:', error);
          return [];
        }
      },
      // {{ AURA-X: Modify - 使用AI提供商管理器获取模型列表. Approval: 寸止(ID:**********). }}

      // {{ AURA-X: Add - 缓存管理方法. Approval: 寸止(ID:**********). }}
      // {{ AURA-X: Modify - 移除缓存过期机制，改为永久持久化. Approval: 寸止(ID:**********). }}
      getCachedModels: (config) => {
        const cacheKey = generateCacheKey(config);
        const cached = get().cachedModelData[cacheKey];

        if (!cached) return null;

        // 直接返回持久化的模型数据，无过期检查
        return cached.models;
      },

      setCachedModels: (config, models) => {
        const cacheKey = generateCacheKey(config);
        const cachedData: CachedModelData = {
          models,
          timestamp: Date.now(),
          provider: config.provider,
          apiKey: config.apiKey,
          baseUrl: config.baseUrl,
        };

        set((state) => ({
          cachedModelData: {
            ...state.cachedModelData,
            [cacheKey]: cachedData,
          },
        }));
      },

      // {{ AURA-X: Add - 验证状态管理方法. Approval: 寸止(ID:**********). }}
      getValidationState: (config) => {
        const configHash = generateConfigHash(config);
        return get().validationStates[configHash] || null;
      },

      setValidationState: (config, state) => {
        const configHash = generateConfigHash(config);
        const validationState: ValidationState = {
          ...state,
          configHash,
        };

        set((prevState) => ({
          validationStates: {
            ...prevState.validationStates,
            [configHash]: validationState,
          },
        }));
      },

      clearValidationState: (config) => {
        const configHash = generateConfigHash(config);
        set((state) => {
          const newValidationStates = { ...state.validationStates };
          delete newValidationStates[configHash];
          return { validationStates: newValidationStates };
        });
      },

      isConfigurationChanged: (config) => {
        const currentHash = generateConfigHash(config);
        const validationState = get().validationStates[currentHash];
        return !validationState || validationState.configHash !== currentHash;
      },
    }),
    {
      name: 'ai-model-store',
      version: 2, // {{ AURA-X: Modify - 升级版本清理旧数据. Approval: 寸止(ID:**********). }}
      migrate: (persistedState: any, version: number) => {
        // 清理包含xiamu提供商的旧数据
        if (version < 2) {
          const cleanedModels = persistedState.models?.filter((model: any) =>
            model.provider === 'openai' || model.provider === 'gemini'
          ) || [];

          return {
            ...persistedState,
            models: cleanedModels,
            cachedModelData: {}, // 清空缓存数据
            validationStates: {}, // 清空验证状态
            activeModelId: cleanedModels.length > 0 ? cleanedModels[0].id : null,
          };
        }
        return persistedState;
      },
    }
  )
);

// OpenAI模型验证
async function validateOpenAIModel(config: Partial<AIModelConfig>): Promise<AIModelValidationResult> {
  try {
    // {{ AURA-X: Modify - 修复#结尾URL的模型验证路径. Approval: 寸止(ID:**********). }}
    let baseUrl = config.baseUrl!;
    if (config.baseUrl!.endsWith('#')) {
      // 移除#并提取基础路径
      const urlWithoutHash = config.baseUrl!.slice(0, -1);
      const pathParts = urlWithoutHash.split('/');
      if (pathParts.length > 3 && pathParts[pathParts.length - 1] !== '') {
        pathParts.pop();
      }
      baseUrl = pathParts.join('/');
    } else if (config.baseUrl!.includes('/chat/completions') || config.baseUrl!.includes('/completions')) {
      // {{ AURA-X: Add - 处理完整API URL的情况. Approval: 寸止(ID:**********). }}
      const pathParts = config.baseUrl!.split('/');
      const completionsIndex = pathParts.findIndex((part: string) => part === 'completions' || part === 'chat');
      if (completionsIndex > 0) {
        baseUrl = pathParts.slice(0, completionsIndex).join('/');
      } else {
        baseUrl = baseUrl.replace(/\/+$/, '');
      }
    } else {
      baseUrl = baseUrl.replace(/\/+$/, '');
    }

    const apiUrl = config.baseUrl!.endsWith('#') ? `${baseUrl}/models` : `${baseUrl}/v1/models`;
    const response = await fetch(apiUrl, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const models = data.data as OpenAIModelInfo[];
    
    // 检查指定的模型是否存在
    const modelExists = config.modelName ? 
      models.some(model => model.id === config.modelName) : 
      true;

    if (!modelExists) {
      return {
        isValid: false,
        error: `模型 ${config.modelName} 不存在`,
        modelList: models.map(m => m.id),
      };
    }

    // 检查视觉支持（基于模型名称判断）
    const supportsVision = config.modelName ? 
      config.modelName.includes('vision') || config.modelName.includes('gpt') || config.modelName.includes('o') : 
      false;

    return {
      isValid: true,
      capabilities: {
        supportsText: true,
        supportsVision,
        supportsAudio: false,
      },
      modelList: models.map(m => m.id),
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'OpenAI API验证失败',
    };
  }
}

// Gemini模型验证
async function validateGeminiModel(config: Partial<AIModelConfig>): Promise<AIModelValidationResult> {
  try {
    const response = await fetch(`${config.baseUrl}/v1beta/models`, {
      headers: {
        'x-goog-api-key': config.apiKey!,
        'Content-Type': 'application/json',
      },
    });
    // {{ AURA-X: Modify - 使用官方x-goog-api-key认证和v1beta端点. Approval: 寸止(ID:1736937800). }}

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const models = data.models as GeminiModelInfo[];
    
    // 检查指定的模型是否存在
    const modelExists = config.modelName ? 
      models.some(model => model.name.includes(config.modelName!)) : 
      true;

    if (!modelExists) {
      return {
        isValid: false,
        error: `模型 ${config.modelName} 不存在`,
        modelList: models.map(m => m.name.split('/').pop() || m.name),
      };
    }

    // 检查视觉支持（基于模型名称和支持的生成方法判断）
    const targetModel = models.find(model => 
      config.modelName ? model.name.includes(config.modelName) : true
    );
    
    const supportsVision = targetModel ? 
      targetModel.name.includes('vision') || 
      targetModel.supportedGenerationMethods.includes('generateContent') :
      false;

    return {
      isValid: true,
      capabilities: {
        supportsText: true,
        supportsVision,
        supportsAudio: false,
      },
      modelList: models.map(m => m.name.split('/').pop() || m.name),
    };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Gemini API验证失败',
    };
  }
}

// 获取OpenAI模型列表
async function getOpenAIModelList(apiKey: string, baseUrl: string): Promise<string[]> {
  // {{ AURA-X: Modify - 修复#结尾URL的模型列表获取路径. Approval: 寸止(ID:**********). }}
  let normalizedBaseUrl = baseUrl;
  if (baseUrl.endsWith('#')) {
    // 移除#并提取基础路径
    const urlWithoutHash = baseUrl.slice(0, -1);
    const pathParts = urlWithoutHash.split('/');
    if (pathParts.length > 3 && pathParts[pathParts.length - 1] !== '') {
      pathParts.pop();
    }
    normalizedBaseUrl = pathParts.join('/');
  } else if (baseUrl.includes('/chat/completions') || baseUrl.includes('/completions')) {
    // {{ AURA-X: Add - 处理完整API URL的情况. Approval: 寸止(ID:**********). }}
    const pathParts = baseUrl.split('/');
    const completionsIndex = pathParts.findIndex((part: string) => part === 'completions' || part === 'chat');
    if (completionsIndex > 0) {
      normalizedBaseUrl = pathParts.slice(0, completionsIndex).join('/');
    } else {
      normalizedBaseUrl = normalizedBaseUrl.replace(/\/+$/, '');
    }
  } else {
    normalizedBaseUrl = normalizedBaseUrl.replace(/\/+$/, '');
  }

  const apiUrl = baseUrl.endsWith('#') ? `${normalizedBaseUrl}/models` : `${normalizedBaseUrl}/v1/models`;
  const response = await fetch(apiUrl, {
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`获取OpenAI模型列表失败: ${response.status}`);
  }

  const data = await response.json();
  return (data.data as OpenAIModelInfo[]).map(model => model.id);
}

// 获取Gemini模型列表
async function getGeminiModelList(apiKey: string, baseUrl: string): Promise<string[]> {
  const response = await fetch(`${baseUrl}/v1beta/models`, {
    headers: {
      'x-goog-api-key': apiKey,
      'Content-Type': 'application/json',
    },
  });
  // {{ AURA-X: Modify - 使用官方x-goog-api-key认证和v1beta端点. Approval: 寸止(ID:1736937800). }}

  if (!response.ok) {
    throw new Error(`获取Gemini模型列表失败: ${response.status}`);
  }

  const data = await response.json();
  return (data.models as GeminiModelInfo[]).map(model => 
    model.name.split('/').pop() || model.name
  );
}
