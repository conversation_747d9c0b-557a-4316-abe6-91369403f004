import React, { useState, useMemo, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarDaysIcon, ChartBarIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import { formatDate, getMonthDays, isInSamePeriod, addDays, subDays, startOfWeek, endOfWeek, eachDayOfInterval } from '@/shared/utils';
import { startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { cn } from '@/shared/utils/format';
// import * as anime from 'animejs'; // Temporarily disabled

export type CalendarViewType = 'week' | 'month' | 'year';
export type ComparisonType = 'none' | 'previous' | 'target';

interface ModernCalendarProps {
  onDateSelect?: (date: Date) => void;
  selectedDate?: Date;
  className?: string;
  defaultView?: CalendarViewType;
  showComparison?: boolean;
  showMiniStats?: boolean;
}

interface DayStats {
  date: Date;
  calories: number;
  targetCalories: number;
  percentage: number;
  status: 'no-data' | 'under' | 'normal' | 'over' | 'exceed';
  entries: number;
}

const ModernCalendar: React.FC<ModernCalendarProps> = ({
  onDateSelect,
  selectedDate,
  className,
  defaultView = 'month',
  showComparison = true,
  showMiniStats = true
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>(defaultView);
  const [comparison, setComparison] = useState<ComparisonType>('none');
  const [showHeatmap, setShowHeatmap] = useState(false);
  const { getDailySummary, getWeeklyAnalysis, getMonthlyAnalysis } = useNutritionStore();

  // 计算当前视图的日期数据
  const viewData = useMemo(() => {
    const today = new Date();
    
    switch (viewType) {
      case 'week': {
        const weekStart = startOfWeek(currentDate);
        const weekEnd = endOfWeek(currentDate);
        const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
        return { days, title: `${formatDate(weekStart, 'yyyy年MM月dd日')} - ${formatDate(weekEnd, 'MM月dd日')}` };
      }
      case 'year': {
        const year = currentDate.getFullYear();
        const months = Array.from({ length: 12 }, (_, i) => {
          const monthStart = new Date(year, i, 1);
          const monthEnd = new Date(year, i + 1, 0);
          const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
          return { monthStart, monthEnd, monthDays, month: i + 1 };
        });
        return { months, title: `${year}年`, year };
      }
      case 'month':
      default: {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        const monthStart = new Date(year, month, 1);
        const monthEnd = new Date(year, month + 1, 0);
        const firstDayOfWeek = monthStart.getDay();
        
        // 生成完整的日历网格
        const days: Date[] = [];
        // 添加上月末尾的日期
        for (let i = firstDayOfWeek - 1; i >= 0; i--) {
          days.push(subDays(monthStart, i + 1));
        }
        // 添加当月所有日期
        const monthDays = getMonthDays(year, month + 1);
        days.push(...monthDays);
        // 添加下月开头的日期，确保总共42天（6周）
        while (days.length < 42) {
          days.push(addDays(monthEnd, days.length - monthDays.length - firstDayOfWeek + 1));
        }
        
        return { days, title: `${year}年${month + 1}月`, year, month };
      }
    }
  }, [currentDate, viewType]);

  // 获取日期统计数据
  const getDayStats = (date: Date): DayStats => {
    const summary = getDailySummary(date);
    const targetCalories = summary?.calorieLimit || 2000;
    const calories = summary?.totalCalories || 0;
    const percentage = calories / targetCalories * 100;
    
    let status: DayStats['status'] = 'no-data';
    if (calories > 0) {
      if (percentage < 80) status = 'under';
      else if (percentage <= 110) status = 'normal';
      else if (percentage <= 130) status = 'over';
      else status = 'exceed';
    }
    
    return {
      date,
      calories,
      targetCalories,
      percentage,
      status,
      entries: summary ? Object.values(summary.mealBreakdown).reduce((total, meal) => total + meal.foodCount, 0) : 0
    };
  };

  // 获取热力图强度
  const getHeatmapIntensity = (stats: DayStats): number => {
    if (stats.status === 'no-data') return 0;
    return Math.min(Math.max(stats.percentage / 100, 0.1), 1);
  };

  // 导航函数
  const navigate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    switch (viewType) {
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'year':
        newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));
        break;
    }
    setCurrentDate(newDate);
  };

  // 获取日期样式
  const getDayStyle = (date: Date, stats: DayStats) => {
    const today = new Date();
    const isToday = isInSamePeriod(date, today, 'day');
    const isSelected = selectedDate && isInSamePeriod(date, selectedDate, 'day');
    const isCurrentMonth = viewType === 'month' && date.getMonth() === currentDate.getMonth();
    const isOtherMonth = viewType === 'month' && date.getMonth() !== currentDate.getMonth();
    
    let baseClasses = 'relative flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-200 cursor-pointer group';
    
    if (showHeatmap) {
      const intensity = getHeatmapIntensity(stats);
      const opacity = intensity === 0 ? 0.05 : 0.1 + intensity * 0.4;
      baseClasses += ` bg-primary bg-opacity-${Math.floor(opacity * 100)}`;
    }
    
    if (isSelected) {
      baseClasses += ' ring-2 ring-primary ring-offset-2 bg-primary text-primary-content';
    } else if (isToday) {
      baseClasses += ' ring-2 ring-accent bg-accent text-accent-content font-bold';
    } else if (isOtherMonth) {
      baseClasses += ' text-base-content/30';
    } else {
      switch (stats.status) {
        case 'under':
          baseClasses += ' bg-info/10 text-info-content hover:bg-info/20';
          break;
        case 'normal':
          baseClasses += ' bg-success/10 text-success-content hover:bg-success/20';
          break;
        case 'over':
          baseClasses += ' bg-warning/10 text-warning-content hover:bg-warning/20';
          break;
        case 'exceed':
          baseClasses += ' bg-error/10 text-error-content hover:bg-error/20';
          break;
        default:
          baseClasses += ' hover:bg-base-200';
      }
    }
    
    return baseClasses;
  };

  // 动画效果
  useEffect(() => {
    // Animation temporarily disabled to fix build
    // const elements = document.querySelectorAll('.calendar-day');
    // elements.forEach((el, index) => {
    //   anime.default({
    //     targets: el,
    //     opacity: [0, 1],
    //     translateY: [20, 0],
    //     delay: index * 20,
    //     duration: 300,
    //     easing: 'easeOutCubic'
    //   });
    // });
  }, [viewType, currentDate]);

  const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

  return (
    <div className={cn('bg-base-100 rounded-2xl shadow-lg border border-base-200', className)}>
      {/* 头部控制区 */}
      <div className="flex items-center justify-between p-4 border-b border-base-200">
        <div className="flex items-center gap-2">
          <button
            onClick={() => navigate('prev')}
            className="btn btn-ghost btn-sm btn-circle"
          >
            <ChevronLeftIcon className="w-4 h-4" />
          </button>
          
          <h3 className="text-lg font-bold text-base-content">
            {viewData.title}
          </h3>
          
          <button
            onClick={() => navigate('next')}
            className="btn btn-ghost btn-sm btn-circle"
          >
            <ChevronRightIcon className="w-4 h-4" />
          </button>
        </div>

        <div className="flex items-center gap-2">
          {/* 视图切换 */}
          <div className="btn-group">
            <button
              onClick={() => setViewType('week')}
              className={cn('btn btn-sm', viewType === 'week' ? 'btn-primary' : 'btn-ghost')}
            >
              周
            </button>
            <button
              onClick={() => setViewType('month')}
              className={cn('btn btn-sm', viewType === 'month' ? 'btn-primary' : 'btn-ghost')}
            >
              月
            </button>
            <button
              onClick={() => setViewType('year')}
              className={cn('btn btn-sm', viewType === 'year' ? 'btn-primary' : 'btn-ghost')}
            >
              年
            </button>
          </div>

          {/* 功能按钮 */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setShowHeatmap(!showHeatmap)}
              className={cn('btn btn-ghost btn-sm btn-circle', showHeatmap && 'btn-primary')}
              title="热力图模式"
            >
              <ChartBarIcon className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => setCurrentDate(new Date())}
              className="btn btn-ghost btn-sm"
            >
              今天
            </button>
          </div>
        </div>
      </div>

      {/* 日历内容 */}
      <div className="p-4">
        {viewType === 'week' && (
          <div className="grid grid-cols-7 gap-2">
            {/* 星期标题 */}
            {weekDays.map(day => (
              <div key={day} className="text-center text-sm font-medium text-base-content/70 py-2">
                {day}
              </div>
            ))}
            
            {/* 周视图日期 */}
            {viewData.days && viewData.days.map((date, index) => {
              const stats = getDayStats(date);
              return (
                <div
                  key={index}
                  onClick={() => onDateSelect?.(date)}
                  className={cn('calendar-day min-h-24', getDayStyle(date, stats))}
                >
                  <div className="text-lg font-bold">{date.getDate()}</div>
                  {stats.calories > 0 && (
                    <div className="text-xs mt-1 text-center">
                      <div className="font-medium">{Math.round(stats.calories)}</div>
                      <div className="text-xs opacity-70">卡</div>
                    </div>
                  )}
                  {stats.entries > 0 && (
                    <div className="absolute bottom-1 right-1 w-2 h-2 bg-current rounded-full opacity-50" />
                  )}
                </div>
              );
            })}
          </div>
        )}

        {viewType === 'month' && (
          <div className="grid grid-cols-7 gap-1">
            {/* 星期标题 */}
            {weekDays.map(day => (
              <div key={day} className="text-center text-sm font-medium text-base-content/70 py-2">
                {day}
              </div>
            ))}
            
            {/* 月视图日期 */}
            {viewData.days && viewData.days.map((date, index) => {
              const stats = getDayStats(date);
              return (
                <div
                  key={index}
                  onClick={() => onDateSelect?.(date)}
                  className={cn('calendar-day h-12 text-sm', getDayStyle(date, stats))}
                >
                  <div className="font-medium">{date.getDate()}</div>
                  {stats.calories > 0 && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-current opacity-50 rounded-b" />
                  )}
                  {stats.entries > 0 && (
                    <div className="absolute top-1 right-1 w-1 h-1 bg-current rounded-full" />
                  )}
                </div>
              );
            })}
          </div>
        )}

        {viewType === 'year' && (
          <div className="grid grid-cols-3 gap-4">
            {viewData.months && viewData.months.map(({ monthStart, monthDays, month }) => (
              <div key={month} className="bg-base-50 rounded-lg p-3">
                <div className="text-sm font-bold text-center mb-2">
                  {months[month - 1]}
                </div>
                <div className="grid grid-cols-7 gap-1">
                  {monthDays.map((date, dayIndex) => {
                    const stats = getDayStats(date);
                    const intensity = getHeatmapIntensity(stats);
                    return (
                      <div
                        key={dayIndex}
                        onClick={() => onDateSelect?.(date)}
                        className={cn(
                          'calendar-day w-6 h-6 flex items-center justify-center text-xs cursor-pointer rounded',
                          showHeatmap
                            ? `bg-primary bg-opacity-${Math.floor((0.1 + intensity * 0.4) * 100)}`
                            : 'hover:bg-base-200'
                        )}
                        style={showHeatmap ? {
                          backgroundColor: `hsl(var(--p) / ${0.1 + intensity * 0.4})`
                        } : undefined}
                      >
                        {date.getDate()}
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 图例和统计 */}
      <div className="px-4 pb-4 border-t border-base-200">
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-3 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-success/20"></div>
              <span className="text-base-content/70">达标</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-warning/20"></div>
              <span className="text-base-content/70">超标</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-error/20"></div>
              <span className="text-base-content/70">严重超标</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-info/20"></div>
              <span className="text-base-content/70">未达标</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full bg-base-200"></div>
              <span className="text-base-content/70">无数据</span>
            </div>
          </div>

          {showMiniStats && (
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4 text-base-content/50" />
                <span className="text-base-content/70">
                  {viewType === 'week' ? '7天' : viewType === 'month' ? '30天' : '365天'}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <ArrowTrendingUpIcon className="w-4 h-4 text-success" />
                <span className="text-success font-medium">85%</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernCalendar;