import React, { useState, useMemo } from 'react';
import { XMarkIcon, ChartBarIcon, ClockIcon, FireIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import { formatDate } from '@/shared/utils';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { DailySummary } from '@/shared/types';
import { cn } from '@/shared/utils/format';

interface EnhancedDateDetailProps {
  date: Date;
  summary: DailySummary | null;
  onClose: () => void;
  className?: string;
  showComparison?: boolean;
}

const EnhancedDateDetail: React.FC<EnhancedDateDetailProps> = ({
  date,
  summary,
  onClose,
  className,
  showComparison = true
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'meals' | 'nutrition'>('overview');
  const { getDailySummary } = useNutritionStore();

  // 获取前一天的数据进行对比
  const previousDay = useMemo(() => {
    const prevDate = new Date(date);
    prevDate.setDate(prevDate.getDate() - 1);
    return getDailySummary(prevDate);
  }, [date, getDailySummary]);

  const getStatusColor = (percentage: number) => {
    if (percentage < 80) return 'text-info';
    if (percentage <= 110) return 'text-success';
    if (percentage <= 130) return 'text-warning';
    return 'text-error';
  };

  const getStatusBg = (percentage: number) => {
    if (percentage < 80) return 'bg-info/10';
    if (percentage <= 110) return 'bg-success/10';
    if (percentage <= 130) return 'bg-warning/10';
    return 'bg-error/10';
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else {
      return formatDate(date);
    }
  };

  const getComparisonData = (current: number, previous: number) => {
    if (previous === 0) return { change: 0, percentage: 0, trend: 'stable' as const };
    
    const change = current - previous;
    const percentage = (change / previous) * 100;
    const trend = Math.abs(percentage) < 5 ? 'stable' : percentage > 0 ? 'up' : 'down';
    
    return { change, percentage, trend };
  };

  if (!summary) {
    return (
      <div className={cn('bg-base-100 rounded-2xl shadow-lg border border-base-200', className)}>
        <div className="flex items-center justify-between p-4 border-b border-base-200">
          <h3 className="text-lg font-bold text-base-content">
            {formatDate(date)}
          </h3>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-circle"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>
        
        <div className="p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-base-200 rounded-full flex items-center justify-center">
            <ChartBarIcon className="w-8 h-8 text-base-content/50" />
          </div>
          <h4 className="text-lg font-semibold text-base-content mb-2">暂无数据</h4>
          <p className="text-base-content/70">
            这一天还没有记录任何营养数据
          </p>
        </div>
      </div>
    );
  }

  const percentage = summary.percentage;
  const caloriesComparison = showComparison && previousDay ? 
    getComparisonData(summary.totalCalories, previousDay.totalCalories) : null;

  return (
    <div className={cn('bg-base-100 rounded-2xl shadow-lg border border-base-200', className)}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-base-200">
        <div>
          <h3 className="text-lg font-bold text-base-content">
            {formatDate(date)}
          </h3>
          <p className="text-sm text-base-content/70">
            {formatDate(date)}
          </p>
        </div>
        <button
          onClick={onClose}
          className="btn btn-ghost btn-sm btn-circle"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </div>

      {/* 标签页 */}
      <div className="px-4 pt-4">
        <div className="tabs tabs-boxed mb-4">
          <button
            onClick={() => setActiveTab('overview')}
            className={cn('tab', activeTab === 'overview' && 'tab-active')}
          >
            总览
          </button>
          <button
            onClick={() => setActiveTab('meals')}
            className={cn('tab', activeTab === 'meals' && 'tab-active')}
          >
            餐次
          </button>
          <button
            onClick={() => setActiveTab('nutrition')}
            className={cn('tab', activeTab === 'nutrition' && 'tab-active')}
          >
            营养
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="px-4 pb-4">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            {/* 卡路里概览 */}
            <div className={cn('p-4 rounded-xl', getStatusBg(percentage))}>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <FireIcon className={cn('w-5 h-5', getStatusColor(percentage))} />
                  <span className="font-semibold text-base-content">总卡路里</span>
                </div>
                <div className={cn('text-2xl font-bold', getStatusColor(percentage))}>
                  {Math.round(summary.totalCalories)}
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-base-content/70">
                  目标: {Math.round(summary.calorieLimit)} kcal
                </span>
                <span className={cn('font-medium', getStatusColor(percentage))}>
                  {Math.round(percentage)}%
                </span>
              </div>
              
              <div className="w-full bg-base-200 rounded-full h-2 mt-2">
                <div
                  className={cn('h-2 rounded-full transition-all duration-300', {
                    'bg-info': percentage < 80,
                    'bg-success': percentage >= 80 && percentage <= 110,
                    'bg-warning': percentage > 110 && percentage <= 130,
                    'bg-error': percentage > 130
                  })}
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                />
              </div>

              {/* 对比数据 */}
              {caloriesComparison && (
                <div className="mt-3 pt-3 border-t border-base-content/10">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-base-content/70">较昨天</span>
                    <div className="flex items-center gap-1">
                      <ArrowTrendingUpIcon className={cn('w-4 h-4', {
                        'text-warning': caloriesComparison.trend === 'up',
                        'text-success': caloriesComparison.trend === 'down',
                        'text-base-content/50': caloriesComparison.trend === 'stable'
                      })} />
                      <span className={cn('font-medium', {
                        'text-warning': caloriesComparison.trend === 'up',
                        'text-success': caloriesComparison.trend === 'down',
                        'text-base-content/50': caloriesComparison.trend === 'stable'
                      })}>
                        {caloriesComparison.change > 0 ? '+' : ''}
                        {Math.round(caloriesComparison.change)} kcal
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 餐次分布 */}
            <div className="bg-base-50 rounded-xl p-4">
              <h4 className="font-semibold text-base-content mb-3">餐次分布</h4>
              <div className="space-y-2">
                {Object.entries(summary.mealBreakdown).map(([mealType, mealSummary], index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-primary" />
                      <span className="text-sm text-base-content/70">
                        {mealType === 'breakfast' ? '早餐' : 
                         mealType === 'lunch' ? '午餐' : 
                         mealType === 'dinner' ? '晚餐' : '加餐'}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-base-content">
                      {Math.round(mealSummary.calories)} kcal
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 记录信息 */}
            <div className="bg-base-50 rounded-xl p-4">
              <div className="flex items-center gap-2 mb-3">
                <ClockIcon className="w-5 h-5 text-base-content/70" />
                <span className="font-semibold text-base-content">记录信息</span>
              </div>
              <div className="text-sm text-base-content/70">
                <div className="flex justify-between">
                  <span>总记录数:</span>
                  <span>{Object.values(summary.mealBreakdown).reduce((total, meal) => total + meal.foodCount, 0)} 条</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span>最后更新:</span>
                  <span>{formatDate(summary.updatedAt || summary.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'meals' && (
          <div className="space-y-4">
            {Object.entries(summary.mealBreakdown).map(([mealType, mealSummary], index) => (
              <div key={index} className="bg-base-50 rounded-xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-base-content">
                    {mealType === 'breakfast' ? '早餐' : 
                     mealType === 'lunch' ? '午餐' : 
                     mealType === 'dinner' ? '晚餐' : '加餐'}
                  </h4>
                  <div className="text-lg font-bold text-primary">
                    {Math.round(mealSummary.calories)} kcal
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-base-content/70">
                    <div className="flex justify-between">
                      <span>食物数量:</span>
                      <span>{mealSummary.foodCount} 种</span>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span>完成度:</span>
                      <span>{Math.round(mealSummary.percentage)}%</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'nutrition' && (
          <div className="space-y-4">
            {/* 宏量营养素 */}
            <div className="bg-base-50 rounded-xl p-4">
              <h4 className="font-semibold text-base-content mb-3">宏量营养素</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-info">
                    {Math.round(summary.nutrition.protein)}g
                  </div>
                  <div className="text-xs text-base-content/70">蛋白质</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-warning">
                    {Math.round(summary.nutrition.carbs)}g
                  </div>
                  <div className="text-xs text-base-content/70">碳水化合物</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-error">
                    {Math.round(summary.nutrition.fat)}g
                  </div>
                  <div className="text-xs text-base-content/70">脂肪</div>
                </div>
              </div>
            </div>

            {/* 微量营养素 */}
            <div className="bg-base-50 rounded-xl p-4">
              <h4 className="font-semibold text-base-content mb-3">其他营养素</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-base-content/70">膳食纤维:</span>
                  <span className="font-medium text-base-content">
                    {Math.round(summary.nutrition.fiber)}g
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">糖分:</span>
                  <span className="font-medium text-base-content">
                    {Math.round(summary.nutrition.sugar)}g
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-base-content/70">钠:</span>
                  <span className="font-medium text-base-content">
                    {Math.round(summary.nutrition.sodium)}mg
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedDateDetail;