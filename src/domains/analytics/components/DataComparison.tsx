import React, { useState, useMemo } from 'react';
import { ChevronUpIcon, ChevronDownIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { formatDate, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, subWeeks, subMonths, subYears } from '@/shared/utils';
import { cn } from '@/shared/utils/format';

export type ComparisonPeriod = 'week' | 'month' | 'year';
export type ComparisonMetric = 'calories' | 'protein' | 'carbs' | 'fat' | 'adherence';

interface ComparisonData {
  current: {
    value: number;
    period: string;
    details: {
      total: number;
      average: number;
      days: number;
      adherenceRate: number;
    };
  };
  previous: {
    value: number;
    period: string;
    details: {
      total: number;
      average: number;
      days: number;
      adherenceRate: number;
    };
  };
  change: {
    absolute: number;
    percentage: number;
    trend: 'up' | 'down' | 'stable';
  };
}

interface DataComparisonProps {
  period: ComparisonPeriod;
  metric: ComparisonMetric;
  referenceDate?: Date;
  className?: string;
}

const DataComparison: React.FC<DataComparisonProps> = ({
  period,
  metric,
  referenceDate = new Date(),
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { getWeeklyAnalysis, getMonthlyAnalysis, getDailySummary } = useNutritionStore();

  const comparisonData = useMemo((): ComparisonData => {
    let currentStart: Date, currentEnd: Date, previousStart: Date, previousEnd: Date;
    
    // 确定时间范围
    switch (period) {
      case 'week':
        currentStart = startOfWeek(referenceDate);
        currentEnd = endOfWeek(referenceDate);
        previousStart = startOfWeek(subWeeks(referenceDate, 1));
        previousEnd = endOfWeek(subWeeks(referenceDate, 1));
        break;
      case 'month':
        currentStart = startOfMonth(referenceDate);
        currentEnd = endOfMonth(referenceDate);
        previousStart = startOfMonth(subMonths(referenceDate, 1));
        previousEnd = endOfMonth(subMonths(referenceDate, 1));
        break;
      case 'year':
        currentStart = startOfYear(referenceDate);
        currentEnd = endOfYear(referenceDate);
        previousStart = startOfYear(subYears(referenceDate, 1));
        previousEnd = endOfYear(subYears(referenceDate, 1));
        break;
    }

    // 获取数据
    const getCurrentData = () => {
      switch (period) {
        case 'week':
          return getWeeklyAnalysis(referenceDate) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        case 'month':
          return getMonthlyAnalysis(referenceDate.getFullYear(), referenceDate.getMonth() + 1) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        case 'year':
          // 简化年度数据获取
          return getMonthlyAnalysis(referenceDate.getFullYear(), referenceDate.getMonth() + 1) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        default:
          return { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
      }
    };

    const getPreviousData = () => {
      const prevDate = period === 'week' ? subWeeks(referenceDate, 1) :
                      period === 'month' ? subMonths(referenceDate, 1) :
                      subYears(referenceDate, 1);
      
      switch (period) {
        case 'week':
          return getWeeklyAnalysis(prevDate) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        case 'month':
          return getMonthlyAnalysis(prevDate.getFullYear(), prevDate.getMonth() + 1) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        case 'year':
          return getMonthlyAnalysis(prevDate.getFullYear(), prevDate.getMonth() + 1) || { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
        default:
          return { calories: { average: 0, total: 0, target: 0, adherenceRate: 0 }, trends: [] };
      }
    };

    const currentData = getCurrentData();
    const previousData = getPreviousData();

    // 根据指标获取具体数值
    const getMetricValue = (data: any, metric: ComparisonMetric) => {
      switch (metric) {
        case 'calories':
          return data.calories?.average || 0;
        case 'protein':
          return data.protein?.average || 0;
        case 'carbs':
          return data.carbs?.average || 0;
        case 'fat':
          return data.fat?.average || 0;
        case 'adherence':
          return (data.calories?.adherenceRate || 0) * 100;
        default:
          return 0;
      }
    };

    const currentValue = getMetricValue(currentData, metric);
    const previousValue = getMetricValue(previousData, metric);
    const change = currentValue - previousValue;
    const changePercentage = previousValue > 0 ? (change / previousValue) * 100 : 0;

    return {
      current: {
        value: currentValue,
        period: `${formatDate(currentStart, 'MM/dd')} - ${formatDate(currentEnd, 'MM/dd')}`,
        details: {
          total: currentData.calories?.total || 0,
          average: currentData.calories?.average || 0,
          days: currentData.trends?.length || 0,
          adherenceRate: (currentData.calories?.adherenceRate || 0) * 100
        }
      },
      previous: {
        value: previousValue,
        period: `${formatDate(previousStart, 'MM/dd')} - ${formatDate(previousEnd, 'MM/dd')}`,
        details: {
          total: previousData.calories?.total || 0,
          average: previousData.calories?.average || 0,
          days: previousData.trends?.length || 0,
          adherenceRate: (previousData.calories?.adherenceRate || 0) * 100
        }
      },
      change: {
        absolute: change,
        percentage: changePercentage,
        trend: Math.abs(changePercentage) < 5 ? 'stable' : changePercentage > 0 ? 'up' : 'down'
      }
    };
  }, [period, metric, referenceDate, getWeeklyAnalysis, getMonthlyAnalysis]);

  const getMetricUnit = (metric: ComparisonMetric) => {
    switch (metric) {
      case 'calories':
        return 'kcal';
      case 'protein':
      case 'carbs':
      case 'fat':
        return 'g';
      case 'adherence':
        return '%';
      default:
        return '';
    }
  };

  const getMetricLabel = (metric: ComparisonMetric) => {
    switch (metric) {
      case 'calories':
        return '卡路里';
      case 'protein':
        return '蛋白质';
      case 'carbs':
        return '碳水化合物';
      case 'fat':
        return '脂肪';
      case 'adherence':
        return '目标达成率';
      default:
        return '';
    }
  };

  const getPeriodLabel = (period: ComparisonPeriod) => {
    switch (period) {
      case 'week':
        return '周';
      case 'month':
        return '月';
      case 'year':
        return '年';
      default:
        return '';
    }
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <ArrowTrendingUpIcon className="w-4 h-4" />;
      case 'down':
        return <ArrowTrendingDownIcon className="w-4 h-4" />;
      case 'stable':
        return <div className="w-4 h-4 flex items-center justify-center">
          <div className="w-3 h-0.5 bg-current rounded" />
        </div>;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable', metric: ComparisonMetric) => {
    if (trend === 'stable') return 'text-base-content/70';
    
    // 对于目标达成率，上升是好的
    if (metric === 'adherence') {
      return trend === 'up' ? 'text-success' : 'text-warning';
    }
    
    // 对于其他指标，根据具体情况判断
    return trend === 'up' ? 'text-warning' : 'text-success';
  };

  return (
    <div className={cn('bg-base-100 rounded-xl border border-base-200 p-4', className)}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-base-content">
          {getPeriodLabel(period)}对比 · {getMetricLabel(metric)}
        </h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="btn btn-ghost btn-xs btn-circle"
        >
          {isExpanded ? (
            <ChevronUpIcon className="w-4 h-4" />
          ) : (
            <ChevronDownIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* 主要指标对比 */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-xs text-base-content/70 mb-1">本{getPeriodLabel(period)}</div>
          <div className="text-2xl font-bold text-primary">
            {Math.round(comparisonData.current.value)}
            <span className="text-sm font-normal text-base-content/70 ml-1">
              {getMetricUnit(metric)}
            </span>
          </div>
          <div className="text-xs text-base-content/50">
            {comparisonData.current.period}
          </div>
        </div>

        <div className="text-center">
          <div className="text-xs text-base-content/70 mb-1">上{getPeriodLabel(period)}</div>
          <div className="text-2xl font-bold text-base-content/50">
            {Math.round(comparisonData.previous.value)}
            <span className="text-sm font-normal text-base-content/70 ml-1">
              {getMetricUnit(metric)}
            </span>
          </div>
          <div className="text-xs text-base-content/50">
            {comparisonData.previous.period}
          </div>
        </div>
      </div>

      {/* 变化趋势 */}
      <div className="flex items-center justify-center gap-2 p-3 bg-base-50 rounded-lg">
        <div className={cn('flex items-center gap-1', getTrendColor(comparisonData.change.trend, metric))}>
          {getTrendIcon(comparisonData.change.trend)}
          <span className="text-sm font-medium">
            {comparisonData.change.percentage >= 0 ? '+' : ''}
            {Math.round(comparisonData.change.percentage)}%
          </span>
        </div>
        <div className="text-sm text-base-content/70">
          ({comparisonData.change.absolute >= 0 ? '+' : ''}
          {Math.round(comparisonData.change.absolute)} {getMetricUnit(metric)})
        </div>
      </div>

      {/* 详细信息 */}
      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-base-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-base-content mb-2">本{getPeriodLabel(period)}详情</div>
              <div className="space-y-1 text-base-content/70">
                <div className="flex justify-between">
                  <span>总计:</span>
                  <span>{Math.round(comparisonData.current.details.total)} kcal</span>
                </div>
                <div className="flex justify-between">
                  <span>平均:</span>
                  <span>{Math.round(comparisonData.current.details.average)} kcal</span>
                </div>
                <div className="flex justify-between">
                  <span>记录天数:</span>
                  <span>{comparisonData.current.details.days} 天</span>
                </div>
                <div className="flex justify-between">
                  <span>达成率:</span>
                  <span className="text-success">{Math.round(comparisonData.current.details.adherenceRate)}%</span>
                </div>
              </div>
            </div>

            <div>
              <div className="font-medium text-base-content mb-2">上{getPeriodLabel(period)}详情</div>
              <div className="space-y-1 text-base-content/50">
                <div className="flex justify-between">
                  <span>总计:</span>
                  <span>{Math.round(comparisonData.previous.details.total)} kcal</span>
                </div>
                <div className="flex justify-between">
                  <span>平均:</span>
                  <span>{Math.round(comparisonData.previous.details.average)} kcal</span>
                </div>
                <div className="flex justify-between">
                  <span>记录天数:</span>
                  <span>{comparisonData.previous.details.days} 天</span>
                </div>
                <div className="flex justify-between">
                  <span>达成率:</span>
                  <span>{Math.round(comparisonData.previous.details.adherenceRate)}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataComparison;