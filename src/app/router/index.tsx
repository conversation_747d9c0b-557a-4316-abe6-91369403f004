import React from 'react';
import { createBrowserRouter, Navigate, useSearchParams } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { PageTransition } from '@/shared/components/animations';
import ProfileSetupPage from '@/app/pages/ProfileSetup';
import DashboardPage from '@/app/pages/Dashboard';
import ReportPage from '@/app/pages/ReportPage';
import ProfilePage from '@/app/pages/ProfilePage';
import FoodRecordPage from '@/app/pages/FoodRecord';


// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  
  if (!isProfileComplete) {
    return <Navigate to="/setup" replace />;
  }
  
  return <>{children}</>;
};

// 设置页面守卫
const SetupRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete } = useUserStore();
  const [searchParams] = useSearchParams();
  const isEditMode = searchParams.get('mode') === 'edit';

  // 如果档案已完成且不是编辑模式，重定向到dashboard
  if (isProfileComplete && !isEditMode) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// 根路由重定向
const RootRedirect: React.FC = () => {
  const { isProfileComplete } = useUserStore();
  
  return <Navigate to={isProfileComplete ? "/dashboard" : "/setup"} replace />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <RootRedirect />
  },
  {
    path: "/setup",
    element: (
      <SetupRoute>
        <PageTransition>
          <ProfileSetupPage />
        </PageTransition>
      </SetupRoute>
    )
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <DashboardPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/reports",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <ReportPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },

  {
    path: "/profile",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <ProfilePage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/records",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <FoodRecordPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "*",
    element: <Navigate to="/" replace />
  }
]);