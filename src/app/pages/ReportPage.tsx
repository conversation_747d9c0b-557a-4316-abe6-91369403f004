import React, { useState, useMemo } from 'react';
import {
  <PERSON>sponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  RadialBarChart,
  RadialBar,
  Legend
} from 'recharts';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useExerciseStore } from '@/domains/exercise/stores/exerciseStore';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { BottomNavigation } from '@/shared/components/navigation';
import { formatDate } from '@/shared/utils';
import { eachDayOfInterval, subDays, subMonths, subYears, startOfDay } from 'date-fns';
import { AIService } from '@/infrastructure/ai/AIService';

type TimePeriod = 'week' | 'month' | 'year';

const ReportPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('week');
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [aiReport, setAiReport] = useState<string>('');
  
  const { getDailySummary } = useNutritionStore();
  const { getDailyExerciseRecords } = useExerciseStore();
  const { getActiveModel } = useAIModelStore();

  // 获取时间范围数据
  const reportData = useMemo(() => {
    const today = new Date();
    let dateRange: Date[] = [];

    switch (selectedPeriod) {
      case 'week':
        // 获取过去7天
        dateRange = eachDayOfInterval({
          start: subDays(today, 6),
          end: today
        });
        break;
      case 'month':
        // 获取过去30天
        dateRange = eachDayOfInterval({
          start: subDays(today, 29),
          end: today
        });
        break;
      case 'year':
        // 获取过去12个月的每月第一天
        dateRange = [];
        for (let i = 11; i >= 0; i--) {
          const monthStart = startOfDay(subMonths(today, i));
          dateRange.push(monthStart);
        }
        break;
    }

    // 营养趋势数据
    const nutritionTrends = dateRange.map(date => {
      const summary = getDailySummary(date);
      return {
        date: formatDate(date, selectedPeriod === 'year' ? 'MM月' : selectedPeriod === 'month' ? 'MM-dd' : 'MM/dd'),
        calories: summary?.totalCalories || 0,
        protein: summary?.nutrition?.protein || 0,
        carbs: summary?.nutrition?.carbs || 0,
        fat: summary?.nutrition?.fat || 0,
        target: summary?.calorieLimit || 2000
      };
    });

    // 三餐卡路里统计数据
    const mealCaloriesData = [
      { name: '早餐', calories: 0, fill: '#ff6b6b' },
      { name: '午餐', calories: 0, fill: '#4ecdc4' },
      { name: '晚餐', calories: 0, fill: '#45b7d1' },
      { name: '零食', calories: 0, fill: '#96ceb4' }
    ];

    // 计算三餐总卡路里
    dateRange.forEach(date => {
      const summary = getDailySummary(date);
      if (summary?.mealBreakdown) {
        mealCaloriesData[0].calories += summary.mealBreakdown.breakfast?.calories || 0;
        mealCaloriesData[1].calories += summary.mealBreakdown.lunch?.calories || 0;
        mealCaloriesData[2].calories += summary.mealBreakdown.dinner?.calories || 0;
        mealCaloriesData[3].calories += summary.mealBreakdown.snack?.calories || 0;
      }
    });

    // 运动数据
    const exerciseData = dateRange.map(date => {
      const dailyExercise = getDailyExerciseRecords(date);
      return {
        date: formatDate(date, selectedPeriod === 'year' ? 'MM月' : selectedPeriod === 'month' ? 'MM-dd' : 'MM/dd'),
        calories: dailyExercise.totalCaloriesBurned,
        duration: dailyExercise.totalDuration
      };
    });

    return {
      nutritionTrends,
      mealCaloriesData,
      exerciseData
    };
  }, [selectedPeriod, getDailySummary, getDailyExerciseRecords]);

  // 生成综合AI健康建议报告
  const generateComprehensiveReport = async () => {
    const activeModel = getActiveModel();
    if (!activeModel) {
      setAiReport('<p style="color: red;">请先配置AI模型</p>');
      return;
    }

    setIsGeneratingReport(true);

    try {
      const periodText = selectedPeriod === 'week' ? '本周' : selectedPeriod === 'month' ? '本月' : '本年';

      // 收集营养数据
      const nutritionSummary = {
        totalCalories: reportData.nutritionTrends.reduce((sum, day) => sum + day.calories, 0),
        avgCalories: reportData.nutritionTrends.reduce((sum, day) => sum + day.calories, 0) / reportData.nutritionTrends.length,
        totalProtein: reportData.nutritionTrends.reduce((sum, day) => sum + day.protein, 0),
        totalCarbs: reportData.nutritionTrends.reduce((sum, day) => sum + day.carbs, 0),
        totalFat: reportData.nutritionTrends.reduce((sum, day) => sum + day.fat, 0),
        mealDistribution: reportData.mealCaloriesData
      };

      // 收集运动数据
      const exerciseSummary = {
        totalCaloriesBurned: reportData.exerciseData.reduce((sum, day) => sum + day.calories, 0),
        totalDuration: reportData.exerciseData.reduce((sum, day) => sum + day.duration, 0),
        avgDailyBurn: reportData.exerciseData.reduce((sum, day) => sum + day.calories, 0) / reportData.exerciseData.length,
        activeDays: reportData.exerciseData.filter(day => day.calories > 0).length
      };

      // 构建综合分析提示词
      const prompt = `
作为专业的营养师和健身教练，请基于以下${periodText}的完整健康数据，生成一份综合的健康建议报告：

**营养摄入数据：**
- 总卡路里摄入：${nutritionSummary.totalCalories}卡
- 平均每日卡路里：${Math.round(nutritionSummary.avgCalories)}卡
- 总蛋白质：${nutritionSummary.totalProtein}g
- 总碳水化合物：${nutritionSummary.totalCarbs}g
- 总脂肪：${nutritionSummary.totalFat}g
- 三餐分配：早餐${reportData.mealCaloriesData[0].calories}卡，午餐${reportData.mealCaloriesData[1].calories}卡，晚餐${reportData.mealCaloriesData[2].calories}卡，零食${reportData.mealCaloriesData[3].calories}卡

**运动数据：**
- 总消耗卡路里：${exerciseSummary.totalCaloriesBurned}卡
- 总运动时长：${exerciseSummary.totalDuration}分钟
- 平均每日消耗：${Math.round(exerciseSummary.avgDailyBurn)}卡
- 活跃天数：${exerciseSummary.activeDays}天

请提供以下方面的专业建议，使用HTML格式返回：
1. 整体健康状况评估
2. 营养摄入优化建议
3. 运动计划调整建议
4. 减肥/维重策略
5. 下阶段目标建议

要求：使用HTML标签格式化，包含标题、列表、强调等元素，内容专业且实用。
`;

      // 这里需要实际的AI调用，暂时使用模拟数据
      setAiReport(`
        <h3>🏥 ${periodText}综合健康建议报告</h3>

        <h4>📊 整体健康状况评估</h4>
        <p>基于您${periodText}的数据分析，平均每日摄入${Math.round(nutritionSummary.avgCalories)}卡路里，运动消耗${Math.round(exerciseSummary.avgDailyBurn)}卡路里。</p>

        <h4>🥗 营养摄入优化建议</h4>
        <ul>
          <li><strong>卡路里平衡：</strong>建议保持当前摄入水平，注意营养素比例</li>
          <li><strong>蛋白质：</strong>适当增加优质蛋白质摄入，建议每日1.2-1.6g/kg体重</li>
          <li><strong>三餐分配：</strong>优化三餐比例，建议早餐30%、午餐40%、晚餐30%</li>
        </ul>

        <h4>💪 运动计划调整建议</h4>
        <ul>
          <li><strong>运动频率：</strong>建议增加到每周${Math.max(5, exerciseSummary.activeDays + 1)}天</li>
          <li><strong>运动强度：</strong>结合有氧和力量训练，提高代谢效率</li>
          <li><strong>目标消耗：</strong>建议每日运动消耗300-500卡路里</li>
        </ul>

        <h4>⚖️ 减肥/维重策略</h4>
        <ul>
          <li><strong>热量缺口：</strong>保持每日300-500卡路里的适度缺口</li>
          <li><strong>减重速度：</strong>建议每周减重0.5-1kg，健康可持续</li>
          <li><strong>监测指标：</strong>关注体重、体脂率和身体围度变化</li>
        </ul>

        <h4>🎯 下阶段目标建议</h4>
        <ul>
          <li>继续保持规律的饮食记录习惯</li>
          <li>逐步增加运动强度和频率</li>
          <li>关注营养素质量，选择天然食材</li>
          <li>保持充足睡眠，促进新陈代谢</li>
        </ul>

        <p><em>注：此为基于数据的个性化建议，如有特殊健康状况请咨询专业医师。</em></p>
      `);
    } catch (error) {
      console.error('生成AI报告失败:', error);
      setAiReport('<p style="color: red;">生成报告失败，请稍后重试</p>');
    } finally {
      setIsGeneratingReport(false);
    }
  };

  return (
    <div className="relative">
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-4">
        <div className="container mx-auto max-w-7xl">
          {/* 页面标题 */}
          <div className="text-center mb-8">
            <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
              📊 数据报告
            </h1>
            <p className="text-gray-600 lg:text-lg">
              查看您的营养、运动数据分析和AI智能建议
            </p>
          </div>

          {/* 时间筛选器 */}
          <div className="flex justify-center mb-8">
            <div className="btn-group gap-2">
              {(['week', 'month', 'year'] as TimePeriod[]).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`btn min-h-[44px] ${selectedPeriod === period ? 'btn-primary' : 'btn-outline'}`}
                >
                  {period === 'week' ? '本周' : period === 'month' ? '本月' : '本年'}
                </button>
              ))}
            </div>
          </div>

          {/* 图表区域 */}
          <div className="space-y-8">
            {/* 营养趋势图 */}
            <div className="card bg-white/80 backdrop-blur-sm shadow-xl">
              <div className="card-body">
                <h2 className="card-title text-lg font-bold text-gray-800 mb-4">
                  🍽️ 营养摄入趋势
                </h2>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={reportData.nutritionTrends}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis 
                        dataKey="date" 
                        stroke="#64748b"
                        fontSize={12}
                      />
                      <YAxis stroke="#64748b" fontSize={12} />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #e2e8f0',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="calories" 
                        stroke="#3b82f6" 
                        strokeWidth={2}
                        name="卡路里"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="target" 
                        stroke="#ef4444" 
                        strokeDasharray="5 5"
                        name="目标"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* 三餐卡路里统计和运动数据柱状图 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 三餐卡路里统计柱状图 */}
              <div className="card bg-white/80 backdrop-blur-sm shadow-xl">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold text-gray-800 mb-4">
                    🍽️ 三餐卡路里统计
                  </h2>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={reportData.mealCaloriesData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                        <XAxis
                          dataKey="name"
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <YAxis stroke="#64748b" fontSize={12} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e2e8f0',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                          formatter={(value) => [`${value}卡`, '卡路里']}
                        />
                        <Bar
                          dataKey="calories"
                          name="卡路里"
                          radius={[4, 4, 0, 0]}
                        >
                          {reportData.mealCaloriesData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              {/* 运动数据柱状图 */}
              <div className="card bg-white/80 backdrop-blur-sm shadow-xl">
                <div className="card-body">
                  <h2 className="card-title text-lg font-bold text-gray-800 mb-4">
                    🏃‍♂️ 运动消耗统计
                  </h2>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={reportData.exerciseData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                        <XAxis 
                          dataKey="date" 
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <YAxis stroke="#64748b" fontSize={12} />
                        <Tooltip 
                          contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #e2e8f0',
                            borderRadius: '8px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                        />
                        <Bar 
                          dataKey="calories" 
                          fill="#f59e0b" 
                          name="消耗卡路里"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>

            {/* AI建议报告区域 */}
            <div className="card bg-white/80 backdrop-blur-sm shadow-xl">
              <div className="card-body">
                <h2 className="card-title text-lg font-bold text-gray-800 mb-4">
                  🤖 AI智能建议
                </h2>
                
                {/* AI综合建议生成 */}
                <div className="flex justify-center mb-4">
                  <button
                    onClick={generateComprehensiveReport}
                    disabled={isGeneratingReport}
                    className="btn btn-primary min-h-[44px] gap-2"
                  >
                    {isGeneratingReport ? (
                      <span className="loading loading-spinner loading-sm"></span>
                    ) : (
                      '🤖'
                    )}
                    生成智能健康建议
                  </button>
                </div>

                {/* AI报告内容 */}
                {aiReport ? (
                  <div 
                    className="prose prose-sm max-w-none bg-gray-50 p-4 rounded-lg"
                    dangerouslySetInnerHTML={{ __html: aiReport }}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-4">🤖</div>
                    <p className="text-lg font-medium mb-2">点击上方按钮生成AI建议</p>
                    <p className="text-sm">基于您的数据，AI将为您提供个性化的健康建议</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 底部留白 */}
          <div className="h-24 pb-safe"></div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <BottomNavigation />
    </div>
  );
};

export default ReportPage;
