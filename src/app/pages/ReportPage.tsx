import React, { useState, useMemo } from 'react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Cell,
  BarChart,
  Bar
} from 'recharts';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useExerciseStore } from '@/domains/exercise/stores/exerciseStore';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { BottomNavigation } from '@/shared/components/navigation';
import { formatDate } from '@/shared/utils';
import { eachDayOfInterval, subDays } from 'date-fns';
import { AIService } from '@/infrastructure/ai/AIService';

type TimePeriod = 'week' | 'month';

const ReportPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('week');
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [aiReport, setAiReport] = useState<string>('');
  
  const { getDailySummary } = useNutritionStore();
  const { getDailyExerciseRecords } = useExerciseStore();
  const { getActiveModel } = useAIModelStore();

  // 获取时间范围数据
  const reportData = useMemo(() => {
    const today = new Date();
    let dateRange: Date[] = [];

    switch (selectedPeriod) {
      case 'week':
        // 获取过去7天
        dateRange = eachDayOfInterval({
          start: subDays(today, 6),
          end: today
        });
        break;
      case 'month':
        // 获取过去30天
        dateRange = eachDayOfInterval({
          start: subDays(today, 29),
          end: today
        });
        break;

    }

    // {{ AURA-X: Modify - 简化营养趋势数据，只支持周/月视图. Approval: 寸止(ID:1737100000). }}
    // 营养趋势数据 - 显示每日数据
    const nutritionTrends = dateRange.map(date => {
      const summary = getDailySummary(date);
      return {
        date: formatDate(date, selectedPeriod === 'month' ? 'MM-dd' : 'MM/dd'),
        calories: Math.round(summary?.totalCalories || 0),
        protein: Math.round(summary?.nutrition?.protein || 0),
        carbs: Math.round(summary?.nutrition?.carbs || 0),
        fat: Math.round(summary?.nutrition?.fat || 0),
        target: summary?.calorieLimit || 2000
      };
    });



    // {{ AURA-X: Modify - 简化运动数据，只支持周/月视图. Approval: 寸止(ID:1737100000). }}
    // 运动数据 - 显示每日数据
    const exerciseData = dateRange.map(date => {
      const dailyExercise = getDailyExerciseRecords(date);
      return {
        date: formatDate(date, selectedPeriod === 'month' ? 'MM-dd' : 'MM/dd'),
        calories: Math.round(dailyExercise.totalCaloriesBurned),
        duration: Math.round(dailyExercise.totalDuration)
      };
    });

    // {{ AURA-X: Modify - 简化卡路里净值分析，只支持周/月视图. Approval: 寸止(ID:1737100000). }}
    // 卡路里净值分析数据（摄入-消耗）- 显示每日数据
    const calorieNetData = dateRange.map(date => {
      const summary = getDailySummary(date);
      const dailyExercise = getDailyExerciseRecords(date);
      const intake = summary?.totalCalories || 0;
      const burned = dailyExercise.totalCaloriesBurned;
      const net = intake - burned;

      return {
        date: formatDate(date, selectedPeriod === 'month' ? 'MM-dd' : 'MM/dd'),
        intake: Math.round(intake),
        burned: Math.round(burned),
        net: Math.round(net),
        status: net > 0 ? 'surplus' : net < 0 ? 'deficit' : 'balanced'
      };
    });



    return {
      nutritionTrends,
      exerciseData,
      calorieNetData
    };
  }, [selectedPeriod, getDailySummary, getDailyExerciseRecords]);

  // 生成综合AI健康建议报告
  const generateComprehensiveReport = async () => {
    const activeModel = getActiveModel();
    if (!activeModel) {
      setAiReport('<p style="color: red;">请先配置AI模型</p>');
      return;
    }

    setIsGeneratingReport(true);

    try {
      const periodText = selectedPeriod === 'week' ? '本周' : '本月';

      // 收集营养数据
      const nutritionSummary = {
        totalCalories: reportData.nutritionTrends.reduce((sum, day) => sum + day.calories, 0),
        avgCalories: reportData.nutritionTrends.reduce((sum, day) => sum + day.calories, 0) / reportData.nutritionTrends.length,
        totalProtein: reportData.nutritionTrends.reduce((sum, day) => sum + day.protein, 0),
        totalCarbs: reportData.nutritionTrends.reduce((sum, day) => sum + day.carbs, 0),
        totalFat: reportData.nutritionTrends.reduce((sum, day) => sum + day.fat, 0),
        mealDistribution: null
      };

      // 收集运动数据
      const exerciseSummary = {
        totalCaloriesBurned: reportData.exerciseData.reduce((sum, day) => sum + day.calories, 0),
        totalDuration: reportData.exerciseData.reduce((sum, day) => sum + day.duration, 0),
        avgDailyBurn: reportData.exerciseData.reduce((sum, day) => sum + day.calories, 0) / reportData.exerciseData.length,
        activeDays: reportData.exerciseData.filter(day => day.calories > 0).length
      };

      // 构建综合分析提示词
      const prompt = `
作为专业的营养师和健身教练，请基于以下${periodText}的完整健康数据，生成一份综合的健康建议报告：

**营养摄入数据：**
- 总卡路里摄入：${nutritionSummary.totalCalories}卡
- 平均每日卡路里：${Math.round(nutritionSummary.avgCalories)}卡
- 总蛋白质：${nutritionSummary.totalProtein}g
- 总碳水化合物：${nutritionSummary.totalCarbs}g
- 总脂肪：${nutritionSummary.totalFat}g
- 营养摄入均衡分析

**运动数据：**
- 总消耗卡路里：${exerciseSummary.totalCaloriesBurned}卡
- 总运动时长：${exerciseSummary.totalDuration}分钟
- 平均每日消耗：${Math.round(exerciseSummary.avgDailyBurn)}卡
- 活跃天数：${exerciseSummary.activeDays}天

请提供以下方面的专业建议，使用HTML格式返回：
1. 整体健康状况评估
2. 营养摄入优化建议
3. 运动计划调整建议
4. 减肥/维重策略
5. 下阶段目标建议

要求：使用HTML标签格式化，包含标题、列表、强调等元素，内容专业且实用。
`;

      // 使用真实的AI服务调用
      const aiService = AIService.getInstance();
      const aiResponse = await aiService.analyzeNutritionAdvice(prompt);
      const reportContent = aiResponse.advice;

      setAiReport(reportContent || `
        <h3>🏥 ${periodText}综合健康建议报告</h3>
        <p><strong>数据分析：</strong>基于您${periodText}的数据，平均每日摄入${Math.round(nutritionSummary.avgCalories)}卡路里，运动消耗${Math.round(exerciseSummary.avgDailyBurn)}卡路里。</p>
        <p><em>AI分析暂时不可用，请检查AI模型配置。</em></p>
      `);
    } catch (error) {
      console.error('生成AI报告失败:', error);
      const errorMessage = error instanceof Error ? error.message : '生成报告失败，请稍后重试';
      setAiReport(`<p style="color: red;">❌ ${errorMessage}</p>`);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  return (
    <div className="relative">
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 p-2 sm:p-4 lg:p-6">
        <div className="container mx-auto max-w-7xl">
          {/* 页面标题 */}
          <div className="text-center mb-6 sm:mb-8">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">
              数据报告
            </h1>
            <p className="text-sm sm:text-base text-gray-600 lg:text-lg px-4">
              查看您的营养、运动数据分析和AI智能建议
            </p>
          </div>

          {/* 时间筛选器 */}
          <div className="flex justify-center mb-6 sm:mb-8">
            <div className="flex gap-2 sm:gap-3">
              {(['week', 'month'] as TimePeriod[]).map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`btn min-h-[44px] min-w-[70px] sm:min-w-[80px] text-sm sm:text-base ${selectedPeriod === period ? 'btn-primary' : 'btn-outline'}`}
                >
                  {period === 'week' ? '本周' : '本月'}
                </button>
              ))}
            </div>
          </div>

          {/* 图表区域 */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8">
            {/* 营养趋势图 */}
            <div className="card bg-white/90 backdrop-blur-md shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-300">
              <div className="card-body p-3 sm:p-4 lg:p-6">
                <h2 className="card-title text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                  🍽️ 营养摄入趋势
                </h2>
                <div className="h-80 sm:h-96 lg:h-[28rem] xl:h-[32rem]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={reportData.nutritionTrends}>
                      <defs>
                        <linearGradient id="caloriesGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                        </linearGradient>
                        <linearGradient id="targetGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" opacity={0.6} />
                      <XAxis
                        dataKey="date"
                        stroke="#64748b"
                        fontSize={10}
                        tick={{ fill: '#64748b', fontSize: 10 }}
                        interval="preserveStartEnd"
                      />
                      <YAxis
                        stroke="#64748b"
                        fontSize={10}
                        tick={{ fill: '#64748b', fontSize: 10 }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                          backdropFilter: 'blur(8px)'
                        }}
                        labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                      />
                      <Line
                        type="monotone"
                        dataKey="calories"
                        stroke="url(#caloriesGradient)"
                        strokeWidth={3}
                        name="卡路里"
                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2, fill: '#ffffff' }}
                        animationDuration={1500}
                      />
                      <Line
                        type="monotone"
                        dataKey="target"
                        stroke="url(#targetGradient)"
                        strokeWidth={2}
                        strokeDasharray="8 4"
                        name="目标"
                        dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
                        animationDuration={1500}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* 运动数据柱状图 */}
            <div className="grid grid-cols-1 gap-4 sm:gap-6 lg:gap-8">


              {/* 运动数据柱状图 */}
              <div className="card bg-white/90 backdrop-blur-md shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-300">
                <div className="card-body p-3 sm:p-4 lg:p-6">
                  <h2 className="card-title text-lg font-bold bg-gradient-to-r from-green-500 to-teal-500 bg-clip-text text-transparent mb-4">
                    🏃‍♂️ 运动消耗统计
                  </h2>
                  <div className="h-80 sm:h-96 lg:h-[28rem] xl:h-[32rem]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={reportData.exerciseData} margin={{ top: 20, right: 5, left: 5, bottom: 5 }}>
                        <defs>
                          <linearGradient id="exerciseGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.9}/>
                            <stop offset="50%" stopColor="#f97316" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#ea580c" stopOpacity={0.7}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" opacity={0.6} />
                        <XAxis
                          dataKey="date"
                          stroke="#64748b"
                          fontSize={12}
                          tick={{ fill: '#64748b' }}
                        />
                        <YAxis
                          stroke="#64748b"
                          fontSize={12}
                          tick={{ fill: '#64748b' }}
                          width={50}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: 'none',
                            borderRadius: '12px',
                            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                            backdropFilter: 'blur(8px)'
                          }}
                          labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                        />
                        <Bar
                          dataKey="calories"
                          fill="url(#exerciseGradient)"
                          name="消耗卡路里"
                          radius={[6, 6, 0, 0]}
                          animationDuration={1200}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>

            {/* 减肥数据维度分析 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
              {/* 卡路里净值分析 */}
              <div className="card bg-white/90 backdrop-blur-md shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-300">
                <div className="card-body p-3 sm:p-4 lg:p-6">
                  <h2 className="card-title text-lg font-bold bg-gradient-to-r from-purple-500 to-indigo-500 bg-clip-text text-transparent mb-4">
                    ⚖️ 卡路里净值分析
                  </h2>
                  <div className="h-80 sm:h-96 lg:h-[28rem] xl:h-[32rem]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={reportData.calorieNetData} margin={{ top: 20, right: 5, left: 5, bottom: 5 }}>
                        <defs>
                          <linearGradient id="surplusGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ef4444" stopOpacity={0.9}/>
                            <stop offset="95%" stopColor="#ef4444" stopOpacity={0.6}/>
                          </linearGradient>
                          <linearGradient id="deficitGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.9}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0.6}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" opacity={0.6} />
                        <XAxis
                          dataKey="date"
                          stroke="#64748b"
                          fontSize={12}
                          tick={{ fill: '#64748b' }}
                        />
                        <YAxis
                          stroke="#64748b"
                          fontSize={12}
                          tick={{ fill: '#64748b' }}
                          width={50}
                        />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: 'none',
                            borderRadius: '12px',
                            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                            backdropFilter: 'blur(8px)'
                          }}
                          formatter={(value, name) => {
                            const numValue = Number(value);
                            if (name === 'net') {
                              return [`${numValue > 0 ? '+' : ''}${numValue}卡`, numValue > 0 ? '热量盈余' : '热量缺口'];
                            }
                            return [`${numValue}卡`, name === 'intake' ? '摄入' : '消耗'];
                          }}
                          labelStyle={{ color: '#374151', fontWeight: 'bold' }}
                        />
                        <Bar
                          dataKey="net"
                          name="net"
                          radius={[6, 6, 0, 0]}
                          animationDuration={1200}
                        >
                          {reportData.calorieNetData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.net > 0 ? 'url(#surplusGradient)' : 'url(#deficitGradient)'}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>


            </div>

            {/* AI建议报告区域 */}
            <div className="card bg-white/90 backdrop-blur-md shadow-2xl border border-white/20 hover:shadow-3xl transition-all duration-300">
              <div className="card-body p-4 sm:p-6">
                <h2 className="card-title text-base sm:text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
                  🤖 AI智能建议
                </h2>
                
                {/* AI综合建议生成 */}
                <div className="flex flex-col sm:flex-row justify-center items-center gap-3 mb-6">
                  <button
                    onClick={generateComprehensiveReport}
                    disabled={isGeneratingReport}
                    className="btn btn-primary w-full sm:w-auto min-h-[44px] gap-2 text-sm sm:text-base"
                  >
                    {isGeneratingReport ? (
                      <>
                        <span className="loading loading-spinner loading-sm"></span>
                        <span className="hidden sm:inline">正在分析数据...</span>
                        <span className="sm:hidden">分析中...</span>
                      </>
                    ) : (
                      <>
                        <span className="text-lg">🤖</span>
                        <span>生成智能健康建议</span>
                      </>
                    )}
                  </button>

                  {aiReport && (
                    <div className="flex gap-2">
                      <button
                        onClick={() => navigator.clipboard.writeText(aiReport.replace(/<[^>]*>/g, ''))}
                        className="btn btn-outline btn-sm min-h-[44px] gap-1"
                        title="复制建议内容"
                      >
                        📋 <span className="hidden sm:inline">复制</span>
                      </button>
                      <button
                        onClick={() => setAiReport('')}
                        className="btn btn-outline btn-sm min-h-[44px] gap-1"
                        title="清除建议"
                      >
                        🗑️ <span className="hidden sm:inline">清除</span>
                      </button>
                    </div>
                  )}
                </div>

                {/* AI报告内容 */}
                {aiReport ? (
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-100">
                    <div
                      className="prose prose-sm sm:prose-base max-w-none text-gray-800 leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: aiReport }}
                      style={{
                        fontSize: 'clamp(14px, 2.5vw, 16px)',
                        lineHeight: '1.6'
                      }}
                    />
                  </div>
                ) : (
                  <div className="text-center py-8 sm:py-12 text-gray-500">
                    <div className="text-4xl sm:text-5xl mb-4">🤖</div>
                    <p className="text-base sm:text-lg font-medium mb-2">点击按钮生成AI建议</p>
                    <p className="text-sm sm:text-base px-4">基于您的数据，AI将为您提供个性化的健康建议</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 底部留白 */}
          <div className="h-24 pb-safe"></div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <BottomNavigation />
    </div>
  );
};

export default ReportPage;
