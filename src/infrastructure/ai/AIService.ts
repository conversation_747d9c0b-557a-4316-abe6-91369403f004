/**
 * 统一AI服务管理器
 * 根据用户选择的AI模型自动使用对应的提供商
 */

import { aiProviderManager } from './providers/AIProviderManager';
import { AIImageRecognitionResult, AITextAnalysisResult } from './providers/AIProvider';
import { useAIModelStore } from '../../domains/ai/stores/aiModelStore';

export class AIService {
  private static instance: AIService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * 初始化当前活跃的AI提供商
   */
  private initializeCurrentProvider(): void {
    const { getActiveModel } = useAIModelStore.getState();
    const activeModel = getActiveModel();

    if (!activeModel) {
      throw new Error('未配置活跃的AI模型，请先在设置中配置AI模型');
    }

    // 设置当前提供商
    aiProviderManager.setProvider(activeModel.provider, {
      apiKey: activeModel.apiKey,
      baseUrl: activeModel.baseUrl,
      modelName: activeModel.modelName
    });
  }

  /**
   * 图片食物识别
   */
  async recognizeFood(files: File[], additionalContext?: string, userContext?: any): Promise<AIImageRecognitionResult> {
    this.initializeCurrentProvider();
    
    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.recognizeFood(files, additionalContext, userContext);
  }

  /**
   * 文本食物分析
   */
  async analyzeText(text: string, additionalContext?: string): Promise<AITextAnalysisResult> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeText(text, additionalContext);
  }

  /**
   * 运动文字分析
   */
  async analyzeExerciseText(text: string, additionalContext?: string, userContext?: any): Promise<any> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeExerciseText(text, additionalContext, userContext);
  }

  /**
   * 运动图像识别
   */
  async recognizeExercise(files: File[], additionalContext?: string, userContext?: any): Promise<any> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.recognizeExercise(files, additionalContext, userContext);
  }

  /**
   * 生成营养建议
   */
  async generateNutritionAdvice(foods: any[], userContext?: any): Promise<string> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.generateNutritionAdvice(foods, userContext);
  }

  /**
   * 生成运动建议
   */
  async generateExerciseRecommendations(exercises: any[], userContext?: any): Promise<string> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.generateExerciseRecommendations(exercises, userContext);
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    this.initializeCurrentProvider();
    
    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeNutritionAdvice(prompt);
  }

  /**
   * 获取当前使用的AI模型信息
   */
  getCurrentModelInfo(): { provider: string; modelName: string } | null {
    const { getActiveModel } = useAIModelStore.getState();
    const activeModel = getActiveModel();

    if (!activeModel) {
      return null;
    }

    return {
      provider: activeModel.provider,
      modelName: activeModel.modelName
    };
  }

  /**
   * 生成个性化运动建议
   */
  async generatePersonalizedExerciseAdvice(analysisData: any, signal?: AbortSignal): Promise<string> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    // 构建专门的运动建议提示词
    const prompt = this.buildExerciseAdvicePrompt(analysisData);

    try {
      // 使用文本生成方法而不是analyzeText
      const result = await provider.generateText(prompt, signal);
      return result || '暂无运动建议';
    } catch (error) {
      console.error('生成运动建议失败:', error);
      if (signal?.aborted) {
        throw new DOMException('运动建议分析已取消', 'AbortError');
      }
      throw new Error('生成运动建议失败，请稍后重试');
    }
  }

  /**
   * 构建运动建议提示词
   */
  private buildExerciseAdvicePrompt(analysisData: any): string {
    const { personalInfo, exerciseRecords, nutritionData } = analysisData;

    return `作为专业的健身教练和营养师，请基于以下数据为用户制定个性化的减肥运动建议：

**个人信息：**
- 年龄：${personalInfo.age}岁
- 性别：${personalInfo.gender === 'male' ? '男性' : '女性'}
- 身高：${personalInfo.height}cm
- 体重：${personalInfo.weight}kg
- BMI：${personalInfo.bmi.toFixed(1)}
- 基础代谢率：${personalInfo.bmr}kcal/天
- 总消耗：${personalInfo.tdee}kcal/天
- 活动水平：${personalInfo.activityLevel}

**今日运动记录：**
${exerciseRecords ? `
- 运动次数：${exerciseRecords.recordCount}次
- 总时长：${exerciseRecords.totalDuration}分钟
- 消耗卡路里：${exerciseRecords.totalCaloriesBurned}kcal
- 运动详情：${exerciseRecords.exercises.map(ex => `${ex.name}(${ex.intensity}强度, ${ex.duration}分钟, ${ex.caloriesBurned}kcal)`).join(', ')}
` : '- 今日暂无运动记录'}

**今日营养数据：**
- 摄入卡路里：${nutritionData.totalCalories}kcal
- 目标卡路里：${nutritionData.calorieLimit}kcal
- 净卡路里：${nutritionData.netCalories}kcal (摄入-消耗)
- 蛋白质：${nutritionData.nutrition.protein}g
- 脂肪：${nutritionData.nutrition.fat}g
- 碳水化合物：${nutritionData.nutrition.carbs}g

请提供简洁实用的个性化运动建议，控制在3-5个核心要点：

要求：
- 直接给出最重要的运动建议，无需详细解释
- 重点关注：推荐运动类型、建议时长、注意事项
- 建议要科学合理，适合减肥目标
- 返回HTML格式，使用<ul>、<li>、<strong>等标签
- 内容精练，每个要点1-2句话即可
- 不要包含问候语和冗余说明`;
  }

  /**
   * 检查是否有可用的AI模型
   */
  hasActiveModel(): boolean {
    const { getActiveModel } = useAIModelStore.getState();
    return getActiveModel() !== null;
  }
}

// 创建全局实例
export const aiService = AIService.getInstance();

// {{ AURA-X: Add - 创建统一AI服务管理器. Approval: 寸止(ID:1736938200). }}
