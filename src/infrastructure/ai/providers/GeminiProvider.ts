/**
 * Gemini AI提供商实现
 */

import { AI<PERSON>rovider, AIProviderConfig, AIImageRecognitionResult, AITextAnalysisResult, AIValidationResult, AIExerciseRecognitionResult } from './AIProvider';
import { promptManager } from '../prompts/promptManager';

export class GeminiProvider implements AIProvider {
  public readonly name = 'gemini';
  
  private apiKey: string = '';
  private baseUrl: string = '';
  private modelName: string = '';
  private timeout: number = 120000; // 延长到2分钟

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.modelName = config.modelName;
    this.timeout = config.timeout || 120000; // 默认2分钟超时
  }

  /**
   * 图片识别
   */
  async recognizeFood(files: File[], additionalContext?: string, _userContext?: any): Promise<AIImageRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // 使用统一的图片识别方法
      const response = await this.sendImageRequest(base64Images, additionalContext, _userContext);

      // 解析响应
      return this.parseImageResponse(response);
    } catch (error) {
      console.error('Gemini食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini食物识别失败');
    }
  }

  /**
   * 文本分析
   */
  async analyzeText(text: string): Promise<AITextAnalysisResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const response = await this.sendTextRequest(text);
      return this.parseTextResponse(response);
    } catch (error) {
      console.error('Gemini文本分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini文本分析失败');
    }
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const response = await this.sendAdviceRequest(prompt);
      return this.parseAdviceResponse(response);
    } catch (error) {
      console.error('Gemini营养建议分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini营养建议分析失败');
    }
  }

  /**
   * 验证配置
   */
  async validateConfig(config: AIProviderConfig): Promise<AIValidationResult> {
    try {
      // {{ AURA-X: Modify - 使用与generateText相同的API调用格式验证配置. Approval: 寸止(ID:**********). }}
      // 使用正常的generateContent端点进行验证测试
      const response = await fetch(`${config.baseUrl}/v1beta/models/${config.modelName}:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': config.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              role: 'user',
              parts: [
                { text: 'Hello' }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            candidateCount: 1,
            maxOutputTokens: 10
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      // 检查Gemini API的标准响应格式
      if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      return {
        isValid: true,
        modelList: [config.modelName] // 验证成功则返回当前模型
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '验证失败'
      };
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(config: AIProviderConfig): Promise<string[]> {
    const response = await fetch(`${config.baseUrl}/v1beta/models`, {
      headers: {
        'x-goog-api-key': config.apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取Gemini模型列表失败: ${response.status}`);
    }

    const result = await response.json();
    return result.models.map((model: any) => model.name.replace('models/', ''));
  }

  /**
   * 将文件转换为base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 发送图片识别请求
   */
  private async sendImageRequest(base64Images: string[], additionalContext?: string, userContext?: any): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // {{ AURA-X: Modify - 使用迁移的增强版提示词. Approval: 寸止(ID:1737098100). }}
      // 根据图片数量选择合适的提示词
      const isMultiImage = base64Images.length > 1;
      const promptId = isMultiImage ? 'multi_image_food_recognition' : 'single_image_food_recognition';
      const prompt = promptManager.renderPrompt(promptId, {
        imageCount: base64Images.length,
        additionalContext: additionalContext || '',
        userContext: userContext || null
      });

      // 构建图片parts
      const imageParts = base64Images.map(base64 => ({
        inline_data: {
          mime_type: 'image/jpeg',
          data: base64
        }
      }));

      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt },
                ...imageParts
              ]
            }
          ],
          generationConfig: {
          temperature: 0.05,  // 降低温度以提高JSON格式稳定性
          candidateCount: 1,  // 只生成一个候选结果
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送文本分析请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const prompt = promptManager.renderPrompt('text_food_analysis', { text });

      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ],
          generationConfig: {
            // {{ AURA-X: Modify - 统一文字识别参数配置. Approval: 寸止(ID:1737098200). }}
            temperature: 0.05, // 保持文字识别的创造性和准确性平衡
            candidateCount: 1,  // 只生成一个候选结果
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送营养建议请求
   */
  private async sendAdviceRequest(prompt: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 解析图片识别响应
   */
  private parseImageResponse(response: any): AIImageRecognitionResult {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const content = response.candidates[0].content.parts[0].text;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AIImageRecognitionResult;
    } catch (error) {
      console.error('解析图片识别响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析文本分析响应
   */
  private parseTextResponse(response: any): AITextAnalysisResult {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const content = response.candidates[0].content.parts[0].text;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AITextAnalysisResult;
    } catch (error) {
      console.error('解析文本分析响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析营养建议响应
   */
  private parseAdviceResponse(response: any): { advice: string; rawResponse?: any } {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const advice = response.candidates[0].content.parts[0].text;

      return {
        advice: advice.trim(),
        rawResponse: response
      };
    } catch (error) {
      console.error('解析营养建议响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 运动文字分析
   */
  async analyzeExerciseText(text: string, additionalContext?: string, userContext?: any): Promise<AIExerciseRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const prompt = promptManager.getExerciseTextPrompt(text, additionalContext, userContext);
      const response = await this.sendTextRequest(prompt);
      return this.parseExerciseResponse(response);
    } catch (error) {
      console.error('Gemini运动文字分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini运动文字分析失败');
    }
  }

  /**
   * 运动图像识别
   */
  async recognizeExercise(files: File[], additionalContext?: string, userContext?: any): Promise<AIExerciseRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      const prompt = promptManager.getExerciseImagePrompt(additionalContext, userContext);
      const response = await this.sendImageRequest(base64Images, prompt, userContext);
      return this.parseExerciseResponse(response);
    } catch (error) {
      console.error('Gemini运动图像识别失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini运动图像识别失败');
    }
  }

  /**
   * 生成营养建议
   */
  async generateNutritionAdvice(foods: any[], userContext?: any): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const prompt = promptManager.getNutritionAdvicePrompt(foods, userContext);
      const response = await this.sendTextRequest(prompt);
      const result = this.parseAdviceResponse(response);
      return result.advice;
    } catch (error) {
      console.error('Gemini营养建议生成失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini营养建议生成失败');
    }
  }

  /**
   * 生成运动建议
   */
  async generateExerciseRecommendations(exercises: any[], userContext?: any): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const prompt = promptManager.getExerciseAdvicePrompt(exercises, userContext);
      const response = await this.sendTextRequest(prompt);
      const result = this.parseAdviceResponse(response);
      return result.advice;
    } catch (error) {
      console.error('Gemini运动建议生成失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini运动建议生成失败');
    }
  }

  /**
   * 解析运动识别响应
   */
  private parseExerciseResponse(response: any): AIExerciseRecognitionResult {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const content = response.candidates[0].content.parts[0].text;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.exercises || !Array.isArray(result.exercises)) {
        throw new Error('响应中缺少exercises字段或格式不正确');
      }

      return result as AIExerciseRecognitionResult;
    } catch (error) {
      console.error('解析运动识别响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 生成文本内容
   */
  async generateText(prompt: string, signal?: AbortSignal): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    // 如果传入了外部signal，监听它的abort事件
    if (signal) {
      signal.addEventListener('abort', () => controller.abort());
    }

    try {
      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      // {{ AURA-X: Modify - 添加HTML格式清理，移除代码块标记. Approval: 寸止(ID:**********). }}
      const rawText = result.candidates[0].content.parts[0].text || '';

      // 清理HTML格式中的代码块标记
      const cleanedText = rawText
        .replace(/```html\s*/gi, '')  // 移除```html开始标记
        .replace(/```\s*/g, '')       // 移除```结束标记
        .trim();

      return cleanedText;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }
}

// {{ AURA-X: Add - 添加响应解析方法. Approval: 寸止(ID:1736938000). }}
