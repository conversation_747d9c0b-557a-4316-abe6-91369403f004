/**
 * AI提示词统一管理系统
 * 支持不同场景的提示词配置和管理
 */

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables?: string[];
  category: PromptCategory;
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

export type PromptCategory = 
  | 'food_recognition'
  | 'nutrition_analysis' 
  | 'text_analysis'
  | 'health_advice'
  | 'exercise_suggestion';

export interface PromptContext {
  additionalContext?: string;
  userContext?: any;
  imageCount?: number;
  method?: 'image' | 'text';
  [key: string]: any;
}

/**
 * 提示词管理器类
 */
export class PromptManager {
  private static instance: PromptManager;
  private prompts: Map<string, PromptTemplate> = new Map();

  private constructor() {
    this.initializeDefaultPrompts();
  }

  public static getInstance(): PromptManager {
    if (!PromptManager.instance) {
      PromptManager.instance = new PromptManager();
    }
    return PromptManager.instance;
  }

  /**
   * 初始化默认提示词
   */
  private initializeDefaultPrompts(): void {
    // 统一图片食物识别提示词（支持单图片和多图片）
    this.prompts.set('unified_image_food_recognition', {
      id: 'unified_image_food_recognition',
      name: '统一图片食物识别',
      description: '用于识别单张或多张图片中的食物信息',
      category: 'food_recognition',
      version: '3.0.0',
      template: `你是一个专业的营养师和食物识别专家。请仔细分析{{imageCount}}张食物图片，识别其中的所有食物并提供详细的营养信息。

**核心任务：**
1. **精确识别**：识别图片中的所有食物，包括主食、配菜、调料等
2. **重量/体积估算**：基于视觉线索（餐具大小、食物比例等）估算每种食物的重量(克)或体积(毫升)
3. **营养分析**：提供准确的营养成分数据，优先使用营养标签信息
4. **数据来源标注**：明确标注营养数据的来源（营养标签/视觉估算）
5. **单位适配**：根据食物类型选择合适的计量单位（固体用克，液体用毫升，个数用个）
{{#if multiImage}}6. **跨图片分析**：分析所有图片，识别不同角度或状态的同一食物，避免重复计算{{/if}}
// {{ AURA-X: Modify - 增强食物单位支持，包含毫升、个数等多种单位. Approval: 寸止(ID:1737099400). }}

**输出格式要求：**
1.内容上必须是中文的语言
2.必须严格返回纯JSON数据，禁止包含任何推理过程、解释文字、markdown标记或其他内容：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      {{#if multiImage}}"imageIndex": 主要出现的图片索引(0开始),{{/if}}
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "confidence": 置信度(0-1),
        "readableText": "标签文字内容"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low",
        "unitReasoning": "单位选择的理由"
      }
    }
  ],
  // {{ AURA-X: Modify - 增加unit和unitValue字段支持多种食物单位. Approval: 寸止(ID:1737099400). }}
  "analysisMetadata": {
    "hasNutritionLabel": true/false,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "visual_estimation",
    "processingNotes": "处理说明"
  }{{#if multiImage}},
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "duplicatesFound": 去重数量,
    "crossReferenceNotes": "跨图片分析说明"
  }{{/if}}
}

{{additionalContext}}`,
      variables: ['imageCount', 'multiImage', 'additionalContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // {{ AURA-X: Add - 创建统一图片识别提示词模板. Approval: 寸止(ID:1736937900). }}

    // {{ AURA-X: Modify - 迁移geminiService.ts中的增强版单图片识别提示词. Approval: 寸止(ID:1737098100). }}
    // 单图片食物识别提示词（从geminiService.ts迁移的增强版）
    this.prompts.set('single_image_food_recognition', {
      id: 'single_image_food_recognition',
      name: '增强版单图片食物识别',
      description: '从geminiService.ts迁移的高质量单图片食物识别提示词',
      category: 'food_recognition',
      version: '3.0.0',
      template: `**系统角色：** 你是一位专业的营养师和食物识别专家，具备精确的营养成分分析能力和OCR文字识别技能。

**核心任务：** 分析图片中的食物并提供准确的营养信息。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

**分析优先级（按重要性排序）：**

1. **营养标签优先原则**
   - 首先仔细扫描图片中的营养成分表、营养标签、包装信息
   - 如发现营养标签，请精确读取以下信息：
     * 每份重量/体积
     * 每份卡路里
     * 蛋白质、脂肪、碳水化合物含量
     * 纤维、糖分、钠含量
     * 标注数据来源为 "nutrition_label"
     * 置信度设为 0.9-1.0（基于标签清晰度）

2. **视觉食物识别**
   - 识别所有可见的食物项目
   - 估算每种食物的重量（克）
   - 基于标准营养数据库计算营养成分
   - 标注数据来源为 "visual_estimation"
   - 置信度设为 0.6-0.8（基于识别清晰度）

3. **用户上下文整合**
{{#if additionalContext}}   - 用户提供的额外信息：{{additionalContext}}
   - 将此信息作为重要参考，用于修正识别结果
   - 如用户描述与视觉不符，优先考虑用户描述{{else}}   - 无额外用户信息{{/if}}

**详细分析要求：**

• **食物识别精度**：准确识别每种食物的名称、品牌（如有）
• **重量/体积估算**：基于视觉线索（餐具、包装、手部比例）估算重量(克)或体积(毫升)，根据食物类型选择合适单位
• **营养计算**：使用标准营养数据库进行精确计算
• **置信度评估**：真实反映识别的准确性和可靠性
• **数据来源标注**：明确区分营养标签数据和视觉估算数据

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "brand": "品牌名称（如有）",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "confidence": 置信度(0-1),
        "readableText": "营养标签文字内容",
        "servingSize": "每份重量/体积"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体（餐具、手部等）",
        "confidenceLevel": "high" | "medium" | "low",
        "visualCues": "视觉线索描述",
        "unitReasoning": "单位选择的理由"
      }
    }
    // {{ AURA-X: Modify - 单图片识别也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "analysisMetadata": {
    "hasNutritionLabel": 布尔值,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "label_priority" | "visual_only" | "hybrid",
    "processingNotes": "分析过程中的重要注意事项"
  }
}

**关键处理规则：**
• **空图片检测**：如图片无食物内容，返回 {"foods": [], "analysisMetadata": {"hasNutritionLabel": false, "imageQuality": "unknown", "recognitionMethod": "none", "processingNotes": "未检测到食物内容"}}
• **营养标签优先**：发现标签时，优先使用标签数据，置信度0.9+
• **视觉估算补充**：无标签时使用视觉估算，置信度0.6-0.8
• **混合模式**：标签+视觉结合时，dataSource设为"hybrid"
• **分量计算**：区分包装总重量和实际食用重量
• **品质控制**：确保所有数值合理，营养成分总和符合卡路里计算

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`,
      variables: ['additionalContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // {{ AURA-X: Modify - 迁移geminiService.ts中的增强版多图片识别提示词. Approval: 寸止(ID:1737098100). }}
    // 多图片食物识别提示词（从geminiService.ts迁移的增强版）
    this.prompts.set('multi_image_food_recognition', {
      id: 'multi_image_food_recognition',
      name: '增强版多图片食物识别',
      description: '从geminiService.ts迁移的高质量多图片食物识别提示词，支持个性化建议',
      category: 'food_recognition',
      version: '3.0.0',
      template: `**系统角色：** 你是一位顶级的营养师和多模态食物识别专家，具备同时分析多张图片并提供综合营养建议的能力。

**核心任务：** 分析这{{imageCount}}张图片中的所有食物，提供准确的营养信息和个性化建议。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

{{#if userContext}}
用户个人信息：
- 年龄：{{userContext.age}}岁
- 性别：{{userContext.gender}}
- 体重：{{userContext.weight}}kg
- 身高：{{userContext.height}}cm
- 目标体重：{{userContext.targetWeight}}kg
- 活动水平：{{userContext.activityLevel}}
- 基础代谢率：{{userContext.bmr}} kcal/天
- 总消耗：{{userContext.tdee}} kcal/天
- 目标天数：{{userContext.targetDays}}天

请根据以上个人信息提供个性化的营养建议和运动建议。
{{/if}}

{{#if additionalContext}}**用户补充信息：** {{additionalContext}}{{/if}}

**多图片分析策略：**

1. **跨图片食物识别**
   - 逐张分析每张图片中的食物
   - 识别重复出现的食物（避免重复计算）
   - 检测食物的不同角度或状态
   - 合并相同食物的营养信息

2. **营养标签优先处理**
   - 优先识别任何图片中的营养标签
   - 将标签信息与对应的食物匹配
   - 使用标签数据校正视觉估算结果

3. **综合营养分析**
   - 计算所有食物的总营养成分
   - 分析营养搭配的合理性
   - 识别营养缺口或过量风险

4. **个性化建议生成**
   - 基于用户的个人数据提供定制建议
   - 计算与目标的差距和改进方向
   - 提供具体的运动消耗建议

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "brand": "品牌名称（如有）",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "imageIndex": 主要出现的图片索引(0开始),
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "confidence": 置信度(0-1),
        "readableText": "营养标签文字内容",
        "servingSize": "每份重量/体积"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low",
        "visualCues": "视觉线索描述",
        "unitReasoning": "单位选择的理由"
      }
    }
    // {{ AURA-X: Modify - 多图片识别也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "personalizedAdvice": "基于用户个人数据的详细个性化营养建议",
  "exerciseAdvice": "针对用户的专属运动消耗建议",
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "foodItemsFound": 识别到的食物项目总数,
    "duplicatesDetected": 检测到的重复食物数量,
    "overallNutritionSummary": {
      "totalCalories": 总卡路里,
      "totalProtein": 总蛋白质,
      "totalFat": 总脂肪,
      "totalCarbs": 总碳水化合物
    }
  }
}

**关键处理要求：**
• **跨图片一致性**：确保相同食物在不同图片中的识别结果一致
• **营养标签优先**：任何图片中的营养标签都应优先使用
• **重复检测**：避免将同一食物在不同图片中重复计算
• **个性化建议**：基于用户信息提供具体的营养和运动建议
• **质量控制**：所有营养数据必须合理且相互一致
• **图片索引**：为每个食物标注来源图片的索引号

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`,
      variables: ['imageCount', 'additionalContext', 'userContext'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // {{ AURA-X: Modify - 迁移geminiService.ts中的增强版文本分析提示词. Approval: 寸止(ID:1737098100). }}
    // 文本食物分析提示词（从geminiService.ts迁移的增强版）
    this.prompts.set('text_food_analysis', {
      id: 'text_food_analysis',
      name: '增强版文本食物分析',
      description: '从geminiService.ts迁移的高质量文本食物分析提示词',
      category: 'text_analysis',
      version: '3.0.0',
      template: `**系统角色：** 你是一位专业的营养师和食物分析专家，具备从文字描述中精确提取食物信息的能力。

**核心任务：** 从用户的文字描述中识别食物并提供准确的营养分析。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

**用户输入文本：** "{{text}}"

**分析策略：**

1. **智能文本解析**
   - 识别所有明确提到的食物名称
   - 提取数量、重量(克)、体积(毫升)、个数等量化信息，选择最合适的单位
   - 识别烹饪方式、调料、配菜等影响营养的因素
   - 理解口语化表达（如"一碗"、"一份"、"半个"等）

2. **上下文推理**
   - 根据描述推断食物的大致分量
   - 考虑常见的食物搭配和用餐习惯
   - 基于烹饪方式调整营养成分（如油炸vs蒸煮）
   - 识别可能的隐含食物（如汉堡包含面包、肉饼、蔬菜等）

3. **营养数据估算**
   - 使用标准营养数据库进行计算
   - 根据烹饪方式调整营养成分
   - 考虑食物组合的营养交互作用
   - 提供合理的营养成分分布

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "text_analysis",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "textAnalysis": {
        "originalText": "原始描述文字",
        "inferredWeight": "重量推理依据",
        "contextClues": "上下文线索",
        "cookingMethod": "烹饪方式",
        "portionReference": "分量参考标准",
        "unitReasoning": "单位选择的理由"
      },
      "ambiguityFactors": {
        "weightUncertainty": "重量不确定性说明",
        "preparationVariance": "制作方式变化影响",
        "portionVariability": "分量变化范围"
      }
    }
    // {{ AURA-X: Modify - 文本分析也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "analysisMetadata": {
    "textQuality": "high" | "medium" | "low",
    "ambiguityLevel": "low" | "medium" | "high",
    "extractedFoodCount": 提取的食物数量,
    "processingNotes": "分析过程中的重要注意事项",
    "linguisticComplexity": "语言复杂度评估",
    "contextualRichness": "上下文丰富度"
  }
}

**关键处理规则：**
• **空文本检测**：如文字无食物信息，返回 {"foods": [], "analysisMetadata": {"textQuality": "low", "ambiguityLevel": "high", "extractedFoodCount": 0, "processingNotes": "未识别到明确的食物信息"}}
• **量化推理**：将模糊描述转换为具体重量（如"一碗米饭"→150g）
• **营养调整**：根据烹饪方式调整营养成分（如炒菜增加油脂）
• **置信度评估**：基于描述的明确程度和常见程度设定置信度
• **上下文理解**：考虑食物搭配和用餐场景的合理性

**输出要求：
1、直接返回JSON，无任何额外文字、标记或解释**
2、内容上必须是中文的语言
`,
      variables: ['text'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // 营养建议提示词
    this.prompts.set('nutrition_advice', {
      id: 'nutrition_advice',
      name: '营养建议分析',
      description: '基于用户数据提供个性化营养建议',
      category: 'nutrition_analysis',
      version: '1.0.0',
      template: `你是一位专业的营养师。请基于用户的个人信息和当前饮食记录，提供个性化的营养建议。

用户信息：
- 年龄：{{age}}岁
- 性别：{{gender}}
- 身高：{{height}}cm
- 体重：{{weight}}kg
- 目标体重：{{targetWeight}}kg
- 活动水平：{{activityLevel}}
- 每日卡路里目标：{{dailyCalorieLimit}}卡

当前饮食记录：
{{nutritionData}}

请提供简洁实用的营养建议，包括：
1. 当前饮食评估
2. 营养平衡建议
3. 具体改进措施
4. 健康提醒

建议应该实用、具体、易于执行。`,
      variables: ['age', 'gender', 'height', 'weight', 'targetWeight', 'activityLevel', 'dailyCalorieLimit', 'nutritionData'],
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  /**
   * 添加提示词模板
   */
  public addPrompt(prompt: Omit<PromptTemplate, 'createdAt' | 'updatedAt'>): void {
    const fullPrompt: PromptTemplate = {
      ...prompt,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.prompts.set(prompt.id, fullPrompt);
  }

  /**
   * 获取提示词模板
   */
  public getPrompt(id: string): PromptTemplate | undefined {
    return this.prompts.get(id);
  }

  /**
   * 渲染提示词（替换变量和条件渲染）
   */
  public renderPrompt(id: string, context: PromptContext = {}): string {
    const prompt = this.getPrompt(id);
    if (!prompt) {
      throw new Error(`提示词模板不存在: ${id}`);
    }

    let rendered = prompt.template;

    // 处理条件渲染 {{#if condition}}...{{/if}}
    rendered = rendered.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g, (_, condition, content) => {
      return context[condition] ? content : '';
    });

    // {{ AURA-X: Modify - 增强变量替换支持嵌套对象访问. Approval: 寸止(ID:1737098100). }}
    // 替换变量（支持嵌套对象访问）
    if (prompt.variables) {
      prompt.variables.forEach(variable => {
        const value = context[variable] || '';
        const placeholder = `{{${variable}}}`;
        rendered = rendered.replace(new RegExp(placeholder, 'g'), String(value));
      });
    }

    // 处理嵌套对象访问（如 {{userContext.age}}）
    rendered = rendered.replace(/\{\{(\w+)\.(\w+)\}\}/g, (_, objName, propName) => {
      const obj = context[objName];
      if (obj && typeof obj === 'object' && propName in obj) {
        return String(obj[propName]);
      }
      return '';
    });

    // 处理条件性内容
    if (context.additionalContext) {
      rendered = rendered.replace('{{additionalContext}}', `\n\n**补充信息：**\n${context.additionalContext}`);
    } else {
      rendered = rendered.replace('{{additionalContext}}', '');
    }

    return rendered.trim();
  }
  // {{ AURA-X: Modify - 增强提示词渲染支持条件渲染. Approval: 寸止(ID:1736937900). }}

  /**
   * 获取所有提示词
   */
  public getAllPrompts(): PromptTemplate[] {
    return Array.from(this.prompts.values());
  }

  /**
   * 按类别获取提示词
   */
  public getPromptsByCategory(category: PromptCategory): PromptTemplate[] {
    return this.getAllPrompts().filter(prompt => prompt.category === category);
  }

  /**
   * 更新提示词模板
   */
  public updatePrompt(id: string, updates: Partial<PromptTemplate>): void {
    const existing = this.prompts.get(id);
    if (!existing) {
      throw new Error(`提示词模板不存在: ${id}`);
    }

    const updated: PromptTemplate = {
      ...existing,
      ...updates,
      updatedAt: new Date()
    };

    this.prompts.set(id, updated);
  }

  /**
   * 删除提示词模板
   */
  public deletePrompt(id: string): boolean {
    return this.prompts.delete(id);
  }

  /**
   * 获取运动文字分析提示词
   */
  public getExerciseTextPrompt(text: string, additionalContext?: string, userContext?: any): string {
    const basePrompt = `请分析以下运动描述，并返回严格的JSON格式运动信息。

运动描述：${text}
${additionalContext ? `补充信息：${additionalContext}` : ''}
${userContext ? `用户信息：体重${userContext.weight}kg，身高${userContext.height}cm，年龄${userContext.age}岁，性别${userContext.gender}` : ''}

重要要求：
1. 必须返回有效的JSON格式，不要包含任何其他文字
2. 不要使用markdown代码块标记
3. 所有数值必须是数字类型，不要使用字符串
4. intensity必须是"low"、"medium"或"high"之一
5. type必须是"cardio"、"strength"或"flexibility"之一

请返回以下JSON格式：
{
  "exercises": [
    {
      "name": "运动名称",
      "duration": 30,
      "intensity": "medium",
      "caloriesBurned": 150,
      "type": "cardio",
      "description": "运动描述",
      "confidence": 0.9
    }
  ],
  "totalCalories": 150,
  "totalDuration": 30,
  "recommendations": "<p>运动建议可以使用HTML格式，支持<strong>加粗</strong>、<ul><li>列表项1</li><li>列表项2</li></ul>等标签</p>"
}`;

    return basePrompt;
  }

  /**
   * 获取运动图像识别提示词
   */
  public getExerciseImagePrompt(additionalContext?: string, userContext?: any): string {
    const basePrompt = `请识别图片中的运动内容，并返回严格的JSON格式运动信息。

${additionalContext ? `补充信息：${additionalContext}` : ''}
${userContext ? `用户信息：体重${userContext.weight}kg，身高${userContext.height}cm，年龄${userContext.age}岁，性别${userContext.gender}` : ''}

重要要求：
1. 必须返回有效的JSON格式，不要包含任何其他文字
2. 不要使用markdown代码块标记
3. 所有数值必须是数字类型，不要使用字符串
4. intensity必须是"low"、"medium"或"high"之一
5. type必须是"cardio"、"strength"或"flexibility"之一
6. 根据图片内容估算合理的运动时长和卡路里消耗

请返回以下JSON格式：
{
  "exercises": [
    {
      "name": "运动名称",
      "duration": 30,
      "intensity": "medium",
      "caloriesBurned": 150,
      "type": "cardio",
      "description": "运动描述",
      "confidence": 0.8
    }
  ],
  "totalCalories": 150,
  "totalDuration": 30,
  "recommendations": "<p><strong>运动评估：</strong>[简要评估]</p><p><strong>训练建议：</strong>[具体建议]</p><p><strong>注意事项：</strong>[安全提示]</p><p><strong>进阶方案：</strong>[提升建议]</p>"
}`;

    return basePrompt;
  }

  /**
   * 获取营养建议提示词
   */
  public getNutritionAdvicePrompt(foods: any[], userContext?: any): string {
    const foodList = foods.map(food => `${food.name}: ${food.calories}kcal`).join(', ');

    const basePrompt = `基于以下食物摄入情况，请提供个性化的营养建议：

今日食物：${foodList}
${userContext ? `用户信息：体重${userContext.weight}kg，身高${userContext.height}cm，年龄${userContext.age}岁，性别${userContext.gender}，目标体重${userContext.targetWeight}kg` : ''}

请提供：
1. 营养均衡分析
2. 改善建议
3. 下一餐建议
4. 注意事项

请用简洁明了的中文回复，不要使用Markdown格式。`;

    return basePrompt;
  }

  /**
   * 获取运动建议提示词
   */
  public getExerciseAdvicePrompt(exercises: any[], userContext?: any): string {
    const exerciseList = exercises.map(ex => `${ex.name}: ${ex.duration}分钟, ${ex.calories}kcal`).join(', ');

    const basePrompt = `基于以下运动情况，请提供个性化的运动建议：

今日运动：${exerciseList}
${userContext ? `用户信息：体重${userContext.weight}kg，身高${userContext.height}cm，年龄${userContext.age}岁，性别${userContext.gender}，目标体重${userContext.targetWeight}kg` : ''}

请提供：
1. 运动效果分析
2. 改善建议
3. 下次运动建议
4. 注意事项

请用简洁明了的中文回复，不要使用Markdown格式。`;

    return basePrompt;
  }
}

// 导出单例实例
export const promptManager = PromptManager.getInstance();
