/**
 * 运动记录Hook
 * 支持文字输入和图片识别两种方式记录运动
 */

import { useState, useCallback } from 'react';
import { unifiedRecognitionService } from '../services/UnifiedRecognitionService';
import {
  ExerciseTextRecognitionStrategy,
  ExerciseImageRecognitionStrategy
} from '../strategies/ExerciseRecognitionStrategy';
import { 
  RecognitionInput,
  ExerciseRecognitionResult,
  RecognitionState,
  RecognitionMethod
} from '../types/recognition';
import { useUserStore } from '../../domains/user/stores/userStore';

// 注册运动识别策略
const exerciseTextStrategy = new ExerciseTextRecognitionStrategy();
const exerciseImageStrategy = new ExerciseImageRecognitionStrategy();

unifiedRecognitionService.registerStrategy('exercise', 'text', exerciseTextStrategy);
unifiedRecognitionService.registerStrategy('exercise', 'image', exerciseImageStrategy);

export interface ExerciseRecordingState {
  isProcessing: boolean;
  error: string | null;
  processingStep: string;
  progress: number;
  result: ExerciseRecognitionResult | null;
}

export interface ExerciseRecordingOptions {
  description: string;
  images?: File[];
  method?: RecognitionMethod; // 'auto', 'text', 'image'
  duration?: number; // 手动输入的时长（分钟）
  intensity?: 'low' | 'medium' | 'high'; // 手动输入的强度
}

export function useExerciseRecording() {
  const [state, setState] = useState<ExerciseRecordingState>({
    isProcessing: false,
    error: null,
    processingStep: '',
    progress: 0,
    result: null
  });

  const { profile } = useUserStore();

  // 监听识别服务状态变化
  const handleStateChange = useCallback((recognitionState: RecognitionState) => {
    setState(prev => ({
      ...prev,
      isProcessing: recognitionState.isProcessing,
      error: recognitionState.error,
      processingStep: recognitionState.processingStep,
      progress: recognitionState.progress
    }));
  }, []);

  // 注册状态监听器
  unifiedRecognitionService.addStateListener(handleStateChange);

  // 智能识别方法选择
  const determineRecognitionMethod = useCallback((options: ExerciseRecordingOptions): RecognitionMethod => {
    if (options.method && options.method !== 'auto') {
      return options.method;
    }

    // 自动检测逻辑
    if (options.images && options.images.length > 0) {
      return 'image';
    }

    if (options.description && options.description.trim().length > 0) {
      return 'text';
    }

    return 'text'; // 默认使用文字识别
  }, []);

  // 构建用户上下文
  const buildUserContext = useCallback(() => {
    if (!profile) return undefined;

    return {
      age: profile.age,
      gender: profile.gender,
      weight: profile.weight,
      height: profile.height,
      activityLevel: profile.activityLevel,
      targetWeight: profile.targetWeight,
      bmr: profile.bmr,
      tdee: profile.tdee
    };
  }, [profile]);

  // 开始运动记录
  const startRecording = useCallback(async (options: ExerciseRecordingOptions): Promise<ExerciseRecognitionResult> => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        result: null
      }));

      // 确定识别方法
      const method = determineRecognitionMethod(options);

      // 构建附加上下文
      let additionalContext = '';
      if (options.duration) {
        additionalContext += `运动时长：${options.duration}分钟 `;
      }
      if (options.intensity) {
        additionalContext += `运动强度：${options.intensity} `;
      }

      // 构建识别输入
      const input: RecognitionInput = {
        type: 'exercise',
        method,
        textContent: options.description,
        images: options.images,
        additionalContext: additionalContext.trim(),
        userContext: buildUserContext()
      };

      // 执行识别
      const result = await unifiedRecognitionService.recognize(input) as ExerciseRecognitionResult;

      setState(prev => ({
        ...prev,
        result,
        isProcessing: false
      }));

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '运动记录失败';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isProcessing: false
      }));

      throw error;
    }
  }, [determineRecognitionMethod, buildUserContext]);

  // 手动计算卡路里消耗
  const calculateCalories = useCallback((
    exerciseType: string,
    duration: number,
    intensity: 'low' | 'medium' | 'high'
  ): number => {
    if (!profile?.weight) return 0;

    // 简化的MET值计算
    const metValues = {
      walking: { low: 2.5, medium: 3.5, high: 4.3 },
      running: { low: 6.0, medium: 8.0, high: 11.0 },
      cycling: { low: 4.0, medium: 6.8, high: 10.0 },
      swimming: { low: 4.0, medium: 6.0, high: 8.0 },
      strength: { low: 3.0, medium: 5.0, high: 6.0 },
      default: { low: 3.0, medium: 5.0, high: 7.0 }
    };

    const type = exerciseType.toLowerCase();
    let met = metValues.default[intensity];

    if (type.includes('walk')) met = metValues.walking[intensity];
    else if (type.includes('run')) met = metValues.running[intensity];
    else if (type.includes('cycle') || type.includes('bike')) met = metValues.cycling[intensity];
    else if (type.includes('swim')) met = metValues.swimming[intensity];
    else if (type.includes('weight') || type.includes('strength')) met = metValues.strength[intensity];

    // 卡路里 = MET × 体重(kg) × 时间(小时)
    return Math.round(met * profile.weight * (duration / 60));
  }, [profile]);

  // 创建手动运动记录
  const createManualRecord = useCallback((
    exerciseType: string,
    duration: number,
    intensity: 'low' | 'medium' | 'high',
    description?: string
  ): ExerciseRecognitionResult => {
    const caloriesBurned = calculateCalories(exerciseType, duration, intensity);

    return {
      type: 'exercise',
      method: 'text',
      confidence: 1.0,
      timestamp: new Date().toISOString(),
      processingTime: 0,
      exercises: [{
        name: exerciseType,
        type: 'cardio', // 默认类型，可以根据运动类型智能判断
        duration,
        intensity,
        caloriesBurned,
        confidence: 1.0,
        dataSource: 'manual_input'
      }],
      totalCaloriesBurned: caloriesBurned,
      totalDuration: duration,
      intensityLevel: intensity,
      recommendations: description
    };
  }, [calculateCalories]);

  // 重置状态
  const reset = useCallback(() => {
    setState({
      isProcessing: false,
      error: null,
      processingStep: '',
      progress: 0,
      result: null
    });
  }, []);

  // 清理资源
  const cleanup = useCallback(() => {
    unifiedRecognitionService.removeStateListener(handleStateChange);
  }, [handleStateChange]);

  // 获取可用的识别方法
  const getAvailableMethods = useCallback((): RecognitionMethod[] => {
    return ['auto', 'text', 'image'];
  }, []);

  // 验证输入
  const validateInput = useCallback((options: ExerciseRecordingOptions): { isValid: boolean; error?: string } => {
    const method = determineRecognitionMethod(options);

    if (method === 'image') {
      if (!options.images || options.images.length === 0) {
        return { isValid: false, error: '图像识别需要上传图片' };
      }
    }

    if (method === 'text') {
      if (!options.description || options.description.trim().length === 0) {
        return { isValid: false, error: '文字识别需要输入运动描述' };
      }
    }

    return { isValid: true };
  }, [determineRecognitionMethod]);

  // 获取处理进度信息
  const getProgressInfo = useCallback(() => {
    return {
      isProcessing: state.isProcessing,
      step: state.processingStep,
      progress: state.progress,
      canCancel: state.isProcessing && state.progress < 50 // 50%之前可以取消
    };
  }, [state]);

  return {
    // 状态
    state,
    
    // 方法
    startRecording,
    createManualRecord,
    calculateCalories,
    reset,
    cleanup,
    
    // 工具方法
    getAvailableMethods,
    validateInput,
    getProgressInfo,
    determineRecognitionMethod,
    
    // 便捷属性
    isProcessing: state.isProcessing,
    error: state.error,
    result: state.result,
    progress: state.progress,
    processingStep: state.processingStep
  };
}


