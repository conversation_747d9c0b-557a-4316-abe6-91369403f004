/**
 * 统一食物记录Hook
 * 整合文字和视觉识别功能，支持智能模式切换
 */

import { useState, useCallback } from 'react';
import { unifiedRecognitionService } from '../services/UnifiedRecognitionService';
import {
  FoodTextRecognitionStrategy,
  FoodImageRecognitionStrategy
} from '../strategies/FoodRecognitionStrategy';
import { 
  RecognitionInput,
  FoodRecognitionResult,
  RecognitionState,
  RecognitionMethod
} from '../types/recognition';
import { useUserStore } from '../../domains/user/stores/userStore';
import { MealType } from '../types/nutrition';

// 注册食物识别策略
const foodTextStrategy = new FoodTextRecognitionStrategy();
const foodImageStrategy = new FoodImageRecognitionStrategy();

unifiedRecognitionService.registerStrategy('food', 'text', foodTextStrategy);
unifiedRecognitionService.registerStrategy('food', 'image', foodImageStrategy);

export interface UnifiedFoodRecordingState {
  isProcessing: boolean;
  error: string | null;
  processingStep: string;
  progress: number;
  result: FoodRecognitionResult | null;
}

export interface UnifiedFoodRecordingOptions {
  meal: MealType;
  description: string;
  images?: File[];
  method?: RecognitionMethod; // 'auto', 'text', 'image'
}

export function useUnifiedFoodRecording() {
  const [state, setState] = useState<UnifiedFoodRecordingState>({
    isProcessing: false,
    error: null,
    processingStep: '',
    progress: 0,
    result: null
  });

  const { profile } = useUserStore();

  // 监听识别服务状态变化
  const handleStateChange = useCallback((recognitionState: RecognitionState) => {
    setState(prev => ({
      ...prev,
      isProcessing: recognitionState.isProcessing,
      error: recognitionState.error,
      processingStep: recognitionState.processingStep,
      progress: recognitionState.progress
    }));
  }, []);

  // 注册状态监听器
  unifiedRecognitionService.addStateListener(handleStateChange);

  // 智能识别方法选择
  const determineRecognitionMethod = useCallback((options: UnifiedFoodRecordingOptions): RecognitionMethod => {
    if (options.method && options.method !== 'auto') {
      return options.method;
    }

    // 自动检测逻辑
    if (options.images && options.images.length > 0) {
      return 'image';
    }

    if (options.description && options.description.trim().length > 0) {
      return 'text';
    }

    return 'text'; // 默认使用文字识别
  }, []);

  // 构建用户上下文
  const buildUserContext = useCallback(() => {
    if (!profile) return undefined;

    return {
      age: profile.age,
      gender: profile.gender,
      weight: profile.weight,
      height: profile.height,
      activityLevel: profile.activityLevel,
      targetWeight: profile.targetWeight,
      bmr: profile.bmr,
      tdee: profile.tdee
    };
  }, [profile]);

  // 开始食物记录
  const startRecording = useCallback(async (options: UnifiedFoodRecordingOptions): Promise<FoodRecognitionResult> => {
    try {
      setState(prev => ({
        ...prev,
        isProcessing: true,
        error: null,
        result: null
      }));

      // 确定识别方法
      const method = determineRecognitionMethod(options);

      // 构建识别输入
      const input: RecognitionInput = {
        type: 'food',
        method,
        textContent: options.description,
        images: options.images,
        additionalContext: `餐次：${options.meal}`,
        userContext: buildUserContext()
      };

      // 执行识别
      const result = await unifiedRecognitionService.recognize(input) as FoodRecognitionResult;

      setState(prev => ({
        ...prev,
        result,
        isProcessing: false
      }));

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '食物记录失败';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isProcessing: false
      }));

      throw error;
    }
  }, [determineRecognitionMethod, buildUserContext]);

  // 重置状态
  const reset = useCallback(() => {
    setState({
      isProcessing: false,
      error: null,
      processingStep: '',
      progress: 0,
      result: null
    });
  }, []);

  // 清理资源
  const cleanup = useCallback(() => {
    unifiedRecognitionService.removeStateListener(handleStateChange);
  }, [handleStateChange]);

  // 获取可用的识别方法
  const getAvailableMethods = useCallback((): RecognitionMethod[] => {
    return ['auto', 'text', 'image'];
  }, []);

  // 验证输入
  const validateInput = useCallback((options: UnifiedFoodRecordingOptions): { isValid: boolean; error?: string } => {
    const method = determineRecognitionMethod(options);

    if (method === 'image') {
      if (!options.images || options.images.length === 0) {
        return { isValid: false, error: '图像识别需要上传图片' };
      }
    }

    if (method === 'text') {
      if (!options.description || options.description.trim().length === 0) {
        return { isValid: false, error: '文字识别需要输入描述' };
      }
    }

    return { isValid: true };
  }, [determineRecognitionMethod]);

  // 获取处理进度信息
  const getProgressInfo = useCallback(() => {
    return {
      isProcessing: state.isProcessing,
      step: state.processingStep,
      progress: state.progress,
      canCancel: state.isProcessing && state.progress < 50 // 50%之前可以取消
    };
  }, [state]);

  return {
    // 状态
    state,
    
    // 方法
    startRecording,
    reset,
    cleanup,
    
    // 工具方法
    getAvailableMethods,
    validateInput,
    getProgressInfo,
    determineRecognitionMethod,
    
    // 便捷属性
    isProcessing: state.isProcessing,
    error: state.error,
    result: state.result,
    progress: state.progress,
    processingStep: state.processingStep
  };
}


