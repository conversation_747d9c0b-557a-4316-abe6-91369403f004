import { BaseEntity, MealType } from './common';

// 重新导出MealType以便其他模块使用
export { MealType };

// 食物记录条目
export interface FoodRecord extends BaseEntity {
  id: string;                    // 唯一标识
  name: string;                  // 食物名称
  weight: number;                // 重量(g) - 用于计算
  unit?: string;                 // 显示单位 (g, ml, 个, 份等)
  unitValue?: number;            // 单位对应的数值
  calories: number;              // 卡路里
  mealType: MealType;            // 餐次类型
  recordedAt: Date;              // 记录时间

  // 营养成分
  nutrition: {
    protein: number;             // 蛋白质(g)
    fat: number;                 // 脂肪(g)
    carbs: number;               // 碳水化合物(g)
    fiber: number;               // 膳食纤维(g)
    sugar: number;               // 糖(g)
    sodium: number;              // 钠(mg)
  };

  // AI识别信息
  aiRecognition?: {
    confidence: number;          // 置信度(0-1)
    method: 'image' | 'text';    // 识别方式
    originalInput: string;       // 原始输入
    dataSource?: 'text_analysis' | 'visual_estimation' | 'nutrition_label'; // 数据源类型
    unit?: string;               // AI识别的原始单位
  };

  // 用户编辑标记
  isEdited: boolean;             // 是否被用户编辑过
}

// 每日食物记录
export interface DailyFoodRecords {
  date: string;                  // 日期(YYYY-MM-DD)
  records: FoodRecord[];         // 食物记录列表

  // 按餐次分组
  mealRecords: {
    breakfast: FoodRecord[];
    lunch: FoodRecord[];
    dinner: FoodRecord[];
    snack: FoodRecord[];
  };
}

// 每日营养汇总
export interface DailySummary extends BaseEntity {
  date: Date;                    // 日期
  totalCalories: number;         // 总卡路里
  calorieLimit: number;          // 卡路里限额
  remainingCalories: number;     // 剩余卡路里
  
  // 三餐分布
  mealBreakdown: {
    breakfast: MealSummary;
    lunch: MealSummary;
    dinner: MealSummary;
    snack: MealSummary;
  };
  
  // 营养素汇总
  nutrition: {
    protein: number;             // 蛋白质(g)
    fat: number;                 // 脂肪(g)
    carbs: number;               // 碳水化合物(g)
    fiber: number;               // 膳食纤维(g)
    sugar: number;               // 糖(g)
    sodium: number;              // 钠(mg)
  };
  
  // 状态指标
  status: 'under' | 'normal' | 'over' | 'exceed'; // 摄入状态
  percentage: number;            // 完成百分比
}

// 单餐汇总
export interface MealSummary {
  mealType: MealType;
  calories: number;              // 卡路里
  calorieLimit: number;          // 该餐限额
  foodCount: number;             // 食物数量
  percentage: number;            // 完成百分比
}

// BMR计算参数
export interface BMRCalculationParams {
  weight: number;                // 体重(kg)
  height: number;                // 身高(cm)
  age: number;                   // 年龄
  gender: 'male' | 'female';     // 性别
}

// BMR计算结果
export interface BMRCalculationResult {
  bmr: number;                   // 基础代谢率
  tdee: number;                  // 总日消耗
  dailyCalorieLimit: number;     // 每日卡路里限额
  weightLossRate: number;        // 预期减重速度(kg/week)
  
  // 三餐分配
  mealCalories: {
    breakfast: number;
    lunch: number;
    dinner: number;
  };
}

// 营养分析结果
export interface NutritionAnalysis {
  period: 'daily' | 'weekly' | 'monthly';
  startDate: Date;
  endDate: Date;
  
  // 卡路里分析
  calories: {
    average: number;
    total: number;
    target: number;
    adherenceRate: number;       // 达标率
  };
  
  // 营养素分析
  nutrition: {
    protein: NutrientAnalysis;
    fat: NutrientAnalysis;
    carbs: NutrientAnalysis;
    fiber: NutrientAnalysis;
  };
  
  // 趋势数据
  trends: {
    date: Date;
    calories: number;
    weight?: number;
  }[];
}

// 营养素分析
export interface NutrientAnalysis {
  average: number;
  total: number;
  recommended: number;
  percentage: number;
}