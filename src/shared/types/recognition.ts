/**
 * 统一识别系统类型定义
 * 支持食物记录和运动记录的双重识别模式
 */

// 识别类型枚举
export type RecognitionType = 'food' | 'exercise';

// 识别方法枚举
export type RecognitionMethod = 'text' | 'image' | 'auto';

// 基础识别结果接口
export interface BaseRecognitionResult {
  method: RecognitionMethod;
  confidence: number;
  timestamp: string;
  processingTime: number;
}

// 食物识别结果
export interface FoodRecognitionResult extends BaseRecognitionResult {
  type: 'food';
  foods: FoodItem[];
  totalCalories: number;
  totalNutrition: NutritionInfo;
  personalizedAdvice?: string;
  exerciseAdvice?: string;
}

// 运动识别结果
export interface ExerciseRecognitionResult extends BaseRecognitionResult {
  type: 'exercise';
  exercises: ExerciseItem[];
  totalCaloriesBurned: number;
  totalDuration: number;
  intensityLevel: 'low' | 'medium' | 'high';
  recommendations?: string;
}

// 食物项目
export interface FoodItem {
  name: string;
  weight: number;
  unit: 'g' | 'ml' | '个' | '份';
  unitValue: number;
  calories: number;
  confidence: number;
  dataSource: 'nutrition_label' | 'visual_estimation' | 'text_analysis';
  nutrition: NutritionInfo;
  brand?: string;
  imageIndex?: number;
}

// 运动项目
export interface ExerciseItem {
  name: string;
  type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'daily';
  duration: number; // 分钟
  intensity: 'low' | 'medium' | 'high';
  caloriesBurned: number;
  confidence: number;
  dataSource: 'image_recognition' | 'text_analysis' | 'manual_input';
  equipment?: string[];
  muscleGroups?: string[];
  imageIndex?: number;
}

// 营养信息
export interface NutritionInfo {
  protein: number;    // 蛋白质(g)
  fat: number;        // 脂肪(g)
  carbs: number;      // 碳水化合物(g)
  fiber: number;      // 纤维(g)
  sugar: number;      // 糖分(g)
  sodium: number;     // 钠(mg)
}

// 识别策略接口
export interface RecognitionStrategy {
  recognize(input: RecognitionInput): Promise<FoodRecognitionResult | ExerciseRecognitionResult>;
  validateInput(input: RecognitionInput): boolean;
  getType(): RecognitionType;
  getMethod(): RecognitionMethod;
}

// 识别输入
export interface RecognitionInput {
  type: RecognitionType;
  method: RecognitionMethod;
  textContent?: string;
  images?: File[];
  additionalContext?: string;
  userContext?: UserContext;
}

// 用户上下文
export interface UserContext {
  age: number;
  gender: 'male' | 'female';
  weight: number;
  height: number;
  // {{ AURA-X: Modify - 统一活动水平类型定义，与bmr.ts保持一致. Approval: 寸止(ID:1737100800). }}
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'veryActive';
  targetWeight?: number;
  bmr?: number;
  tdee?: number;
}

// 识别状态
export interface RecognitionState {
  isProcessing: boolean;
  error: string | null;
  processingStep: string;
  progress: number; // 0-100
}

// 识别配置
export interface RecognitionConfig {
  enableAutoDetection: boolean;
  defaultMethod: RecognitionMethod;
  maxImages: number;
  timeout: number;
  enablePersonalization: boolean;
}

// 统一识别结果类型
export type UnifiedRecognitionResult = FoodRecognitionResult | ExerciseRecognitionResult;

// 识别历史记录
export interface RecognitionHistory {
  id: string;
  type: RecognitionType;
  result: UnifiedRecognitionResult;
  input: RecognitionInput;
  createdAt: string;
  updatedAt: string;
}

// 识别统计
export interface RecognitionStats {
  totalRecognitions: number;
  foodRecognitions: number;
  exerciseRecognitions: number;
  averageConfidence: number;
  averageProcessingTime: number;
  successRate: number;
}
