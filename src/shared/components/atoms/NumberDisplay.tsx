import React from 'react';
import { formatNumber, NumberPrecision } from '@/shared/utils/format';

interface NumberDisplayProps {
  value: number | string | undefined | null;
  unit?: string;
  className?: string;
  precision?: NumberPrecision;
  fallback?: string;
}

/**
 * 全局数值显示组件
 * 统一处理所有数值的格式化显示，支持单位、精度控制等
 *
 * @param value - 要显示的数值
 * @param unit - 单位（如 kg, cm, kcal 等）
 * @param className - CSS类名
 * @param precision - 精度控制：
 *   - 'auto': 自动去除无意义的.00（默认）
 *   - 'integer': 强制显示为整数
 *   - 'decimal': 强制显示两位小数
 *   - number: 指定小数位数
 * @param fallback - 当值为空时的回退显示
 */
export const NumberDisplay: React.FC<NumberDisplayProps> = ({
  value,
  unit,
  className = '',
  precision = 'auto',
  fallback = '0'
}) => {
  // 使用新的格式化系统
  const formattedText = formatNumber(value, {
    precision,
    unit,
    fallback
  });

  return (
    <span className={className} data-react-component="number-display">
      {formattedText}
    </span>
  );
};

// 预设的常用数值显示组件 - 根据新的格式化规则
export const WeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="weight-display">
    <NumberDisplay {...props} unit="kg" precision="decimal" />
  </span>
);

// 食物重量显示组件 - 智能单位选择
export const FoodWeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & {
  value: number | string;
  foodName?: string;
  unit?: string;
  unitValue?: number;
}> = ({ value, foodName, unit, unitValue, ...props }) => {
  // 如果提供了unit和unitValue，优先使用
  if (unit && unitValue !== undefined) {
    return (
      <span data-react-component="food-weight-display">
        <NumberDisplay {...props} value={unitValue} unit={unit} precision="decimal" />
      </span>
    );
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // 根据食物名称和重量智能选择单位（回退逻辑）
  const getSmartUnit = (weight: number, name?: string): string => {
    if (!name) {
      // 没有食物名称时，根据重量选择单位
      if (weight >= 1000) return 'kg';
      if (weight >= 100) return 'g';
      return 'g';
    }

    const lowerName = name.toLowerCase();

    // 液体类食物使用毫升
    if (lowerName.includes('水') || lowerName.includes('汁') || lowerName.includes('奶') ||
        lowerName.includes('茶') || lowerName.includes('咖啡') || lowerName.includes('汤') ||
        lowerName.includes('饮料') || lowerName.includes('酒') || lowerName.includes('醋') ||
        lowerName.includes('油') || lowerName.includes('酱油')) {
      return 'ml';
    }

    // 小份量食物使用个数
    if (lowerName.includes('鸡蛋') || lowerName.includes('苹果') || lowerName.includes('香蕉') ||
        lowerName.includes('橙子') || lowerName.includes('梨') || lowerName.includes('桃') ||
        lowerName.includes('饼干') || lowerName.includes('面包片')) {
      return '个';
    }

    // 片状食物
    if (lowerName.includes('片') || lowerName.includes('吐司')) {
      return '片';
    }

    // 碗装食物
    if (lowerName.includes('米饭') || lowerName.includes('面条') || lowerName.includes('粥') ||
        lowerName.includes('汤') || lowerName.includes('面')) {
      return '碗';
    }

    // 默认使用克
    return 'g';
  };

  const smartUnit = getSmartUnit(numValue, foodName);

  return (
    <span data-react-component="food-weight-display">
      <NumberDisplay {...props} value={value} unit={smartUnit} precision="decimal" />
    </span>
  );
};

export const HeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="height-display">
    <NumberDisplay {...props} unit="cm" precision="decimal" />
  </span>
);

export const AgeDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="age-display">
    <NumberDisplay {...props} unit="岁" precision="decimal" />
  </span>
);

export const CalorieDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="calorie-display">
    <NumberDisplay {...props} unit="kcal" precision="integer" />
  </span>
);

export const BMIDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="bmi-display">
    <NumberDisplay {...props} precision="decimal" />
  </span>
);

export const DaysDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="days-display">
    <NumberDisplay {...props} unit="天" precision="integer" />
  </span>
);

export default NumberDisplay;
