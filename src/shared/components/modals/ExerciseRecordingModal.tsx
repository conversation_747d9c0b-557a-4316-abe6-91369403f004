import React, { useState, useRef } from 'react';
import { useExerciseRecording } from '../../hooks/useExerciseRecording';

export type RecognitionMethod = 'text' | 'image' | 'auto';

interface ExerciseRecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
  currentDate?: Date;
  onProcessingStateChange?: (isProcessing: boolean) => void;
}

const ExerciseRecordingModal: React.FC<ExerciseRecordingModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete,
  currentDate,
  onProcessingStateChange
}) => {
  const [textInput, setTextInput] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [recognitionResult, setRecognitionResult] = useState<any | null>(null);
  const [showResultsView, setShowResultsView] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());
  const [currentMethod, setCurrentMethod] = useState<'text' | 'image'>('text');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const { state, startRecording, reset } = useExerciseRecording();

  // {{ AURA-X: Add - 参照食物记录模态框的功能，添加图片处理和UI状态管理. Approval: 寸止(ID:1737100800). }}

  // 智能检测识别方式
  const detectRecognitionMethod = (): 'text' | 'image' => {
    if (selectedImages.length > 0) {
      return 'image';
    }
    if (textInput.trim()) {
      return 'text';
    }
    return 'text';
  };

  // 自动更新识别方式
  React.useEffect(() => {
    if (method === 'auto') {
      const detectedMethod = detectRecognitionMethod();
      setCurrentMethod(detectedMethod);
    } else {
      setCurrentMethod(method);
    }
  }, [method, textInput, selectedImages]);

  // 处理图片选择 - 参照食物记录的单文件处理方式
  const handleImageSelect = (files: File[]) => {
    if (!files || files.length === 0) return;

    const file = files[0]; // 只处理第一个文件

    if (selectedImages.length >= 5) {
      alert('最多只能上传5张图片');
      return;
    }

    // 类型检查确保file是有效的File对象
    if (!file || !(file instanceof File)) {
      console.error('Invalid file object:', file);
      return;
    }

    if (!file.type.startsWith('image/')) {
      console.warn('跳过非图片文件:', file.name);
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB限制
      console.warn('文件过大，跳过:', file.name);
      return;
    }

    const newImages = [...selectedImages, file];
    setSelectedImages(newImages);

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (result && typeof result === 'string') {
        setImagePreviews(prev => [...prev, result]);
      }
    };
    reader.readAsDataURL(file);
  };

  // 移除图片
  const removeImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    setImagePreviews(newPreviews);
  };

  // 关闭模态框
  const handleClose = () => {
    if (!state.isProcessing) {
      resetState();
      reset();
      onProcessingStateChange?.(false);
      setTimeout(() => {
        onClose();
      }, 0);
    }
  };

  // 检查是否可以开始识别
  const canStartRecognition = (): boolean => {
    return textInput.trim().length > 0 || selectedImages.length > 0;
  };

  // 获取准确度颜色
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // 获取准确度文本
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高准确度';
    if (confidence >= 0.6) return '中等准确度';
    return '低准确度';
  };

  // 重置状态
  const resetState = () => {
    setTextInput('');
    setSelectedImages([]);
    setImagePreviews([]);
    setRecognitionResult(null);
    setShowResultsView(false);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }

    if (state.isProcessing) {
      reset();
    }
  };



  // 开始识别
  const handleStartRecognition = async () => {
    onProcessingStateChange?.(true);

    try {
      // {{ AURA-X: Modify - 增强运动识别逻辑，支持多图片和文本组合. Approval: 寸止(ID:1737100800). }}
      const combinedTextInput = textInput.trim();
      const imagesToUse = selectedImages.length > 0 ? selectedImages : undefined;

      // 确定最终的识别方法
      const finalMethod = detectRecognitionMethod();

      const result = await startRecording({
        description: combinedTextInput,
        images: imagesToUse,
        method: finalMethod
      });

      if (result && result.exercises && result.exercises.length > 0) {
        setRecognitionResult(result);
        setShowResultsView(true);
      } else {
        console.warn('运动识别结果为空');
      }

      onProcessingStateChange?.(false);
    } catch (error) {
      console.error('运动识别失败:', error);
      onProcessingStateChange?.(false);
    }
  };

  // 处理最终提交
  const handleFinalSubmit = () => {
    if (!recognitionResult) {
      console.error('recognitionResult 为空，无法提交');
      return;
    }

    if (!recognitionResult.exercises || recognitionResult.exercises.length === 0) {
      console.error('识别结果中没有运动数据');
      return;
    }

    try {
      // 添加选择的日期到结果中
      const finalResult = {
        ...recognitionResult,
        selectedDate: selectedDate,
        additionalContext: textInput.trim() || undefined
      };

      console.log('准备调用 onRecognitionComplete', finalResult);

      if (typeof onRecognitionComplete === 'function') {
        onProcessingStateChange?.(false);
        onRecognitionComplete(finalResult);
        setTimeout(() => {
          handleClose();
        }, 100);
      } else {
        console.error('onRecognitionComplete 不是一个函数');
        onProcessingStateChange?.(false);
      }
    } catch (error) {
      console.error('提交过程中发生错误:', error);
      onProcessingStateChange?.(false);
    }
  };

  // 重新识别
  const handleReRecognize = () => {
    setRecognitionResult(null);
    setShowResultsView(false);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
        willChange: 'auto',
        transform: 'translateZ(0)',
        ...(state.isProcessing && { touchAction: 'none' })
      }}
      onClick={handleClose}
      onTouchMove={state.isProcessing ? (e) => e.preventDefault() : undefined}
    >
      {/* 背景遮罩 */}
      <div className="absolute inset-0" onClick={handleClose}></div>

      {/* 模态框主体 */}
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-100">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">运动记录</h2>
            <p className="text-sm text-gray-500">
              {currentMethod === 'image' ? '📸 拍照识别运动类型' : '📝 描述运动计算消耗'}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="btn btn-ghost btn-sm btn-circle text-gray-400 hover:text-gray-600 hover:bg-gray-100"
            disabled={state.isProcessing}
            style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-20" style={{ touchAction: 'pan-y' }}>
          {!showResultsView ? (
            <>
              {/* 日期选择器 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">选择记录日期</h3>
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
                  <input
                    type="date"
                    value={selectedDate.toISOString().split('T')[0]}
                    onChange={(e) => setSelectedDate(new Date(e.target.value))}
                    max={new Date().toISOString().split('T')[0]}
                    className="input input-bordered input-sm w-full bg-white"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    当前选择：{selectedDate.toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long'
                    })}
                  </div>
                </div>
              </div>

              {/* 运动描述输入 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">描述运动</h3>
                <textarea
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  placeholder="例如：跑步30分钟，游泳1小时，举重训练..."
                  className="textarea textarea-bordered w-full h-32 resize-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                  disabled={state.isProcessing}
                  style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                />
                <div className="text-xs text-gray-500 mt-1">
                  详细描述运动类型、时长和强度，AI将为您智能分析
                </div>
              </div>

              {/* 图片上传区域 - 参照食物记录样式 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">上传运动图片（可选）</h3>
                <div className="space-y-3">
                  {/* 图片预览网格 */}
                  {imagePreviews.length > 0 && (
                    <div className="grid grid-cols-3 gap-2">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={preview}
                            alt={`运动图片 ${index + 1}`}
                            className="w-full h-20 object-cover rounded-lg border border-gray-200"
                          />
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              removeImage(index);
                            }}
                            className="absolute -top-1 -right-1 btn btn-ghost btn-xs btn-circle bg-red-500 text-white hover:bg-red-600"
                            disabled={state.isProcessing}
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 添加图片按钮 */}
                  {selectedImages.length < 5 && (
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={() => cameraInputRef.current?.click()}
                        className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                        disabled={state.isProcessing}
                        style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                      >
                        <span className="text-2xl flex-shrink-0">📷</span>
                        <span className="text-sm">拍照识别</span>
                      </button>
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                        disabled={state.isProcessing}
                        style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                      >
                        <span className="text-2xl flex-shrink-0">📁</span>
                        <span className="text-sm">从相册选择</span>
                      </button>
                    </div>
                  )}

                  {/* 隐藏的文件输入 */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file && file instanceof File) {
                        handleImageSelect([file]);
                      } else if (file) {
                        console.error('Invalid file type from input:', file);
                      }
                    }}
                    className="hidden"
                  />
                  <input
                    ref={cameraInputRef}
                    type="file"
                    accept="image/*"
                    capture="environment"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file && file instanceof File) {
                        handleImageSelect([file]);
                        // 清空input的value，确保下次拍照能触发onChange事件
                        if (e.target) {
                          e.target.value = '';
                        }
                      }
                    }}
                    className="hidden"
                  />

                  <div className="text-xs text-gray-500">
                    支持上传运动场景、器械或姿势图片，最多5张，每张不超过10MB
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* 识别结果视图 */
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800">识别结果</h3>
                <div className="text-sm text-gray-500">
                  {recognitionResult?.exercises?.length || 0} 项运动
                </div>
              </div>

              {recognitionResult && recognitionResult.exercises && (
                <div className="space-y-3">
                  {recognitionResult.exercises.map((exercise: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{exercise.name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              exercise.intensity === 'high' ? 'bg-red-100 text-red-800' :
                              exercise.intensity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {exercise.intensity === 'high' ? '高强度' :
                               exercise.intensity === 'medium' ? '中强度' : '低强度'}
                            </span>
                            <span className="text-xs text-gray-500">
                              {exercise.type === 'cardio' ? '有氧运动' :
                               exercise.type === 'strength' ? '力量训练' :
                               exercise.type === 'flexibility' ? '柔韧性' : '其他'}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">时长：</span>
                          <span className="font-medium">{exercise.duration} 分钟</span>
                        </div>
                        <div>
                          <span className="text-gray-500">消耗：</span>
                          <span className="font-medium text-orange-600">{exercise.caloriesBurned} kcal</span>
                        </div>
                      </div>

                      {exercise.confidence && (
                        <div className="mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(exercise.confidence)}`}>
                            {getConfidenceText(exercise.confidence)}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {recognitionResult?.recommendations && (
                <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 border border-blue-100 shadow-sm">
                  <h4 className="text-sm font-medium text-blue-800 mb-3 flex items-center">
                    <span className="mr-2">💡</span>运动建议
                  </h4>
                  <div
                    className="text-sm text-blue-700 leading-relaxed prose prose-sm max-w-none prose-headings:text-blue-800 prose-strong:text-blue-800 prose-p:mb-2"
                    dangerouslySetInnerHTML={{ __html: recognitionResult.recommendations }}
                  />
                </div>
              )}
            </div>
          )}

          {/* 错误提示 */}
          {state.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <span className="text-red-800 text-sm">{state.error}</span>
            </div>
          )}

          {/* 进度提示 */}
          {state.isProcessing && state.processingStep && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="loading loading-spinner loading-sm text-blue-600"></div>
                <span className="text-sm text-blue-700 font-medium">{state.processingStep}</span>
              </div>
            </div>
          )}

          {/* 底部间距保障 */}
          <div className="h-4"></div>
        </div>

        {/* 操作按钮 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!state.isProcessing ? (
            showResultsView ? (
              /* 结果视图按钮 */
              <>
                <button
                  onClick={handleReRecognize}
                  className="btn btn-outline flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  重新识别
                </button>
                <button
                  onClick={handleFinalSubmit}
                  className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>确认添加</span>
                </button>
              </>
            ) : (
              /* 初始识别按钮 */
              <button
                onClick={handleStartRecognition}
                disabled={!canStartRecognition() || state.isProcessing}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
              >
                <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span>开始识别</span>
              </button>
            )
          ) : (
            /* 处理中状态 */
            <button
              onClick={() => {
                // 这里可以添加停止识别的逻辑
                onProcessingStateChange?.(false);
              }}
              className="w-full bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-medium py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center gap-2"
            >
              <div className="loading loading-spinner loading-sm"></div>
              <span>取消分析</span>
            </button>
          )}
        </div>


      </div>
    </div>
  );
};

export default ExerciseRecordingModal;
