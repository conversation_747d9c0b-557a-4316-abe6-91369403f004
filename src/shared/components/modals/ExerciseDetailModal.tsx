import React, { useState, useEffect } from 'react';
import { ExerciseRecord } from '@/domains/exercise/stores/exerciseStore';

interface ExerciseDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  exerciseRecord: ExerciseRecord | null;
  onSave?: (updatedRecord: ExerciseRecord, newDate?: Date) => void;
  onDelete?: (recordId: string) => void;
  isEditMode?: boolean;
  currentDate?: Date;
}

const ExerciseDetailModal: React.FC<ExerciseDetailModalProps> = ({
  isOpen,
  onClose,
  exerciseRecord,
  onSave,
  onDelete,
  isEditMode = false,
  currentDate
}) => {
  const [editMode, setEditMode] = useState(isEditMode);
  const [formData, setFormData] = useState<Partial<ExerciseRecord>>({});
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());

  // 初始化表单数据
  useEffect(() => {
    if (exerciseRecord) {
      setFormData({
        name: exerciseRecord.name,
        type: exerciseRecord.type,
        duration: exerciseRecord.duration,
        intensity: exerciseRecord.intensity,
        caloriesBurned: exerciseRecord.caloriesBurned,
        notes: exerciseRecord.notes || ''
      });
      setSelectedDate(new Date(exerciseRecord.recordedAt));
    }
  }, [exerciseRecord]);

  // 重置状态
  const resetState = () => {
    setEditMode(isEditMode);
    setFormData({});
    setSelectedDate(currentDate || new Date());
  };

  // 关闭模态框
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 保存修改
  const handleSave = () => {
    if (!exerciseRecord || !onSave) return;

    const updatedRecord: ExerciseRecord = {
      ...exerciseRecord,
      ...formData,
      recordedAt: selectedDate,
      isEdited: true
    };

    onSave(updatedRecord, selectedDate);
    handleClose();
  };

  // 删除记录
  const handleDelete = () => {
    if (!exerciseRecord || !onDelete) return;
    onDelete(exerciseRecord.id);
    handleClose();
  };

  // 获取运动类型显示文本
  const getExerciseTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      cardio: '有氧运动',
      strength: '力量训练',
      flexibility: '柔韧性',
      other: '其他'
    };
    return typeMap[type] || type;
  };

  // 获取强度显示文本
  const getIntensityText = (intensity: string) => {
    const intensityMap: { [key: string]: string } = {
      low: '低强度',
      medium: '中强度',
      high: '高强度'
    };
    return intensityMap[intensity] || intensity;
  };

  // 获取准确度颜色
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // 获取准确度文本
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高准确度';
    if (confidence >= 0.6) return '中等准确度';
    return '低准确度';
  };

  if (!isOpen || !exerciseRecord) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={handleClose}></div>
      
      {/* 模态框主体 */}
      <div 
        className="relative bg-white rounded-2xl w-full max-w-md max-h-[85vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-100">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              {editMode ? '编辑运动记录' : '运动详情'}
            </h2>
            <p className="text-sm text-gray-500">
              {editMode ? '修改运动信息' : '查看运动详细信息'}
            </p>
          </div>
          <button 
            onClick={handleClose} 
            className="btn btn-ghost btn-sm btn-circle text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 - 与FoodNutritionModal保持一致 */}
        <div className="flex-1 overflow-y-auto relative">
          <div className="p-6 pb-20 space-y-6">
          {editMode ? (
            /* 编辑模式 */
            <>
              {/* 日期选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">记录日期</label>
                <input
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  max={new Date().toISOString().split('T')[0]}
                  className="input input-bordered w-full"
                />
              </div>

              {/* 运动名称 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">运动名称</label>
                <input
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="input input-bordered w-full"
                  placeholder="例如：跑步、游泳、举重..."
                />
              </div>

              {/* 运动类型 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">运动类型</label>
                <select
                  value={formData.type || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as ExerciseRecord['type'] }))}
                  className="select select-bordered w-full"
                >
                  <option value="cardio">有氧运动</option>
                  <option value="strength">力量训练</option>
                  <option value="flexibility">柔韧性</option>
                  <option value="other">其他</option>
                </select>
              </div>

              {/* 运动时长 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">运动时长（分钟）</label>
                <input
                  type="number"
                  value={formData.duration || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration: Number(e.target.value) }))}
                  className="input input-bordered w-full"
                  min="1"
                  max="600"
                />
              </div>

              {/* 运动强度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">运动强度</label>
                <select
                  value={formData.intensity || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, intensity: e.target.value as ExerciseRecord['intensity'] }))}
                  className="select select-bordered w-full"
                >
                  <option value="low">低强度</option>
                  <option value="medium">中强度</option>
                  <option value="high">高强度</option>
                </select>
              </div>

              {/* 消耗卡路里 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">消耗卡路里</label>
                <input
                  type="number"
                  value={formData.caloriesBurned || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, caloriesBurned: Number(e.target.value) }))}
                  className="input input-bordered w-full"
                  min="1"
                  max="2000"
                />
              </div>

              {/* 备注 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">备注</label>
                <textarea
                  value={formData.notes || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  className="textarea textarea-bordered w-full h-20 resize-none"
                  placeholder="添加运动备注..."
                />
              </div>
            </>
          ) : (
            /* 查看模式 */
            <>
              {/* 运动基本信息 - 与食物记录基础信息样式保持一致 */}
              <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 xl:p-6 border border-blue-100 shadow-sm">
                <h3 className="text-lg xl:text-xl font-semibold text-gray-800 mb-4 xl:mb-6 flex items-center">
                  <div className="w-2 h-2 xl:w-3 xl:h-3 bg-blue-500 rounded-full mr-3"></div>基础信息
                </h3>
                <div className="space-y-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">运动名称</label>
                    <div className="text-sm font-medium text-gray-900">{exerciseRecord.name}</div>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">记录日期</label>
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(exerciseRecord.recordedAt).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">运动时长</label>
                      <div className="text-sm font-medium text-gray-900">{exerciseRecord.duration} 分钟</div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">消耗卡路里</label>
                      <div className="text-sm font-medium text-red-600">-{exerciseRecord.caloriesBurned} kcal</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">运动类型</label>
                      <div className="text-sm font-medium text-gray-900">{getExerciseTypeText(exerciseRecord.type)}</div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">运动强度</label>
                      <div className="text-sm font-medium text-gray-900">{getIntensityText(exerciseRecord.intensity)}</div>
                    </div>
                  </div>

                </div>
              </div>

              {/* 记录信息 - 重构为指定HTML结构 */}
              <div className="bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50 rounded-xl p-4 border border-gray-100 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <div className="w-2 h-2 bg-gray-500 rounded-full mr-3"></div>记录信息
                </h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">添加时间：</span>
                    <span className="font-medium">
                      {new Date(exerciseRecord.recordedAt).toLocaleTimeString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>

                  {exerciseRecord.dataSource && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">识别方法：</span>
                      <span className="font-medium">
                        {exerciseRecord.dataSource === 'ai_recognition' ? '文本识别' :
                         exerciseRecord.dataSource === 'image_analysis' ? '图像识别' : '手动输入'}
                      </span>
                    </div>
                  )}

                  {exerciseRecord.confidence && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">准确度：</span>
                      <span className={`font-medium ${
                        exerciseRecord.confidence >= 0.8 ? 'text-green-600' :
                        exerciseRecord.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {Math.round(exerciseRecord.confidence * 100)}% ({getConfidenceText(exerciseRecord.confidence)})
                      </span>
                    </div>
                  )}

                  {exerciseRecord.isEdited && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600">最后编辑：</span>
                        <span className="font-medium">
                          {new Date(exerciseRecord.recordedAt).toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                      <div className="text-xs text-orange-600 bg-orange-50 rounded p-1 mt-1">
                        ⚠️ 此记录已被手动编辑
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* AI运动建议 */}
              {exerciseRecord.recommendations && (
                <div className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 border border-blue-100 shadow-sm">
                  <h4 className="text-sm font-medium text-blue-800 mb-3 flex items-center">
                    <span className="mr-2">💡</span>AI运动建议
                  </h4>
                  <div
                    className="text-sm text-blue-700 leading-relaxed prose prose-sm max-w-none prose-headings:text-blue-800 prose-strong:text-blue-800 prose-ul:text-blue-700 prose-li:text-blue-700"
                    dangerouslySetInnerHTML={{ __html: exerciseRecord.recommendations }}
                  />
                </div>
              )}

              {/* 备注 */}
              {exerciseRecord.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">备注</h4>
                  <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">{exerciseRecord.notes}</p>
                </div>
              )}
            </>
          )}

          {/* 底部间距保障 */}
          <div className="h-4"></div>
          </div>
        </div>

        {/* 操作按钮 - 与FoodNutritionModal保持一致 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!editMode ? (
            <>
              <button
                onClick={() => setEditMode(true)}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑
              </button>
              <button
                onClick={handleClose}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                关闭
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setEditMode(false)}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                取消
              </button>
              <button
                onClick={handleSave}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                保存修改
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExerciseDetailModal;
