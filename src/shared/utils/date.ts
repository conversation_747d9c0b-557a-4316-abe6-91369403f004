import { format, parse, isValid, addDays, subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, isSameDay, isSameWeek, isSameMonth, differenceInDays, subWeeks, subMonths, subYears, eachDayOfInterval } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// Re-export date-fns functions for convenience
export { addDays, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, subWeeks, subMonths, subYears, eachDayOfInterval };

/**
 * 格式化日期
 */
export function formatDate(date: Date | string | number, formatStr: string = 'yyyy-MM-dd'): string {
  const parsedDate = parseDate(date);
  if (!isValid(parsedDate)) return '';
  
  return format(parsedDate, formatStr, { locale: zhCN });
}

/**
 * 解析日期
 */
export function parseDate(date: Date | string | number): Date {
  if (date instanceof Date) return date;
  
  if (typeof date === 'string') {
    // 尝试解析ISO格式
    const parsedDate = new Date(date);
    if (isValid(parsedDate)) return parsedDate;
    
    // 尝试解析常见格式
    try {
      return parse(date, 'yyyy-MM-dd', new Date());
    } catch (error) {
      console.error('日期解析错误:', error);
      return new Date();
    }
  }
  
  if (typeof date === 'number') {
    return new Date(date);
  }
  
  return new Date();
}

/**
 * 获取日期范围
 */
export function getDateRange(
  type: 'day' | 'week' | 'month',
  date: Date = new Date()
): { start: Date; end: Date } {
  const parsedDate = parseDate(date);
  
  switch (type) {
    case 'day':
      return {
        start: startOfDay(parsedDate),
        end: endOfDay(parsedDate)
      };
    case 'week':
      return {
        start: startOfWeek(parsedDate, { locale: zhCN }),
        end: endOfWeek(parsedDate, { locale: zhCN })
      };
    case 'month':
      return {
        start: startOfMonth(parsedDate),
        end: endOfMonth(parsedDate)
      };
    default:
      return {
        start: startOfDay(parsedDate),
        end: endOfDay(parsedDate)
      };
  }
}

/**
 * 检查日期是否在同一时间段
 */
export function isInSamePeriod(
  date1: Date | string | number,
  date2: Date | string | number,
  type: 'day' | 'week' | 'month'
): boolean {
  const parsedDate1 = parseDate(date1);
  const parsedDate2 = parseDate(date2);
  
  switch (type) {
    case 'day':
      return isSameDay(parsedDate1, parsedDate2);
    case 'week':
      return isSameWeek(parsedDate1, parsedDate2, { locale: zhCN });
    case 'month':
      return isSameMonth(parsedDate1, parsedDate2);
    default:
      return isSameDay(parsedDate1, parsedDate2);
  }
}

/**
 * 获取相对日期
 */
export function getRelativeDate(days: number): Date {
  return days >= 0 ? addDays(new Date(), days) : subDays(new Date(), Math.abs(days));
}

/**
 * 计算日期差异（天数）
 */
export function getDaysDifference(startDate: Date | string | number, endDate: Date | string | number): number {
  const parsedStartDate = parseDate(startDate);
  const parsedEndDate = parseDate(endDate);
  
  return differenceInDays(parsedEndDate, parsedStartDate);
}

/**
 * 获取月份的天数数组
 */
export function getMonthDays(year: number, month: number): Date[] {
  const date = new Date(year, month - 1, 1);
  const days: Date[] = [];
  
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);
  
  let currentDay = monthStart;
  
  while (currentDay <= monthEnd) {
    days.push(new Date(currentDay));
    currentDay = addDays(currentDay, 1);
  }
  
  return days;
}

/**
 * 获取友好的相对时间描述
 */
export function getFriendlyTimeAgo(date: Date | string | number): string {
  const parsedDate = parseDate(date);
  const now = new Date();
  const diffInDays = differenceInDays(now, parsedDate);
  
  if (diffInDays === 0) return '今天';
  if (diffInDays === 1) return '昨天';
  if (diffInDays === 2) return '前天';
  if (diffInDays <= 7) return `${diffInDays}天前`;
  
  return formatDate(parsedDate, 'MM月dd日');
}