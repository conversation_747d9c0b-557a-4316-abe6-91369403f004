/**
 * 食物识别策略实现
 * 支持文字和图像两种识别方式
 */

import { 
  RecognitionStrategy, 
  RecognitionInput, 
  FoodRecognitionResult,
  RecognitionType,
  RecognitionMethod,
  FoodItem,
  NutritionInfo
} from '../types/recognition';
import { aiService } from '../../infrastructure/ai/AIService';

// 食物文字识别策略
export class FoodTextRecognitionStrategy implements RecognitionStrategy {
  getType(): RecognitionType {
    return 'food';
  }

  getMethod(): RecognitionMethod {
    return 'text';
  }

  validateInput(input: RecognitionInput): boolean {
    return !!(input.textContent && input.textContent.trim().length > 0);
  }

  async recognize(input: RecognitionInput): Promise<FoodRecognitionResult> {
    if (!input.textContent) {
      throw new Error('文字识别需要提供文本内容');
    }

    try {
      const startTime = Date.now();
      
      // 调用AI服务进行文字分析
      const aiResult = await aiService.analyzeText(
        input.textContent,
        input.additionalContext
      );

      // 转换AI结果为标准格式
      const foods: FoodItem[] = aiResult.foods.map(food => ({
        name: food.name,
        weight: food.weight,
        unit: food.unit || 'g',
        unitValue: food.unitValue || food.weight,
        calories: food.calories,
        confidence: food.confidence,
        dataSource: 'text_analysis',
        nutrition: food.nutrition,
        brand: food.brand
      }));

      // 计算总营养信息
      const totalNutrition = this.calculateTotalNutrition(foods);
      const totalCalories = foods.reduce((sum, food) => sum + food.calories, 0);

      // 生成个性化建议
      const personalizedAdvice = await this.generatePersonalizedAdvice(
        foods, 
        input.userContext
      );

      return {
        type: 'food',
        method: 'text',
        confidence: this.calculateAverageConfidence(foods),
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        foods,
        totalCalories,
        totalNutrition,
        personalizedAdvice
      };

    } catch (error) {
      throw new Error(`食物文字识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private calculateTotalNutrition(foods: FoodItem[]): NutritionInfo {
    return foods.reduce((total, food) => ({
      protein: total.protein + food.nutrition.protein,
      fat: total.fat + food.nutrition.fat,
      carbs: total.carbs + food.nutrition.carbs,
      fiber: total.fiber + food.nutrition.fiber,
      sugar: total.sugar + food.nutrition.sugar,
      sodium: total.sodium + food.nutrition.sodium
    }), {
      protein: 0,
      fat: 0,
      carbs: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0
    });
  }

  private calculateAverageConfidence(foods: FoodItem[]): number {
    if (foods.length === 0) return 0;
    const totalConfidence = foods.reduce((sum, food) => sum + food.confidence, 0);
    return totalConfidence / foods.length;
  }

  private async generatePersonalizedAdvice(
    foods: FoodItem[], 
    userContext?: any
  ): Promise<string | undefined> {
    if (!userContext) return undefined;

    try {
      return await aiService.generateNutritionAdvice(foods, userContext);
    } catch (error) {
      console.warn('生成个性化建议失败:', error);
      return undefined;
    }
  }
}

// 食物图像识别策略
export class FoodImageRecognitionStrategy implements RecognitionStrategy {
  getType(): RecognitionType {
    return 'food';
  }

  getMethod(): RecognitionMethod {
    return 'image';
  }

  validateInput(input: RecognitionInput): boolean {
    return !!(input.images && input.images.length > 0);
  }

  async recognize(input: RecognitionInput): Promise<FoodRecognitionResult> {
    if (!input.images || input.images.length === 0) {
      throw new Error('图像识别需要提供图片');
    }

    try {
      const startTime = Date.now();
      
      // 调用AI服务进行图像识别
      const aiResult = await aiService.recognizeFood(
        input.images,
        input.additionalContext
      );

      // 转换AI结果为标准格式
      const foods: FoodItem[] = aiResult.foods.map(food => ({
        name: food.name,
        weight: food.weight,
        unit: food.unit || 'g',
        unitValue: food.unitValue || food.weight,
        calories: food.calories,
        confidence: food.confidence,
        dataSource: food.dataSource || 'visual_estimation',
        nutrition: food.nutrition,
        brand: food.brand,
        imageIndex: food.imageIndex
      }));

      // 计算总营养信息
      const totalNutrition = this.calculateTotalNutrition(foods);
      const totalCalories = foods.reduce((sum, food) => sum + food.calories, 0);

      // 生成个性化建议和运动建议
      const personalizedAdvice = await this.generatePersonalizedAdvice(
        foods, 
        input.userContext
      );
      
      const exerciseAdvice = await this.generateExerciseAdvice(
        totalCalories,
        input.userContext
      );

      return {
        type: 'food',
        method: 'image',
        confidence: this.calculateAverageConfidence(foods),
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        foods,
        totalCalories,
        totalNutrition,
        personalizedAdvice,
        exerciseAdvice
      };

    } catch (error) {
      throw new Error(`食物图像识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private calculateTotalNutrition(foods: FoodItem[]): NutritionInfo {
    return foods.reduce((total, food) => ({
      protein: total.protein + food.nutrition.protein,
      fat: total.fat + food.nutrition.fat,
      carbs: total.carbs + food.nutrition.carbs,
      fiber: total.fiber + food.nutrition.fiber,
      sugar: total.sugar + food.nutrition.sugar,
      sodium: total.sodium + food.nutrition.sodium
    }), {
      protein: 0,
      fat: 0,
      carbs: 0,
      fiber: 0,
      sugar: 0,
      sodium: 0
    });
  }

  private calculateAverageConfidence(foods: FoodItem[]): number {
    if (foods.length === 0) return 0;
    const totalConfidence = foods.reduce((sum, food) => sum + food.confidence, 0);
    return totalConfidence / foods.length;
  }

  private async generatePersonalizedAdvice(
    foods: FoodItem[], 
    userContext?: any
  ): Promise<string | undefined> {
    if (!userContext) return undefined;

    try {
      return await aiService.generateNutritionAdvice(foods, userContext);
    } catch (error) {
      console.warn('生成个性化建议失败:', error);
      return undefined;
    }
  }

  private async generateExerciseAdvice(
    calories: number,
    userContext?: any
  ): Promise<string | undefined> {
    if (!userContext) return undefined;

    try {
      // 基于摄入卡路里生成运动建议
      const weight = userContext.weight || 70;
      const walkingMinutes = Math.round(calories / (3.5 * weight * 0.0175));
      const runningMinutes = Math.round(calories / (8 * weight * 0.0175));
      
      return `建议运动消耗：快走 ${walkingMinutes} 分钟或跑步 ${runningMinutes} 分钟来消耗这些卡路里`;
    } catch (error) {
      console.warn('生成运动建议失败:', error);
      return undefined;
    }
  }
}
