/**
 * 运动识别策略实现
 * 支持文字和图像两种识别方式
 */

import { 
  RecognitionStrategy, 
  RecognitionInput, 
  ExerciseRecognitionResult,
  RecognitionType,
  RecognitionMethod,
  ExerciseItem,
  UserContext
} from '../types/recognition';
import { aiService } from '../../infrastructure/ai/AIService';

// 运动文字识别策略
export class ExerciseTextRecognitionStrategy implements RecognitionStrategy {
  getType(): RecognitionType {
    return 'exercise';
  }

  getMethod(): RecognitionMethod {
    return 'text';
  }

  validateInput(input: RecognitionInput): boolean {
    return !!(input.textContent && input.textContent.trim().length > 0);
  }

  async recognize(input: RecognitionInput): Promise<ExerciseRecognitionResult> {
    if (!input.textContent) {
      throw new Error('运动文字识别需要提供文本内容');
    }

    try {
      const startTime = Date.now();
      
      // 调用AI服务进行运动文字分析
      const aiResult = await aiService.analyzeExerciseText(
        input.textContent,
        input.additionalContext,
        input.userContext
      );

      // 转换AI结果为标准格式
      const exercises: ExerciseItem[] = aiResult.exercises.map(exercise => ({
        name: exercise.name,
        type: exercise.type,
        duration: exercise.duration,
        intensity: exercise.intensity,
        caloriesBurned: exercise.caloriesBurned,
        confidence: exercise.confidence,
        dataSource: 'text_analysis',
        equipment: exercise.equipment,
        muscleGroups: exercise.muscleGroups
      }));

      // 计算总数据
      const totalCaloriesBurned = exercises.reduce((sum, ex) => sum + ex.caloriesBurned, 0);
      const totalDuration = exercises.reduce((sum, ex) => sum + ex.duration, 0);
      const intensityLevel = this.calculateOverallIntensity(exercises);

      // {{ AURA-X: Modify - 直接使用AI返回的运动建议，不重新生成. Approval: 寸止(ID:1737100000). }}
      // 使用AI返回的运动建议
      const recommendations = aiResult.recommendations;

      return {
        type: 'exercise',
        method: 'text',
        confidence: this.calculateAverageConfidence(exercises),
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        exercises,
        totalCaloriesBurned,
        totalDuration,
        intensityLevel,
        recommendations
      };

    } catch (error) {
      throw new Error(`运动文字识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private calculateAverageConfidence(exercises: ExerciseItem[]): number {
    if (exercises.length === 0) return 0;
    const totalConfidence = exercises.reduce((sum, ex) => sum + ex.confidence, 0);
    return totalConfidence / exercises.length;
  }

  private calculateOverallIntensity(exercises: ExerciseItem[]): 'low' | 'medium' | 'high' {
    if (exercises.length === 0) return 'low';
    
    const intensityScores = exercises.map(ex => {
      switch (ex.intensity) {
        case 'low': return 1;
        case 'medium': return 2;
        case 'high': return 3;
        default: return 1;
      }
    });

    const averageScore = intensityScores.reduce((sum, score) => sum + score, 0) / intensityScores.length;
    
    if (averageScore >= 2.5) return 'high';
    if (averageScore >= 1.5) return 'medium';
    return 'low';
  }

  // {{ AURA-X: Remove - 删除不再需要的generateRecommendations方法，直接使用AI返回的建议. Approval: 寸止(ID:1737100000). }}
}

// 运动图像识别策略
export class ExerciseImageRecognitionStrategy implements RecognitionStrategy {
  getType(): RecognitionType {
    return 'exercise';
  }

  getMethod(): RecognitionMethod {
    return 'image';
  }

  validateInput(input: RecognitionInput): boolean {
    return !!(input.images && input.images.length > 0);
  }

  async recognize(input: RecognitionInput): Promise<ExerciseRecognitionResult> {
    if (!input.images || input.images.length === 0) {
      throw new Error('运动图像识别需要提供图片');
    }

    try {
      const startTime = Date.now();
      
      // 调用AI服务进行运动图像识别
      const aiResult = await aiService.recognizeExercise(
        input.images,
        input.additionalContext,
        input.userContext
      );

      // 转换AI结果为标准格式
      const exercises: ExerciseItem[] = aiResult.exercises.map(exercise => ({
        name: exercise.name,
        type: exercise.type,
        duration: exercise.duration,
        intensity: exercise.intensity,
        caloriesBurned: exercise.caloriesBurned,
        confidence: exercise.confidence,
        dataSource: 'image_recognition',
        equipment: exercise.equipment,
        muscleGroups: exercise.muscleGroups,
        imageIndex: exercise.imageIndex
      }));

      // 计算总数据
      const totalCaloriesBurned = exercises.reduce((sum, ex) => sum + ex.caloriesBurned, 0);
      const totalDuration = exercises.reduce((sum, ex) => sum + ex.duration, 0);
      const intensityLevel = this.calculateOverallIntensity(exercises);

      // {{ AURA-X: Modify - 直接使用AI返回的运动建议，不重新生成. Approval: 寸止(ID:1737100000). }}
      // 使用AI返回的运动建议
      const recommendations = aiResult.recommendations;

      return {
        type: 'exercise',
        method: 'image',
        confidence: this.calculateAverageConfidence(exercises),
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - startTime,
        exercises,
        totalCaloriesBurned,
        totalDuration,
        intensityLevel,
        recommendations
      };

    } catch (error) {
      throw new Error(`运动图像识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  private calculateAverageConfidence(exercises: ExerciseItem[]): number {
    if (exercises.length === 0) return 0;
    const totalConfidence = exercises.reduce((sum, ex) => sum + ex.confidence, 0);
    return totalConfidence / exercises.length;
  }

  private calculateOverallIntensity(exercises: ExerciseItem[]): 'low' | 'medium' | 'high' {
    if (exercises.length === 0) return 'low';
    
    const intensityScores = exercises.map(ex => {
      switch (ex.intensity) {
        case 'low': return 1;
        case 'medium': return 2;
        case 'high': return 3;
        default: return 1;
      }
    });

    const averageScore = intensityScores.reduce((sum, score) => sum + score, 0) / intensityScores.length;
    
    if (averageScore >= 2.5) return 'high';
    if (averageScore >= 1.5) return 'medium';
    return 'low';
  }

  // {{ AURA-X: Remove - 删除不再需要的generateRecommendations方法，直接使用AI返回的建议. Approval: 寸止(ID:1737100000). }}
}

// 卡路里计算工具类
export class CalorieCalculator {
  // MET值表 (代谢当量)
  private static readonly MET_VALUES = {
    // 有氧运动
    walking: { slow: 2.5, moderate: 3.5, fast: 4.3 },
    running: { slow: 6.0, moderate: 8.0, fast: 11.0 },
    cycling: { slow: 4.0, moderate: 6.8, fast: 10.0 },
    swimming: { slow: 4.0, moderate: 6.0, fast: 8.0 },
    
    // 力量训练
    weightlifting: { light: 3.0, moderate: 5.0, heavy: 6.0 },
    bodyweight: { light: 3.5, moderate: 4.5, heavy: 6.0 },
    
    // 运动项目
    basketball: 6.5,
    football: 8.0,
    tennis: 7.0,
    badminton: 5.5,
    
    // 日常活动
    housework: 3.0,
    gardening: 4.0,
    stairs: 8.0
  };

  static calculateCaloriesBurned(
    exerciseType: string,
    intensity: 'low' | 'medium' | 'high',
    duration: number, // 分钟
    weight: number // 公斤
  ): number {
    let met = 3.5; // 默认MET值

    // 根据运动类型和强度获取MET值
    const normalizedType = exerciseType.toLowerCase();
    
    if (normalizedType.includes('walk')) {
      met = this.MET_VALUES.walking[intensity === 'low' ? 'slow' : intensity === 'medium' ? 'moderate' : 'fast'];
    } else if (normalizedType.includes('run')) {
      met = this.MET_VALUES.running[intensity === 'low' ? 'slow' : intensity === 'medium' ? 'moderate' : 'fast'];
    } else if (normalizedType.includes('cycle') || normalizedType.includes('bike')) {
      met = this.MET_VALUES.cycling[intensity === 'low' ? 'slow' : intensity === 'medium' ? 'moderate' : 'fast'];
    } else if (normalizedType.includes('swim')) {
      met = this.MET_VALUES.swimming[intensity === 'low' ? 'slow' : intensity === 'medium' ? 'moderate' : 'fast'];
    } else if (normalizedType.includes('weight') || normalizedType.includes('strength')) {
      met = this.MET_VALUES.weightlifting[intensity === 'low' ? 'light' : intensity === 'medium' ? 'moderate' : 'heavy'];
    } else {
      // 根据强度设置默认MET值
      met = intensity === 'low' ? 3.0 : intensity === 'medium' ? 5.0 : 7.0;
    }

    // 卡路里计算公式: MET × 体重(kg) × 时间(小时)
    const hours = duration / 60;
    return Math.round(met * weight * hours);
  }
}
