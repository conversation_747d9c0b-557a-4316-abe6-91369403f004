/**
 * 统一识别服务
 * 使用策略模式实现食物记录和运动记录的统一识别
 */

import { 
  RecognitionStrategy, 
  RecognitionInput, 
  UnifiedRecognitionResult,
  RecognitionType,
  RecognitionMethod,
  RecognitionConfig,
  RecognitionState
} from '../types/recognition';

// 策略工厂
class RecognitionStrategyFactory {
  private strategies = new Map<string, RecognitionStrategy>();

  register(type: RecognitionType, method: RecognitionMethod, strategy: RecognitionStrategy) {
    const key = `${type}-${method}`;
    this.strategies.set(key, strategy);
  }

  getStrategy(type: RecognitionType, method: RecognitionMethod): RecognitionStrategy | null {
    const key = `${type}-${method}`;
    return this.strategies.get(key) || null;
  }

  getAllStrategies(): RecognitionStrategy[] {
    return Array.from(this.strategies.values());
  }
}

// 统一识别服务
export class UnifiedRecognitionService {
  private strategyFactory: RecognitionStrategyFactory;
  private config: RecognitionConfig;
  private state: RecognitionState;
  private stateListeners: ((state: RecognitionState) => void)[] = [];

  constructor(config: RecognitionConfig) {
    this.strategyFactory = new RecognitionStrategyFactory();
    this.config = config;
    this.state = {
      isProcessing: false,
      error: null,
      processingStep: '',
      progress: 0
    };
  }

  // 注册识别策略
  registerStrategy(type: RecognitionType, method: RecognitionMethod, strategy: RecognitionStrategy) {
    this.strategyFactory.register(type, method, strategy);
  }

  // 智能识别方法选择
  private determineMethod(input: RecognitionInput): RecognitionMethod {
    if (input.method !== 'auto') {
      return input.method;
    }

    // 自动检测逻辑
    if (input.images && input.images.length > 0) {
      return 'image';
    }

    if (input.textContent && input.textContent.trim().length > 0) {
      return 'text';
    }

    return this.config.defaultMethod;
  }

  // 更新状态
  private updateState(updates: Partial<RecognitionState>) {
    this.state = { ...this.state, ...updates };
    this.stateListeners.forEach(listener => listener(this.state));
  }

  // 添加状态监听器
  addStateListener(listener: (state: RecognitionState) => void) {
    this.stateListeners.push(listener);
  }

  // 移除状态监听器
  removeStateListener(listener: (state: RecognitionState) => void) {
    const index = this.stateListeners.indexOf(listener);
    if (index > -1) {
      this.stateListeners.splice(index, 1);
    }
  }

  // 主要识别方法
  async recognize(input: RecognitionInput): Promise<UnifiedRecognitionResult> {
    try {
      this.updateState({
        isProcessing: true,
        error: null,
        processingStep: '准备识别...',
        progress: 0
      });

      // 确定识别方法
      const method = this.determineMethod(input);
      const finalInput = { ...input, method };

      this.updateState({
        processingStep: '选择识别策略...',
        progress: 20
      });

      // 获取对应的策略
      const strategy = this.strategyFactory.getStrategy(input.type, method);
      if (!strategy) {
        throw new Error(`未找到 ${input.type}-${method} 的识别策略`);
      }

      this.updateState({
        processingStep: '验证输入数据...',
        progress: 30
      });

      // 验证输入
      if (!strategy.validateInput(finalInput)) {
        throw new Error('输入数据验证失败');
      }

      this.updateState({
        processingStep: input.type === 'exercise' ? '正在分析运动中...' : '执行识别...',
        progress: 50
      });

      // 执行识别
      const result = await strategy.recognize(finalInput);

      this.updateState({
        processingStep: '处理结果...',
        progress: 90
      });

      // 添加处理时间
      const finalResult = {
        ...result,
        processingTime: Date.now() - new Date(result.timestamp).getTime()
      };

      this.updateState({
        processingStep: '识别完成',
        progress: 100,
        isProcessing: false
      });

      return finalResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '识别过程中发生未知错误';
      
      this.updateState({
        isProcessing: false,
        error: errorMessage,
        processingStep: '识别失败',
        progress: 0
      });

      throw error;
    }
  }

  // 批量识别
  async recognizeBatch(inputs: RecognitionInput[]): Promise<UnifiedRecognitionResult[]> {
    const results: UnifiedRecognitionResult[] = [];
    
    for (let i = 0; i < inputs.length; i++) {
      try {
        this.updateState({
          processingStep: `处理第 ${i + 1}/${inputs.length} 项...`,
          progress: (i / inputs.length) * 100
        });

        const result = await this.recognize(inputs[i]);
        results.push(result);
      } catch (error) {
        console.error(`批量识别第 ${i + 1} 项失败:`, error);
        // 继续处理下一项
      }
    }

    return results;
  }

  // 获取当前状态
  getState(): RecognitionState {
    return { ...this.state };
  }

  // 获取配置
  getConfig(): RecognitionConfig {
    return { ...this.config };
  }

  // 更新配置
  updateConfig(updates: Partial<RecognitionConfig>) {
    this.config = { ...this.config, ...updates };
  }

  // 获取可用策略
  getAvailableStrategies(): { type: RecognitionType; method: RecognitionMethod }[] {
    return this.strategyFactory.getAllStrategies().map(strategy => ({
      type: strategy.getType(),
      method: strategy.getMethod()
    }));
  }

  // 清理资源
  dispose() {
    this.stateListeners.length = 0;
    this.updateState({
      isProcessing: false,
      error: null,
      processingStep: '',
      progress: 0
    });
  }
}

// 默认配置
export const defaultRecognitionConfig: RecognitionConfig = {
  enableAutoDetection: true,
  defaultMethod: 'auto',
  maxImages: 5,
  timeout: 30000,
  enablePersonalization: true
};

// 单例实例
export const unifiedRecognitionService = new UnifiedRecognitionService(defaultRecognitionConfig);
