import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { VitePWA } from 'vite-plugin-pwa'
import tailwindcss from '@tailwindcss/vite'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  server: {
    host: true // 或 '0.0.0.0'
  },
  plugins: [
    tailwindcss(),
    react(),
    VitePWA({
      registerType: 'prompt',
      includeAssets: ['favicon.svg', 'apple-touch-icon.png'],
      devOptions: {
        enabled: true
      },
      manifest: {
        name: 'KCal Tracker - AI智能卡路里追踪',
        short_name: 'KCal Tracker',
        description: 'AI驱动的智能卡路里追踪应用，助力科学减重',
        theme_color: '#ffffff',
        background_color: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
        start_url: '/',
        scope: '/',
        lang: 'zh-CN',
        categories: ['health', 'lifestyle', 'productivity'],
        dir: 'ltr',
        icons: [
          {
            src: '/icon.svg',
            sizes: 'any',
            type: 'image/svg+xml',
            purpose: 'any maskable'
          },
          {
            src: 'pwa-64x64.png',
            sizes: '64x64',
            type: 'image/png'
          },
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
        cleanupOutdatedCaches: true,
        skipWaiting: true,
        clientsClaim: true,
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/api-proxy\.me\/gemini/,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'gemini-api',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 300
              },
              networkTimeoutSeconds: 10
            }
          },
          {
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 30 * 24 * 60 * 60 // 30 days
              }
            }
          },
          {
            urlPattern: /\.(?:js|css)$/,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'static-resources'
            }
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    // {{ AURA-X: Modify - 优化构建配置，启用代码压缩和混淆. Approval: 寸止(ID:1737100000). }}
    chunkSizeWarningLimit: 1000,
    minify: 'terser', // 使用terser进行代码压缩和混淆
    terserOptions: {
      compress: {
        drop_console: true, // 移除console.log
        drop_debugger: true, // 移除debugger
        pure_funcs: ['console.log', 'console.info', 'console.debug'], // 移除指定函数
      },
      mangle: {
        toplevel: true, // 混淆顶级作用域
        safari10: true, // 兼容Safari 10
      },
      format: {
        comments: false, // 移除注释
      },
    },
    rollupOptions: {
      output: {
        // 文件名混淆
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks: (id) => {
          // React核心库
          if (id.includes('react') || id.includes('react-dom')) {
            return 'vendor';
          }
          // UI组件库
          if (id.includes('@headlessui') || id.includes('@heroicons') || id.includes('daisyui')) {
            return 'ui';
          }
          // 图表库
          if (id.includes('chart.js') || id.includes('react-chartjs-2') || id.includes('recharts')) {
            return 'charts';
          }
          // 工具库
          if (id.includes('date-fns') || id.includes('zustand') || id.includes('clsx')) {
            return 'utils';
          }
          // 动画库
          if (id.includes('animejs') || id.includes('@/utils/animations')) {
            return 'animations';
          }
          // 路由库
          if (id.includes('react-router')) {
            return 'router';
          }
          // node_modules中的其他库
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      }
    },
    // 启用源码映射（生产环境可设为false）
    sourcemap: false,
    // 启用代码分割
    cssCodeSplit: true,
    // 设置构建目标
    target: 'es2015'
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: ['react', 'react-dom', 'zustand', 'date-fns']
  }
})
