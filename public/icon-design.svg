<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- {{ AURA-X: Modify - 重新设计kcal主题图标，采用圆角矩形风格. Approval: 寸止(ID:1737100000). }} -->
  <!-- 渐变定义 -->
  <defs>
    <!-- 主背景渐变 - DaisyUI绿色系 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <!-- 标签背景渐变 -->
    <linearGradient id="labelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:0.9" />
    </linearGradient>

    <!-- 能量波纹渐变 -->
    <radialGradient id="energyGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#34d399;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#10b981;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.1" />
    </radialGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>

    <!-- 内阴影滤镜 -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- 主背景 - 圆角矩形 -->
  <rect x="32" y="32" width="448" height="448" rx="96" ry="96" fill="url(#bgGradient)" filter="url(#shadow)"/>

  <!-- 能量波纹效果 -->
  <circle cx="256" cy="256" r="180" fill="url(#energyGradient)" opacity="0.6"/>
  <circle cx="256" cy="256" r="140" fill="url(#energyGradient)" opacity="0.4"/>
  <circle cx="256" cy="256" r="100" fill="url(#energyGradient)" opacity="0.3"/>

  <!-- 中央卡路里标签容器 -->
  <rect x="156" y="206" width="200" height="100" rx="20" ry="20" fill="url(#labelGradient)" filter="url(#innerShadow)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>

  <!-- 装饰性能量条 -->
  <rect x="80" y="180" width="60" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.7)"/>
  <rect x="80" y="200" width="45" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.5)"/>
  <rect x="80" y="220" width="70" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.6)"/>

  <rect x="372" y="280" width="60" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.7)"/>
  <rect x="387" y="300" width="45" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.5)"/>
  <rect x="367" y="320" width="70" height="8" rx="4" ry="4" fill="rgba(255,255,255,0.6)"/>

  <!-- 能量闪电图标 -->
  <g transform="translate(230, 130)">
    <!-- 主闪电 -->
    <path d="M26 10 L16 35 L24 35 L20 60 L30 35 L22 35 L26 10 Z"
          fill="rgba(255,255,255,0.95)" stroke="rgba(255,255,255,0.8)" stroke-width="1" opacity="0.9"/>
    <!-- 能量光晕 -->
    <circle cx="23" cy="35" r="25" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="2" opacity="0.6"/>
    <circle cx="23" cy="35" r="18" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1.5" opacity="0.5"/>
  </g>

  <!-- 数字显示区域装饰 -->
  <g transform="translate(100, 350)">
    <!-- 小型数据指示器 -->
    <rect x="0" y="0" width="40" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.6)"/>
    <rect x="50" y="0" width="60" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.4)"/>
    <rect x="120" y="0" width="35" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.7)"/>

    <rect x="200" y="0" width="40" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.6)"/>
    <rect x="250" y="0" width="60" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.4)"/>
    <rect x="320" y="0" width="35" height="6" rx="3" ry="3" fill="rgba(255,255,255,0.7)"/>
  </g>

  <!-- 主标题 - 能量追踪 -->
  <text x="256" y="255" text-anchor="middle" dominant-baseline="central" font-family="system-ui, -apple-system, sans-serif"
        font-size="32" font-weight="800" fill="#047857" letter-spacing="1px">能量追踪</text>
</svg>
