<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <!-- {{ AURA-X: Modify - 复用icon-design.svg设计到favicon. Approval: 寸止(ID:1737100000). }} -->
  <defs>
    <!-- 主背景渐变 - DaisyUI绿色系 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <!-- 标签背景渐变 -->
    <linearGradient id="labelGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:0.9" />
    </linearGradient>

    <!-- 能量波纹渐变 -->
    <radialGradient id="energyGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#34d399;stop-opacity:0.8" />
      <stop offset="70%" style="stop-color:#10b981;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.1" />
    </radialGradient>
  </defs>

  <!-- 主背景 - 圆角矩形 -->
  <rect x="2" y="2" width="28" height="28" rx="6" ry="6" fill="url(#bgGradient)"/>

  <!-- 能量波纹效果 -->
  <circle cx="16" cy="16" r="11" fill="url(#energyGradient)" opacity="0.6"/>
  <circle cx="16" cy="16" r="8" fill="url(#energyGradient)" opacity="0.4"/>

  <!-- 中央标签容器 -->
  <rect x="6" y="11" width="20" height="10" rx="2" ry="2" fill="url(#labelGradient)" stroke="rgba(255,255,255,0.3)" stroke-width="0.3"/>

  <!-- 能量闪电图标 -->
  <g transform="translate(12, 6)">
    <!-- 小闪电 -->
    <path d="M2 1 L1 3 L2 3 L1.5 5 L2.5 3 L1.5 3 L2 1 Z"
          fill="rgba(255,255,255,0.95)" opacity="0.9"/>
  </g>

  <!-- 主标题 - 能量追踪 -->
  <text x="16" y="17" text-anchor="middle" dominant-baseline="central" font-family="system-ui, -apple-system, sans-serif"
        font-size="3.5" font-weight="800" fill="#047857">能量追踪</text>
</svg>
