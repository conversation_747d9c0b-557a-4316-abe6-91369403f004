# 常用模式和最佳实践

- 移动端优化最佳实践：1.使用响应式文字hidden sm:inline显示不同内容 2.触控区域最小44px 3.网格布局grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 4.间距space-y-4 sm:space-y-6渐进式增加
- 响应式宽度对齐最佳实践：使用统一的max-w断点类名（max-w-md lg:max-w-lg xl:max-w-xl）确保不同元素在各屏幕尺寸下保持一致的视觉宽度，避免w-full无限制导致的对齐问题
- 2025年现代化UI设计最佳实践：bg-white/80 backdrop-blur-sm毛玻璃效果，shadow-xl阴影，border border-white/20边框，渐变背景from-indigo-50 via-white to-purple-50，emoji图标增强视觉效果，stats组件展示数据，badge组件标签，btn-lg h-16大按钮设计
- Anime.js v4移动端动画最佳实践：createScope创建动画作用域，支持媒体查询检测，prefers-reduced-motion无障碍支持，stagger错开动画，页面加载动画，卡片hover动画，按钮点击反馈动画，60fps性能优化
- 实时日期时间组件最佳实践：useState管理当前时间，setInterval每秒更新，toLocaleDateString中文格式化，font-mono等宽字体，time-digit类名配合Anime.js动画，毛玻璃卡片设计，响应式文字大小
- 炫酷UI设计最佳实践：渐变背景装饰圆形元素，毛玻璃backdrop-blur-sm效果，进度条可视化数据，emoji图标圆形容器，bg-clip-text渐变文字，相对定位装饰元素，shadow-lg阴影层次，border渐变边框，Math.round整数显示
- 进度条数据驱动最佳实践：Math.min((actual/target)*100,100)%动态计算，营养素合理目标值(蛋白质150g脂肪80g碳水300g纤维35g)，代谢数据目标值(BMR2500TDEE3500)，避免固定百分比装饰性进度条
- 营养建议组件现代化重构最佳实践：emoji图标替代Heroicons，渐变背景色彩主题，毛玻璃图标容器，背景装饰元素，hover缩放动画，响应式文字大小，Math.round整数显示，移除复杂依赖简化组件
- 卡片设计统一最佳实践：避免双层包装设计，直接使用渐变背景卡片，移除不必要的白色毛玻璃包装层，保持图标容器的一致性设计，简化组件导入减少依赖，统一视觉层次和设计语言
- 底部导航栏组件最佳实践：fixed bottom-0定位，bg-white/90 backdrop-blur-sm毛玻璃效果，grid grid-cols-3三等分布局，h-16确保44px触控区域，点击缩放动画，活跃状态高亮，iOS安全区域适配，z-50层级管理
- 简洁列表式UI设计最佳实践：使用border-b分割线替代卡片包装，flex布局emoji图标+内容，统一色彩主题避免过度装饰，三餐数据显示格式实际/目标(百分比%)，Math.min限制进度条最大100%
- DaisyUI dock组件最佳实践：使用dock类替代自定义grid布局，dock-active管理活跃状态，dock-label显示按钮标签，fixed定位确保固定显示，backdrop-blur-sm毛玻璃效果，z-50层级管理，iOS安全区域适配
- 原生App底部Tab导航最佳实践：fixed定位到视口底部，env(safe-area-inset-bottom)全面屏适配，backdrop-filter毛玻璃效果，touch-manipulation触控优化，DaisyUI dock组件，dock-active状态管理，拇指触手可及的位置设计
- 底部导航栏Context7最佳实践：position:fixed bottom:0确保真正固定，backdrop-filter:blur(12px)毛玻璃效果，env(safe-area-inset-bottom)安全区域适配，touch-action:manipulation触控优化，z-index:50层级管理，DaisyUI dock组件集成，完全删除重建确保代码质量
- CSS fixed定位问题排查最佳实践：检查父容器transform属性影响，PageTransition组件的transform-gpu会创建层叠上下文，使用!important确保CSS优先级，动画完成后移除transform避免持续影响，z-index使用9999确保最高层级
- Context7底部导航栏最佳实践：避免React Portal和原生DOM操作，使用fixed bottom-0 inset-x-0 z-50定位，Tailwind CSS类替代内联样式，简洁的flex布局，backdrop-blur-sm毛玻璃效果，pb-safe安全区域适配，声明式组件树渲染
- Context7移动端底部导航栏最佳实践：position:fixed+内联样式确保定位，max(env(safe-area-inset-bottom), 8px)安全区域，min-w-[56px] min-h-[44px]触控标准，onTouchStart触控反馈，backdrop-blur-xl毛玻璃，shadow-2xl阴影，touch-manipulation优化，aria-label无障碍
- 数字输入优化最佳实践：-webkit-appearance:none移除步进器，-moz-appearance:textfield兼容Firefox，::-webkit-inner-spin-button隐藏控件，统一min-w-[64px] min-h-[52px]按钮尺寸，hover:scale-105微交互，duration-300流畅动画
- 智能推荐算法最佳实践：基于BMI健康范围18.5-24.9，活动水平系数sedentary:0.8到veryActive:1.6，时间因子weeksAvailable*adjustedWeeklyLoss，合理边界Math.max(healthyMinWeight)和Math.min(currentWeight)，用户友好的至少减重2kg保证
- CSS层级冲突解决最佳实践：使用32位整数最大值**********作为绝对最高z-index，position:fixed!important强制定位，display:block!important和visibility:visible!important确保可见性，全局CSS选择器nav[style*="zIndex"]提供额外保障，限制其他元素z-index避免冲突
- 底部导航栏层叠上下文冲突完整解决方案标准化实施：1.组件结构重构-将BottomNavigation移到根级别脱离Anime.js transform影响；2.绝对层级保障-使用z-index:**********和!important强制优先级；3.全局CSS多重保障-bottom-navigation类选择器和transform容器特殊处理；4.跨页面一致性-Dashboard和CalendarPage都采用相同结构；5.安全区域适配-env(safe-area-inset-bottom)支持全面屏设备
- 运动记录卡片布局优化：强度标签已移动到标题右侧，与食物记录卡片布局保持完全一致。运动记录使用红色卡路里显示(-XXX卡路里)表示消耗，食物记录保持原色表示摄入。所有UI组件已统一使用DaisyUI 5.0.46设计规范，实现了完整的视觉一致性。
- 运动记录系统优化完成：1.移除运动详情基础信息中的识别准确度显示；2.扩展ExerciseRecord数据模型添加recommendations字段存储AI建议；3.Dashboard今日卡路里区域改为3列布局显示摄入/消耗/目标；4.运动详情模态框添加AI建议展示区域，使用蓝色渐变背景和HTML渲染。严格遵循AURA-X协议，所有决策通过寸止MCP确认。
- 运动记录系统6项优化完成：1.今日卡路里进度条纳入运动消耗计算(净摄入概念)；2.已编辑标签改为内联橙色文字显示；3.运动识别状态文案改为"正在分析运动中"；4.OpenAI和Gemini API超时延长到2分钟；5.食物详情模态框单位显示修复，使用foodRecord.unit而非aiRecognition.unit；6.运动AI提示词已支持HTML格式输出。严格遵循AURA-X协议执行。
- UI优化4项任务完成：1.食物记录时间显示改为"添加于 XX:XX · 已编辑"格式，使用中点符号分隔；2.食物详情模态框"重量"标签改为"单位"标签；3.运动建议HTML效果优化，增强prose样式类；4.Dashboard运动卡片添加"查看AI运动建议"按钮，参考营养详情实现，包含模态框状态管理。严格遵循AURA-X协议执行。
- AI建议功能优化完成：1.移除FoodRecord页面运动记录卡片的AI建议按钮；2.Dashboard运动卡片AI建议按钮统一样式，使用运动主题配色(红-橙渐变)；3.实现Dashboard AI运动建议功能，包含专门的AI提示词分析个人信息+运动记录+营养详情，生成个性化减肥运动建议；4.在AIProvider接口和OpenAI/Gemini提供商中添加generateText方法支持文本生成。严格遵循AURA-X协议执行。
- AI建议功能修复完成：1.运动建议支持终止分析功能，使用独立的isExerciseAnalyzing状态和exerciseAbortController；2.修复AI建议状态冲突问题，营养建议使用isAnalyzing，运动建议使用isExerciseAnalyzing；3.在AIProvider接口和OpenAI/Gemini提供商中添加AbortSignal支持，实现可取消的AI请求；4.Dashboard运动建议按钮支持取消分析，显示"取消分析"按钮。严格遵循AURA-X协议执行。
- AI建议功能修复完成：1.修复AI运动建议API请求地址问题，OpenAI提供商的generateText方法改为使用buildApiUrl确保包含/v1路径；2.统一AI营养建议和运动建议的终止分析按钮样式，都使用灰色渐变背景和旋转齿轮图标(⚙️)；3.修复食物记录编辑时单位数值不正确问题，编辑时显示unitValue而非weight，确保与查看详情一致。严格遵循AURA-X协议执行。
- AI运动建议优化完成：1.修改取消分析按钮图标为loading spinner，营养建议和运动建议都统一使用白底旋转进度图标；2.AI运动建议展示方式改为直接在运动卡片下方展示，移除模态框逻辑，参考营养建议样式实现；3.AI运动建议提示词精简，要求返回3-5个核心要点，内容更加简洁实用。严格遵循AURA-X协议执行。
- UI样式统一优化完成：1.取消分析样式同步到食物识别和运动识别模态框，都使用灰色渐变背景和loading spinner图标；2.AI运动建议配色从蓝色系改为红色系，使用红-橙-粉渐变背景，文字颜色也相应调整为红色系，与运动卡片主题保持一致。严格遵循AURA-X协议执行。
- 功能修复和优化完成：1.移除Dashboard运动卡片中的"平均 25 分钟/次"显示，简化运动时长统计；2.修复Toast组件文字竖直显示问题，使用whitespace-nowrap确保水平显示；3.在FoodRecord页面添加体重管理卡片，显示当前体重、待减重量、本周变化；4.修复食物记录编辑保存数据消失问题，完善updateFoodRecord调用参数包含所有必要字段。严格遵循AURA-X协议执行。
- 体重管理和数据修复完成：1.体重管理卡片位置调整到日期选择器下方，跟随日期变化；2.修复食物记录删除和保存后列表消失问题，修复FoodNutritionModal中editedAt字段错误和nutritionStore中updateFoodRecord的数据冲突；3.体重管理卡片图标优化为白色SVG图标，适配紫色渐变背景；4.实现体重更新模态窗功能，支持手动修改体重记录并关联到选择的日期。严格遵循AURA-X协议执行。
- UI交互和数据修复完成：1.修复日期导航按钮触发下拉菜单问题，添加e.preventDefault()和e.stopPropagation()确保事件正确处理；2.体重管理变化指标优化，将"本周变化"改为"与昨日变化"，显示"--"表示无数据；3.首页和个人页面图标颜色统一，将ProfilePage中的SVG图标改为text-white确保在各种背景下清晰可见；4.修复食物记录编辑单位后数据消失问题，在FoodNutritionModal的handleInputChange中同步更新unitValue和weight字段保持数据一致性。严格遵循AURA-X协议执行。
