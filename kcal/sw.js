if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(n,r)=>{const c=e||("document"in self?document.currentScript.src:"")||location.href;if(s[c])return;let o={};const t=e=>i(e,c),a={module:{uri:c},exports:o,require:t};s[c]=Promise.all(n.map(e=>a[e]||t(e))).then(e=>(r(...e),o))}}define(["./workbox-1f723fb5"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/animations-Cpx74AEx.js",revision:null},{url:"assets/animations-DumLLhGv.js",revision:null},{url:"assets/charts-DTefc40_.js",revision:null},{url:"assets/index-BQVKYfGr.css",revision:null},{url:"assets/index-g8oD4Z5-.js",revision:null},{url:"assets/utils-DXIKCEYF.js",revision:null},{url:"assets/vendor-eYbLVx36.js",revision:null},{url:"create-png-icons.js",revision:"235600728910a2c4afdb8a79403a8558"},{url:"favicon.svg",revision:"d089f99d049e9b4c5bc1a08878b57f77"},{url:"generate-icons.html",revision:"6acc760f81138a5f2975d70bf25d3fe5"},{url:"icon-design.svg",revision:"832076d796c201adda968fbfb3847d67"},{url:"icon-generator.html",revision:"e2f2fc923ce1cbefe2c85aa0175880fd"},{url:"icon.svg",revision:"a7edd17431fc19356c89948e7c007b5f"},{url:"index.html",revision:"e676ca91d9a07ed6a6040e59278c4988"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"vite.svg",revision:"8e3a10e157f75ada21ab742c022d5430"},{url:"favicon.svg",revision:"d089f99d049e9b4c5bc1a08878b57f77"},{url:"icon.svg",revision:"a7edd17431fc19356c89948e7c007b5f"},{url:"manifest.webmanifest",revision:"9215e1064bbc88d437e7e3b98b2612f2"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/api-proxy\.me\/gemini/,new e.NetworkFirst({cacheName:"gemini-api",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:50,maxAgeSeconds:300})]}),"GET"),e.registerRoute(/\.(?:png|jpg|jpeg|svg|gif|webp)$/,new e.CacheFirst({cacheName:"images",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:2592e3})]}),"GET"),e.registerRoute(/\.(?:js|css)$/,new e.StaleWhileRevalidate({cacheName:"static-resources",plugins:[]}),"GET")});
