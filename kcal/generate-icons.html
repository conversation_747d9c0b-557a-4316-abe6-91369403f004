<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KCal Tracker - 图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0fdf4;
        }
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #e5e7eb;
            border-radius: 4px;
        }
        .download-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .download-btn:hover {
            background: #059669;
        }
        .instructions {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>KCal Tracker 图标生成器</h1>
    
    <div class="instructions">
        <h3>使用说明：</h3>
        <p>1. 点击下方的"生成图标"按钮</p>
        <p>2. 右键点击每个图标，选择"另存为"</p>
        <p>3. 将文件保存为对应的文件名（如 favicon.ico, pwa-192x192.png 等）</p>
        <p>4. 将生成的图标文件放入 public 目录</p>
    </div>

    <button onclick="generateIcons()" style="padding: 12px 24px; background: #10b981; color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-bottom: 20px;">
        生成图标
    </button>

    <div class="icon-container" id="iconContainer">
        <!-- 图标将在这里生成 -->
    </div>

    <script>
        function generateIcons() {
            const sizes = [
                { size: 16, name: 'favicon-16x16.png' },
                { size: 32, name: 'favicon-32x32.png' },
                { size: 64, name: 'pwa-64x64.png' },
                { size: 192, name: 'pwa-192x192.png' },
                { size: 512, name: 'pwa-512x512.png' }
            ];

            const container = document.getElementById('iconContainer');
            container.innerHTML = '';

            sizes.forEach(({ size, name }) => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';

                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                
                const ctx = canvas.getContext('2d');
                drawIcon(ctx, size);

                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                label.style.fontWeight = 'bold';
                label.style.marginBottom = '5px';

                const filename = document.createElement('div');
                filename.textContent = name;
                filename.style.fontSize = '12px';
                filename.style.color = '#666';

                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadIcon(canvas, name);

                iconItem.appendChild(label);
                iconItem.appendChild(filename);
                iconItem.appendChild(canvas);
                iconItem.appendChild(downloadBtn);
                container.appendChild(iconItem);
            });
        }

        function drawIcon(ctx, size) {
            const scale = size / 512;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const bgGradient = ctx.createLinearGradient(0, 0, size, size);
            bgGradient.addColorStop(0, '#10b981');
            bgGradient.addColorStop(1, '#14b8a6');
            
            // 绘制背景圆形
            ctx.fillStyle = bgGradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 * 0.9, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制盘子
            const plateGradient = ctx.createLinearGradient(0, 0, size, size);
            plateGradient.addColorStop(0, 'rgba(255,255,255,0.95)');
            plateGradient.addColorStop(1, 'rgba(240,253,244,0.9)');
            
            ctx.fillStyle = plateGradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2 + size*0.05, size*0.31, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制食物元素
            // 主菜
            ctx.fillStyle = 'rgba(34, 197, 94, 0.8)';
            ctx.beginPath();
            ctx.ellipse(size*0.43, size*0.51, size*0.09, size*0.07, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 蔬菜
            ctx.fillStyle = 'rgba(34, 197, 94, 0.7)';
            ctx.beginPath();
            ctx.arc(size*0.59, size*0.47, size*0.05, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'rgba(22, 163, 74, 0.7)';
            ctx.beginPath();
            ctx.arc(size*0.625, size*0.53, size*0.04, 0, 2 * Math.PI);
            ctx.fill();
            
            // 碳水化合物
            ctx.fillStyle = 'rgba(251, 191, 36, 0.8)';
            ctx.beginPath();
            ctx.ellipse(size*0.47, size*0.625, size*0.07, size*0.05, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // AI 元素
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.lineWidth = size * 0.006;
            ctx.beginPath();
            ctx.arc(size*0.44, size*0.33, size*0.08, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 神经网络点
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const points = [
                [size*0.38, size*0.31],
                [size*0.44, size*0.29],
                [size*0.50, size*0.31],
                [size*0.41, size*0.37],
                [size*0.47, size*0.37]
            ];
            
            points.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, size*0.006, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // kcal 文字
            if (size >= 64) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.font = `bold ${size*0.047}px Arial, sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillText('kcal', size/2, size*0.38);
            }
        }

        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
    </script>
</body>
</html>
