<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981"/>
      <stop offset="100%" style="stop-color:#14b8a6"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <circle cx="16" cy="16" r="15" fill="url(#bg)"/>
  
  <!-- 盘子 -->
  <circle cx="16" cy="18" r="10" fill="rgba(255,255,255,0.9)" stroke="#e5e7eb" stroke-width="0.5"/>
  
  <!-- 食物元素 -->
  <ellipse cx="14" cy="17" rx="3" ry="2" fill="rgba(34,197,94,0.8)"/>
  <circle cx="19" cy="16" r="1.5" fill="rgba(34,197,94,0.7)"/>
  <circle cx="20" cy="18" r="1.2" fill="rgba(22,163,74,0.7)"/>
  <ellipse cx="15" cy="20" rx="2.5" ry="1.5" fill="rgba(251,191,36,0.8)"/>
  
  <!-- AI 元素 -->
  <circle cx="16" cy="10" r="3" fill="none" stroke="rgba(255,255,255,0.9)" stroke-width="0.8"/>
  <circle cx="14.5" cy="9.5" r="0.4" fill="rgba(255,255,255,0.8)"/>
  <circle cx="16" cy="9" r="0.4" fill="rgba(255,255,255,0.8)"/>
  <circle cx="17.5" cy="9.5" r="0.4" fill="rgba(255,255,255,0.8)"/>
  
  <!-- kcal 文字 -->
  <text x="16" y="25" text-anchor="middle" font-family="Arial" font-size="4" font-weight="bold" fill="rgba(255,255,255,0.9)">kcal</text>
</svg>
