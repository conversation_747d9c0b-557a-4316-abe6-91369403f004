<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#22c55e" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="KCal Tracker" />
    <meta name="description" content="AI驱动的智能卡路里追踪应用" />
    <meta name="keywords" content="卡路里,减肥,健康,AI,食物识别" />

    <!-- 全面屏适配 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#22c55e" />
    <meta name="msapplication-config" content="none" />

    <!-- PWA相关 -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <title>KCal Tracker - AI卡路里追踪</title>
    <script type="module" crossorigin src="/assets/index-g8oD4Z5-.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/utils-DXIKCEYF.js">
    <link rel="modulepreload" crossorigin href="/assets/charts-DTefc40_.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-eYbLVx36.js">
    <link rel="modulepreload" crossorigin href="/assets/animations-DumLLhGv.js">
    <link rel="stylesheet" crossorigin href="/assets/index-BQVKYfGr.css">
  <link rel="manifest" href="/manifest.webmanifest"><script id="vite-plugin-pwa:register-sw" src="/registerSW.js"></script></head>
  <body>
    <div id="root"></div>
  </body>
</html>
