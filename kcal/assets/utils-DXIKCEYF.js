const K=r=>{let e;const t=new Set,n=(c,f)=>{const h=typeof c=="function"?c(e):c;if(!Object.is(h,e)){const k=e;e=f??(typeof h!="object"||h===null)?h:Object.assign({},e,h),t.forEach(T=>T(e,k))}},a=()=>e,i={setState:n,getState:a,getInitialState:()=>y,subscribe:c=>(t.add(c),()=>t.delete(c))},y=e=r(n,a,i);return i},Jn=r=>r?K(r):K;function xe(r,e){let t;try{t=r()}catch{return}return{getItem:a=>{var s;const o=y=>y===null?null:JSON.parse(y,void 0),i=(s=t.getItem(a))!=null?s:null;return i instanceof Promise?i.then(o):o(i)},setItem:(a,s)=>t.setItem(a,JSON.stringify(s,void 0)),removeItem:a=>t.removeItem(a)}}const A=r=>e=>{try{const t=r(e);return t instanceof Promise?t:{then(n){return A(n)(t)},catch(n){return this}}}catch(t){return{then(n){return this},catch(n){return A(n)(t)}}}},Pe=(r,e)=>(t,n,a)=>{let s={storage:xe(()=>localStorage),partialize:m=>m,version:0,merge:(m,u)=>({...u,...m}),...e},o=!1;const i=new Set,y=new Set;let c=s.storage;if(!c)return r((...m)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),t(...m)},n,a);const f=()=>{const m=s.partialize({...n()});return c.setItem(s.name,{state:m,version:s.version})},h=a.setState;a.setState=(m,u)=>{h(m,u),f()};const k=r((...m)=>{t(...m),f()},n,a);a.getInitialState=()=>k;let T;const H=()=>{var m,u;if(!c)return;o=!1,i.forEach(g=>{var v;return g((v=n())!=null?v:k)});const w=((u=s.onRehydrateStorage)==null?void 0:u.call(s,(m=n())!=null?m:k))||void 0;return A(c.getItem.bind(c))(s.name).then(g=>{if(g)if(typeof g.version=="number"&&g.version!==s.version){if(s.migrate){const v=s.migrate(g.state,g.version);return v instanceof Promise?v.then(N=>[!0,N]):[!0,v]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,g.state];return[!1,void 0]}).then(g=>{var v;const[N,R]=g;if(T=s.merge(R,(v=n())!=null?v:k),t(T,!0),N)return f()}).then(()=>{w?.(T,void 0),T=n(),o=!0,y.forEach(g=>g(T))}).catch(g=>{w?.(void 0,g)})};return a.persist={setOptions:m=>{s={...s,...m},m.storage&&(c=m.storage)},clearStorage:()=>{c?.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>H(),hasHydrated:()=>o,onHydrate:m=>(i.add(m),()=>{i.delete(m)}),onFinishHydration:m=>(y.add(m),()=>{y.delete(m)})},s.skipHydration||H(),T||k},Zn=Pe,se=6048e5,Me=864e5,De=6e4,ke=36e5,ve=1e3,U=Symbol.for("constructDateFrom");function D(r,e){return typeof r=="function"?r(e):r&&typeof r=="object"&&U in r?r[U](e):r instanceof Date?new r.constructor(e):new Date(e)}function p(r,e){return D(e||r,r)}function oe(r,e,t){const n=p(r,t?.in);return isNaN(e)?D(t?.in||r,NaN):(e&&n.setDate(n.getDate()+e),n)}let Te={};function F(){return Te}function E(r,e){const t=F(),n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,a=p(r,e?.in),s=a.getDay(),o=(s<n?7:0)+s-n;return a.setDate(a.getDate()-o),a.setHours(0,0,0,0),a}function Q(r,e){return E(r,{...e,weekStartsOn:1})}function ie(r,e){const t=p(r,e?.in),n=t.getFullYear(),a=D(t,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);const s=Q(a),o=D(t,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const i=Q(o);return t.getTime()>=s.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function $(r){const e=p(r),t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+r-+t}function B(r,...e){const t=D.bind(null,r||e.find(n=>typeof n=="object"));return e.map(t)}function X(r,e){const t=p(r,e?.in);return t.setHours(0,0,0,0),t}function Oe(r,e,t){const[n,a]=B(t?.in,r,e),s=X(n),o=X(a),i=+s-$(s),y=+o-$(o);return Math.round((i-y)/Me)}function Ye(r,e){const t=ie(r,e),n=D(r,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),Q(n)}function Kn(r,e,t){const[n,a]=B(t?.in,r,e);return+X(n)==+X(a)}function We(r){return r instanceof Date||typeof r=="object"&&Object.prototype.toString.call(r)==="[object Date]"}function _e(r){return!(!We(r)&&typeof r!="number"||isNaN(+p(r)))}function Un(r,e){const t=p(r,e?.in);return t.setHours(23,59,59,999),t}function er(r,e){const t=p(r,e?.in),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function tr(r,e){const t=p(r,e?.in);return t.setDate(1),t.setHours(0,0,0,0),t}function Ee(r,e){const t=p(r,e?.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function nr(r,e){const t=F(),n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,a=p(r,e?.in),s=a.getDay(),o=(s<n?-7:0)+6-(s-n);return a.setDate(a.getDate()+o),a.setHours(23,59,59,999),a}const He={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},qe=(r,e,t)=>{let n;const a=He[r];return typeof a=="string"?n=a:e===1?n=a.one:n=a.other.replace("{{count}}",e.toString()),t?.addSuffix?t.comparison&&t.comparison>0?"in "+n:n+" ago":n};function L(r){return(e={})=>{const t=e.width?String(e.width):r.defaultWidth;return r.formats[t]||r.formats[r.defaultWidth]}}const Ne={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Ie={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Fe={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Ce={date:L({formats:Ne,defaultWidth:"full"}),time:L({formats:Ie,defaultWidth:"full"}),dateTime:L({formats:Fe,defaultWidth:"full"})},Le={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Qe=(r,e,t,n)=>Le[r];function O(r){return(e,t)=>{const n=t?.context?String(t.context):"standalone";let a;if(n==="formatting"&&r.formattingValues){const o=r.defaultFormattingWidth||r.defaultWidth,i=t?.width?String(t.width):o;a=r.formattingValues[i]||r.formattingValues[o]}else{const o=r.defaultWidth,i=t?.width?String(t.width):r.defaultWidth;a=r.values[i]||r.values[o]}const s=r.argumentCallback?r.argumentCallback(e):e;return a[s]}}const Re={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},$e={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Xe={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Ge={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Be={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Ae={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},je=(r,e)=>{const t=Number(r),n=t%100;if(n>20||n<10)switch(n%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},Ve={ordinalNumber:je,era:O({values:Re,defaultWidth:"wide"}),quarter:O({values:$e,defaultWidth:"wide",argumentCallback:r=>r-1}),month:O({values:Xe,defaultWidth:"wide"}),day:O({values:Ge,defaultWidth:"wide"}),dayPeriod:O({values:Be,defaultWidth:"wide",formattingValues:Ae,defaultFormattingWidth:"wide"})};function Y(r){return(e,t={})=>{const n=t.width,a=n&&r.matchPatterns[n]||r.matchPatterns[r.defaultMatchWidth],s=e.match(a);if(!s)return null;const o=s[0],i=n&&r.parsePatterns[n]||r.parsePatterns[r.defaultParseWidth],y=Array.isArray(i)?Se(i,h=>h.test(o)):ze(i,h=>h.test(o));let c;c=r.valueCallback?r.valueCallback(y):y,c=t.valueCallback?t.valueCallback(c):c;const f=e.slice(o.length);return{value:c,rest:f}}}function ze(r,e){for(const t in r)if(Object.prototype.hasOwnProperty.call(r,t)&&e(r[t]))return t}function Se(r,e){for(let t=0;t<r.length;t++)if(e(r[t]))return t}function ue(r){return(e,t={})=>{const n=e.match(r.matchPattern);if(!n)return null;const a=n[0],s=e.match(r.parsePattern);if(!s)return null;let o=r.valueCallback?r.valueCallback(s[0]):s[0];o=t.valueCallback?t.valueCallback(o):o;const i=e.slice(a.length);return{value:o,rest:i}}}const Je=/^(\d+)(th|st|nd|rd)?/i,Ze=/\d+/i,Ke={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Ue={any:[/^b/i,/^(a|c)/i]},et={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},tt={any:[/1/i,/2/i,/3/i,/4/i]},nt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},rt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},at={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},st={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},ot={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},it={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},ut={ordinalNumber:ue({matchPattern:Je,parsePattern:Ze,valueCallback:r=>parseInt(r,10)}),era:Y({matchPatterns:Ke,defaultMatchWidth:"wide",parsePatterns:Ue,defaultParseWidth:"any"}),quarter:Y({matchPatterns:et,defaultMatchWidth:"wide",parsePatterns:tt,defaultParseWidth:"any",valueCallback:r=>r+1}),month:Y({matchPatterns:nt,defaultMatchWidth:"wide",parsePatterns:rt,defaultParseWidth:"any"}),day:Y({matchPatterns:at,defaultMatchWidth:"wide",parsePatterns:st,defaultParseWidth:"any"}),dayPeriod:Y({matchPatterns:ot,defaultMatchWidth:"any",parsePatterns:it,defaultParseWidth:"any"})},ce={code:"en-US",formatDistance:qe,formatLong:Ce,formatRelative:Qe,localize:Ve,match:ut,options:{weekStartsOn:0,firstWeekContainsDate:1}};function ct(r,e){const t=p(r,e?.in);return Oe(t,Ee(t))+1}function de(r,e){const t=p(r,e?.in),n=+Q(t)-+Ye(t);return Math.round(n/se)+1}function z(r,e){const t=p(r,e?.in),n=t.getFullYear(),a=F(),s=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??a.firstWeekContainsDate??a.locale?.options?.firstWeekContainsDate??1,o=D(e?.in||r,0);o.setFullYear(n+1,0,s),o.setHours(0,0,0,0);const i=E(o,e),y=D(e?.in||r,0);y.setFullYear(n,0,s),y.setHours(0,0,0,0);const c=E(y,e);return+t>=+i?n+1:+t>=+c?n:n-1}function dt(r,e){const t=F(),n=e?.firstWeekContainsDate??e?.locale?.options?.firstWeekContainsDate??t.firstWeekContainsDate??t.locale?.options?.firstWeekContainsDate??1,a=z(r,e),s=D(e?.in||r,0);return s.setFullYear(a,0,n),s.setHours(0,0,0,0),E(s,e)}function le(r,e){const t=p(r,e?.in),n=+E(t,e)-+dt(t,e);return Math.round(n/se)+1}function l(r,e){const t=r<0?"-":"",n=Math.abs(r).toString().padStart(e,"0");return t+n}const q={y(r,e){const t=r.getFullYear(),n=t>0?t:1-t;return l(e==="yy"?n%100:n,e.length)},M(r,e){const t=r.getMonth();return e==="M"?String(t+1):l(t+1,2)},d(r,e){return l(r.getDate(),e.length)},a(r,e){const t=r.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(r,e){return l(r.getHours()%12||12,e.length)},H(r,e){return l(r.getHours(),e.length)},m(r,e){return l(r.getMinutes(),e.length)},s(r,e){return l(r.getSeconds(),e.length)},S(r,e){const t=e.length,n=r.getMilliseconds(),a=Math.trunc(n*Math.pow(10,t-3));return l(a,e.length)}},C={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},ee={G:function(r,e,t){const n=r.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return t.era(n,{width:"abbreviated"});case"GGGGG":return t.era(n,{width:"narrow"});case"GGGG":default:return t.era(n,{width:"wide"})}},y:function(r,e,t){if(e==="yo"){const n=r.getFullYear(),a=n>0?n:1-n;return t.ordinalNumber(a,{unit:"year"})}return q.y(r,e)},Y:function(r,e,t,n){const a=z(r,n),s=a>0?a:1-a;if(e==="YY"){const o=s%100;return l(o,2)}return e==="Yo"?t.ordinalNumber(s,{unit:"year"}):l(s,e.length)},R:function(r,e){const t=ie(r);return l(t,e.length)},u:function(r,e){const t=r.getFullYear();return l(t,e.length)},Q:function(r,e,t){const n=Math.ceil((r.getMonth()+1)/3);switch(e){case"Q":return String(n);case"QQ":return l(n,2);case"Qo":return t.ordinalNumber(n,{unit:"quarter"});case"QQQ":return t.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(n,{width:"wide",context:"formatting"})}},q:function(r,e,t){const n=Math.ceil((r.getMonth()+1)/3);switch(e){case"q":return String(n);case"qq":return l(n,2);case"qo":return t.ordinalNumber(n,{unit:"quarter"});case"qqq":return t.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(n,{width:"wide",context:"standalone"})}},M:function(r,e,t){const n=r.getMonth();switch(e){case"M":case"MM":return q.M(r,e);case"Mo":return t.ordinalNumber(n+1,{unit:"month"});case"MMM":return t.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(n,{width:"wide",context:"formatting"})}},L:function(r,e,t){const n=r.getMonth();switch(e){case"L":return String(n+1);case"LL":return l(n+1,2);case"Lo":return t.ordinalNumber(n+1,{unit:"month"});case"LLL":return t.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(n,{width:"wide",context:"standalone"})}},w:function(r,e,t,n){const a=le(r,n);return e==="wo"?t.ordinalNumber(a,{unit:"week"}):l(a,e.length)},I:function(r,e,t){const n=de(r);return e==="Io"?t.ordinalNumber(n,{unit:"week"}):l(n,e.length)},d:function(r,e,t){return e==="do"?t.ordinalNumber(r.getDate(),{unit:"date"}):q.d(r,e)},D:function(r,e,t){const n=ct(r);return e==="Do"?t.ordinalNumber(n,{unit:"dayOfYear"}):l(n,e.length)},E:function(r,e,t){const n=r.getDay();switch(e){case"E":case"EE":case"EEE":return t.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(n,{width:"short",context:"formatting"});case"EEEE":default:return t.day(n,{width:"wide",context:"formatting"})}},e:function(r,e,t,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(e){case"e":return String(s);case"ee":return l(s,2);case"eo":return t.ordinalNumber(s,{unit:"day"});case"eee":return t.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(a,{width:"short",context:"formatting"});case"eeee":default:return t.day(a,{width:"wide",context:"formatting"})}},c:function(r,e,t,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(e){case"c":return String(s);case"cc":return l(s,e.length);case"co":return t.ordinalNumber(s,{unit:"day"});case"ccc":return t.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(a,{width:"narrow",context:"standalone"});case"cccccc":return t.day(a,{width:"short",context:"standalone"});case"cccc":default:return t.day(a,{width:"wide",context:"standalone"})}},i:function(r,e,t){const n=r.getDay(),a=n===0?7:n;switch(e){case"i":return String(a);case"ii":return l(a,e.length);case"io":return t.ordinalNumber(a,{unit:"day"});case"iii":return t.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(n,{width:"short",context:"formatting"});case"iiii":default:return t.day(n,{width:"wide",context:"formatting"})}},a:function(r,e,t){const a=r.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(r,e,t){const n=r.getHours();let a;switch(n===12?a=C.noon:n===0?a=C.midnight:a=n/12>=1?"pm":"am",e){case"b":case"bb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(r,e,t){const n=r.getHours();let a;switch(n>=17?a=C.evening:n>=12?a=C.afternoon:n>=4?a=C.morning:a=C.night,e){case"B":case"BB":case"BBB":return t.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(r,e,t){if(e==="ho"){let n=r.getHours()%12;return n===0&&(n=12),t.ordinalNumber(n,{unit:"hour"})}return q.h(r,e)},H:function(r,e,t){return e==="Ho"?t.ordinalNumber(r.getHours(),{unit:"hour"}):q.H(r,e)},K:function(r,e,t){const n=r.getHours()%12;return e==="Ko"?t.ordinalNumber(n,{unit:"hour"}):l(n,e.length)},k:function(r,e,t){let n=r.getHours();return n===0&&(n=24),e==="ko"?t.ordinalNumber(n,{unit:"hour"}):l(n,e.length)},m:function(r,e,t){return e==="mo"?t.ordinalNumber(r.getMinutes(),{unit:"minute"}):q.m(r,e)},s:function(r,e,t){return e==="so"?t.ordinalNumber(r.getSeconds(),{unit:"second"}):q.s(r,e)},S:function(r,e){return q.S(r,e)},X:function(r,e,t){const n=r.getTimezoneOffset();if(n===0)return"Z";switch(e){case"X":return ne(n);case"XXXX":case"XX":return I(n);case"XXXXX":case"XXX":default:return I(n,":")}},x:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"x":return ne(n);case"xxxx":case"xx":return I(n);case"xxxxx":case"xxx":default:return I(n,":")}},O:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+te(n,":");case"OOOO":default:return"GMT"+I(n,":")}},z:function(r,e,t){const n=r.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+te(n,":");case"zzzz":default:return"GMT"+I(n,":")}},t:function(r,e,t){const n=Math.trunc(+r/1e3);return l(n,e.length)},T:function(r,e,t){return l(+r,e.length)}};function te(r,e=""){const t=r>0?"-":"+",n=Math.abs(r),a=Math.trunc(n/60),s=n%60;return s===0?t+String(a):t+String(a)+e+l(s,2)}function ne(r,e){return r%60===0?(r>0?"-":"+")+l(Math.abs(r)/60,2):I(r,e)}function I(r,e=""){const t=r>0?"-":"+",n=Math.abs(r),a=l(Math.trunc(n/60),2),s=l(n%60,2);return t+a+e+s}const re=(r,e)=>{switch(r){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},fe=(r,e)=>{switch(r){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},lt=(r,e)=>{const t=r.match(/(P+)(p+)?/)||[],n=t[1],a=t[2];if(!a)return re(r,e);let s;switch(n){case"P":s=e.dateTime({width:"short"});break;case"PP":s=e.dateTime({width:"medium"});break;case"PPP":s=e.dateTime({width:"long"});break;case"PPPP":default:s=e.dateTime({width:"full"});break}return s.replace("{{date}}",re(n,e)).replace("{{time}}",fe(a,e))},j={p:fe,P:lt},ft=/^D+$/,ht=/^Y+$/,mt=["D","DD","YY","YYYY"];function he(r){return ft.test(r)}function me(r){return ht.test(r)}function V(r,e,t){const n=wt(r,e,t);if(console.warn(n),mt.includes(r))throw new RangeError(n)}function wt(r,e,t){const n=r[0]==="Y"?"years":"days of the month";return`Use \`${r.toLowerCase()}\` instead of \`${r}\` (in \`${e}\`) for formatting ${n} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const yt=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,gt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,bt=/^'([^]*?)'?$/,pt=/''/g,xt=/[a-zA-Z]/;function rr(r,e,t){const n=F(),a=t?.locale??n.locale??ce,s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,o=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=p(r,t?.in);if(!_e(i))throw new RangeError("Invalid time value");let y=e.match(gt).map(f=>{const h=f[0];if(h==="p"||h==="P"){const k=j[h];return k(f,a.formatLong)}return f}).join("").match(yt).map(f=>{if(f==="''")return{isToken:!1,value:"'"};const h=f[0];if(h==="'")return{isToken:!1,value:Pt(f)};if(ee[h])return{isToken:!0,value:f};if(h.match(xt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+h+"`");return{isToken:!1,value:f}});a.localize.preprocessor&&(y=a.localize.preprocessor(i,y));const c={firstWeekContainsDate:s,weekStartsOn:o,locale:a};return y.map(f=>{if(!f.isToken)return f.value;const h=f.value;(!t?.useAdditionalWeekYearTokens&&me(h)||!t?.useAdditionalDayOfYearTokens&&he(h))&&V(h,e,String(r));const k=ee[h[0]];return k(i,h,a.localize,c)}).join("")}function Pt(r){const e=r.match(bt);return e?e[1].replace(pt,"'"):r}function Mt(){return Object.assign({},F())}function Dt(r,e){const t=p(r,e?.in).getDay();return t===0?7:t}function kt(r,e){const t=vt(e)?new e(0):D(e,0);return t.setFullYear(r.getFullYear(),r.getMonth(),r.getDate()),t.setHours(r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()),t}function vt(r){return typeof r=="function"&&r.prototype?.constructor===r}const Tt=10;class we{subPriority=0;validate(e,t){return!0}}class Ot extends we{constructor(e,t,n,a,s){super(),this.value=e,this.validateValue=t,this.setValue=n,this.priority=a,s&&(this.subPriority=s)}validate(e,t){return this.validateValue(e,this.value,t)}set(e,t,n){return this.setValue(e,t,this.value,n)}}class Yt extends we{priority=Tt;subPriority=-1;constructor(e,t){super(),this.context=e||(n=>D(t,n))}set(e,t){return t.timestampIsSet?e:D(e,kt(e,this.context))}}class d{run(e,t,n,a){const s=this.parse(e,t,n,a);return s?{setter:new Ot(s.value,this.validate,this.set,this.priority,this.subPriority),rest:s.rest}:null}validate(e,t,n){return!0}}class Wt extends d{priority=140;parse(e,t,n){switch(t){case"G":case"GG":case"GGG":return n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"});case"GGGGG":return n.era(e,{width:"narrow"});case"GGGG":default:return n.era(e,{width:"wide"})||n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"})}}set(e,t,n){return t.era=n,e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}incompatibleTokens=["R","u","t","T"]}const P={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},W={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function M(r,e){return r&&{value:e(r.value),rest:r.rest}}function b(r,e){const t=e.match(r);return t?{value:parseInt(t[0],10),rest:e.slice(t[0].length)}:null}function _(r,e){const t=e.match(r);if(!t)return null;if(t[0]==="Z")return{value:0,rest:e.slice(1)};const n=t[1]==="+"?1:-1,a=t[2]?parseInt(t[2],10):0,s=t[3]?parseInt(t[3],10):0,o=t[5]?parseInt(t[5],10):0;return{value:n*(a*ke+s*De+o*ve),rest:e.slice(t[0].length)}}function ye(r){return b(P.anyDigitsSigned,r)}function x(r,e){switch(r){case 1:return b(P.singleDigit,e);case 2:return b(P.twoDigits,e);case 3:return b(P.threeDigits,e);case 4:return b(P.fourDigits,e);default:return b(new RegExp("^\\d{1,"+r+"}"),e)}}function G(r,e){switch(r){case 1:return b(P.singleDigitSigned,e);case 2:return b(P.twoDigitsSigned,e);case 3:return b(P.threeDigitsSigned,e);case 4:return b(P.fourDigitsSigned,e);default:return b(new RegExp("^-?\\d{1,"+r+"}"),e)}}function S(r){switch(r){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function ge(r,e){const t=e>0,n=t?e:1-e;let a;if(n<=50)a=r||100;else{const s=n+50,o=Math.trunc(s/100)*100,i=r>=s%100;a=r+o-(i?100:0)}return t?a:1-a}function be(r){return r%400===0||r%4===0&&r%100!==0}class _t extends d{priority=130;incompatibleTokens=["Y","R","u","w","I","i","e","c","t","T"];parse(e,t,n){const a=s=>({year:s,isTwoDigitYear:t==="yy"});switch(t){case"y":return M(x(4,e),a);case"yo":return M(n.ordinalNumber(e,{unit:"year"}),a);default:return M(x(t.length,e),a)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n){const a=e.getFullYear();if(n.isTwoDigitYear){const o=ge(n.year,a);return e.setFullYear(o,0,1),e.setHours(0,0,0,0),e}const s=!("era"in t)||t.era===1?n.year:1-n.year;return e.setFullYear(s,0,1),e.setHours(0,0,0,0),e}}class Et extends d{priority=130;parse(e,t,n){const a=s=>({year:s,isTwoDigitYear:t==="YY"});switch(t){case"Y":return M(x(4,e),a);case"Yo":return M(n.ordinalNumber(e,{unit:"year"}),a);default:return M(x(t.length,e),a)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n,a){const s=z(e,a);if(n.isTwoDigitYear){const i=ge(n.year,s);return e.setFullYear(i,0,a.firstWeekContainsDate),e.setHours(0,0,0,0),E(e,a)}const o=!("era"in t)||t.era===1?n.year:1-n.year;return e.setFullYear(o,0,a.firstWeekContainsDate),e.setHours(0,0,0,0),E(e,a)}incompatibleTokens=["y","R","u","Q","q","M","L","I","d","D","i","t","T"]}class Ht extends d{priority=130;parse(e,t){return G(t==="R"?4:t.length,e)}set(e,t,n){const a=D(e,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),Q(a)}incompatibleTokens=["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]}class qt extends d{priority=130;parse(e,t){return G(t==="u"?4:t.length,e)}set(e,t,n){return e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}incompatibleTokens=["G","y","Y","R","w","I","i","e","c","t","T"]}class Nt extends d{priority=120;parse(e,t,n){switch(t){case"Q":case"QQ":return x(t.length,e);case"Qo":return n.ordinalNumber(e,{unit:"quarter"});case"QQQ":return n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(e,{width:"wide",context:"formatting"})||n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]}class It extends d{priority=120;parse(e,t,n){switch(t){case"q":case"qq":return x(t.length,e);case"qo":return n.ordinalNumber(e,{unit:"quarter"});case"qqq":return n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(e,{width:"wide",context:"standalone"})||n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]}class Ft extends d{incompatibleTokens=["Y","R","q","Q","L","w","I","D","i","e","c","t","T"];priority=110;parse(e,t,n){const a=s=>s-1;switch(t){case"M":return M(b(P.month,e),a);case"MM":return M(x(2,e),a);case"Mo":return M(n.ordinalNumber(e,{unit:"month"}),a);case"MMM":return n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(e,{width:"wide",context:"formatting"})||n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}}class Ct extends d{priority=110;parse(e,t,n){const a=s=>s-1;switch(t){case"L":return M(b(P.month,e),a);case"LL":return M(x(2,e),a);case"Lo":return M(n.ordinalNumber(e,{unit:"month"}),a);case"LLL":return n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(e,{width:"wide",context:"standalone"})||n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]}function Lt(r,e,t){const n=p(r,t?.in),a=le(n,t)-e;return n.setDate(n.getDate()-a*7),p(n,t?.in)}class Qt extends d{priority=100;parse(e,t,n){switch(t){case"w":return b(P.week,e);case"wo":return n.ordinalNumber(e,{unit:"week"});default:return x(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n,a){return E(Lt(e,n,a),a)}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","i","t","T"]}function Rt(r,e,t){const n=p(r,t?.in),a=de(n,t)-e;return n.setDate(n.getDate()-a*7),n}class $t extends d{priority=100;parse(e,t,n){switch(t){case"I":return b(P.week,e);case"Io":return n.ordinalNumber(e,{unit:"week"});default:return x(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n){return Q(Rt(e,n))}incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]}const Xt=[31,28,31,30,31,30,31,31,30,31,30,31],Gt=[31,29,31,30,31,30,31,31,30,31,30,31];class Bt extends d{priority=90;subPriority=1;parse(e,t,n){switch(t){case"d":return b(P.date,e);case"do":return n.ordinalNumber(e,{unit:"date"});default:return x(t.length,e)}}validate(e,t){const n=e.getFullYear(),a=be(n),s=e.getMonth();return a?t>=1&&t<=Gt[s]:t>=1&&t<=Xt[s]}set(e,t,n){return e.setDate(n),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","w","I","D","i","e","c","t","T"]}class At extends d{priority=90;subpriority=1;parse(e,t,n){switch(t){case"D":case"DD":return b(P.dayOfYear,e);case"Do":return n.ordinalNumber(e,{unit:"date"});default:return x(t.length,e)}}validate(e,t){const n=e.getFullYear();return be(n)?t>=1&&t<=366:t>=1&&t<=365}set(e,t,n){return e.setMonth(0,n),e.setHours(0,0,0,0),e}incompatibleTokens=["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]}function J(r,e,t){const n=F(),a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,s=p(r,t?.in),o=s.getDay(),y=(e%7+7)%7,c=7-a,f=e<0||e>6?e-(o+c)%7:(y+c)%7-(o+c)%7;return oe(s,f,t)}class jt extends d{priority=90;parse(e,t,n){switch(t){case"E":case"EE":case"EEE":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=J(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["D","i","e","c","t","T"]}class Vt extends d{priority=90;parse(e,t,n,a){const s=o=>{const i=Math.floor((o-1)/7)*7;return(o+a.weekStartsOn+6)%7+i};switch(t){case"e":case"ee":return M(x(t.length,e),s);case"eo":return M(n.ordinalNumber(e,{unit:"day"}),s);case"eee":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeeee":return n.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=J(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]}class zt extends d{priority=90;parse(e,t,n,a){const s=o=>{const i=Math.floor((o-1)/7)*7;return(o+a.weekStartsOn+6)%7+i};switch(t){case"c":case"cc":return M(x(t.length,e),s);case"co":return M(n.ordinalNumber(e,{unit:"day"}),s);case"ccc":return n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"ccccc":return n.day(e,{width:"narrow",context:"standalone"});case"cccccc":return n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return n.day(e,{width:"wide",context:"standalone"})||n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,a){return e=J(e,n,a),e.setHours(0,0,0,0),e}incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]}function St(r,e,t){const n=p(r,t?.in),a=Dt(n,t),s=e-a;return oe(n,s,t)}class Jt extends d{priority=90;parse(e,t,n){const a=s=>s===0?7:s;switch(t){case"i":case"ii":return x(t.length,e);case"io":return n.ordinalNumber(e,{unit:"day"});case"iii":return M(n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a);case"iiiii":return M(n.day(e,{width:"narrow",context:"formatting"}),a);case"iiiiii":return M(n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a);case"iiii":default:return M(n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),a)}}validate(e,t){return t>=1&&t<=7}set(e,t,n){return e=St(e,n),e.setHours(0,0,0,0),e}incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]}class Zt extends d{priority=80;parse(e,t,n){switch(t){case"a":case"aa":case"aaa":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(S(n),0,0,0),e}incompatibleTokens=["b","B","H","k","t","T"]}class Kt extends d{priority=80;parse(e,t,n){switch(t){case"b":case"bb":case"bbb":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(S(n),0,0,0),e}incompatibleTokens=["a","B","H","k","t","T"]}class Ut extends d{priority=80;parse(e,t,n){switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(S(n),0,0,0),e}incompatibleTokens=["a","b","t","T"]}class en extends d{priority=70;parse(e,t,n){switch(t){case"h":return b(P.hour12h,e);case"ho":return n.ordinalNumber(e,{unit:"hour"});default:return x(t.length,e)}}validate(e,t){return t>=1&&t<=12}set(e,t,n){const a=e.getHours()>=12;return a&&n<12?e.setHours(n+12,0,0,0):!a&&n===12?e.setHours(0,0,0,0):e.setHours(n,0,0,0),e}incompatibleTokens=["H","K","k","t","T"]}class tn extends d{priority=70;parse(e,t,n){switch(t){case"H":return b(P.hour23h,e);case"Ho":return n.ordinalNumber(e,{unit:"hour"});default:return x(t.length,e)}}validate(e,t){return t>=0&&t<=23}set(e,t,n){return e.setHours(n,0,0,0),e}incompatibleTokens=["a","b","h","K","k","t","T"]}class nn extends d{priority=70;parse(e,t,n){switch(t){case"K":return b(P.hour11h,e);case"Ko":return n.ordinalNumber(e,{unit:"hour"});default:return x(t.length,e)}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.getHours()>=12&&n<12?e.setHours(n+12,0,0,0):e.setHours(n,0,0,0),e}incompatibleTokens=["h","H","k","t","T"]}class rn extends d{priority=70;parse(e,t,n){switch(t){case"k":return b(P.hour24h,e);case"ko":return n.ordinalNumber(e,{unit:"hour"});default:return x(t.length,e)}}validate(e,t){return t>=1&&t<=24}set(e,t,n){const a=n<=24?n%24:n;return e.setHours(a,0,0,0),e}incompatibleTokens=["a","b","h","H","K","t","T"]}class an extends d{priority=60;parse(e,t,n){switch(t){case"m":return b(P.minute,e);case"mo":return n.ordinalNumber(e,{unit:"minute"});default:return x(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setMinutes(n,0,0),e}incompatibleTokens=["t","T"]}class sn extends d{priority=50;parse(e,t,n){switch(t){case"s":return b(P.second,e);case"so":return n.ordinalNumber(e,{unit:"second"});default:return x(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setSeconds(n,0),e}incompatibleTokens=["t","T"]}class on extends d{priority=30;parse(e,t){const n=a=>Math.trunc(a*Math.pow(10,-t.length+3));return M(x(t.length,e),n)}set(e,t,n){return e.setMilliseconds(n),e}incompatibleTokens=["t","T"]}class un extends d{priority=10;parse(e,t){switch(t){case"X":return _(W.basicOptionalMinutes,e);case"XX":return _(W.basic,e);case"XXXX":return _(W.basicOptionalSeconds,e);case"XXXXX":return _(W.extendedOptionalSeconds,e);case"XXX":default:return _(W.extended,e)}}set(e,t,n){return t.timestampIsSet?e:D(e,e.getTime()-$(e)-n)}incompatibleTokens=["t","T","x"]}class cn extends d{priority=10;parse(e,t){switch(t){case"x":return _(W.basicOptionalMinutes,e);case"xx":return _(W.basic,e);case"xxxx":return _(W.basicOptionalSeconds,e);case"xxxxx":return _(W.extendedOptionalSeconds,e);case"xxx":default:return _(W.extended,e)}}set(e,t,n){return t.timestampIsSet?e:D(e,e.getTime()-$(e)-n)}incompatibleTokens=["t","T","X"]}class dn extends d{priority=40;parse(e){return ye(e)}set(e,t,n){return[D(e,n*1e3),{timestampIsSet:!0}]}incompatibleTokens="*"}class ln extends d{priority=20;parse(e){return ye(e)}set(e,t,n){return[D(e,n),{timestampIsSet:!0}]}incompatibleTokens="*"}const fn={G:new Wt,y:new _t,Y:new Et,R:new Ht,u:new qt,Q:new Nt,q:new It,M:new Ft,L:new Ct,w:new Qt,I:new $t,d:new Bt,D:new At,E:new jt,e:new Vt,c:new zt,i:new Jt,a:new Zt,b:new Kt,B:new Ut,h:new en,H:new tn,K:new nn,k:new rn,m:new an,s:new sn,S:new on,X:new un,x:new cn,t:new dn,T:new ln},hn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,mn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,wn=/^'([^]*?)'?$/,yn=/''/g,gn=/\S/,bn=/[a-zA-Z]/;function ar(r,e,t,n){const a=()=>D(t,NaN),s=Mt(),o=s.locale??ce,i=s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,y=s.weekStartsOn??s.locale?.options?.weekStartsOn??0,c={firstWeekContainsDate:i,weekStartsOn:y,locale:o},f=[new Yt(n?.in,t)],h=e.match(mn).map(u=>{const w=u[0];if(w in j){const g=j[w];return g(u,o.formatLong)}return u}).join("").match(hn),k=[];for(let u of h){me(u)&&V(u,e,r),he(u)&&V(u,e,r);const w=u[0],g=fn[w];if(g){const{incompatibleTokens:v}=g;if(Array.isArray(v)){const R=k.find(Z=>v.includes(Z.token)||Z.token===w);if(R)throw new RangeError(`The format string mustn't contain \`${R.fullToken}\` and \`${u}\` at the same time`)}else if(g.incompatibleTokens==="*"&&k.length>0)throw new RangeError(`The format string mustn't contain \`${u}\` and any other token at the same time`);k.push({token:w,fullToken:u});const N=g.run(r,u,o.match,c);if(!N)return a();f.push(N.setter),r=N.rest}else{if(w.match(bn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+w+"`");if(u==="''"?u="'":w==="'"&&(u=pn(u)),r.indexOf(u)===0)r=r.slice(u.length);else return a()}}if(r.length>0&&gn.test(r))return a();const T=f.map(u=>u.priority).sort((u,w)=>w-u).filter((u,w,g)=>g.indexOf(u)===w).map(u=>f.filter(w=>w.priority===u).sort((w,g)=>g.subPriority-w.subPriority)).map(u=>u[0]);let H=p(t,n?.in);if(isNaN(+H))return a();const m={};for(const u of T){if(!u.validate(H,c))return a();const w=u.set(H,m,c);Array.isArray(w)?(H=w[0],Object.assign(m,w[1])):H=w}return H}function pn(r){return r.match(wn)[1].replace(yn,"'")}function xn(r,e,t){const[n,a]=B(t?.in,r,e);return+E(n,t)==+E(a,t)}function sr(r,e,t){const[n,a]=B(t?.in,r,e);return n.getFullYear()===a.getFullYear()&&n.getMonth()===a.getMonth()}const Pn={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}},Mn=(r,e,t)=>{let n;const a=Pn[r];return typeof a=="string"?n=a:e===1?n=a.one:n=a.other.replace("{{count}}",String(e)),t?.addSuffix?t.comparison&&t.comparison>0?n+"内":n+"前":n},Dn={full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},kn={full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},vn={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},Tn={date:L({formats:Dn,defaultWidth:"full"}),time:L({formats:kn,defaultWidth:"full"}),dateTime:L({formats:vn,defaultWidth:"full"})};function ae(r,e,t){const n="eeee p";return xn(r,e,t)?n:r.getTime()>e.getTime()?"'下个'"+n:"'上个'"+n}const On={lastWeek:ae,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:ae,other:"PP p"},Yn=(r,e,t,n)=>{const a=On[r];return typeof a=="function"?a(e,t,n):a},Wn={narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},_n={narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},En={narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},Hn={narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},qn={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},Nn={narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},In=(r,e)=>{const t=Number(r);switch(e?.unit){case"date":return t.toString()+"日";case"hour":return t.toString()+"时";case"minute":return t.toString()+"分";case"second":return t.toString()+"秒";default:return"第 "+t.toString()}},Fn={ordinalNumber:In,era:O({values:Wn,defaultWidth:"wide"}),quarter:O({values:_n,defaultWidth:"wide",argumentCallback:r=>r-1}),month:O({values:En,defaultWidth:"wide"}),day:O({values:Hn,defaultWidth:"wide"}),dayPeriod:O({values:qn,defaultWidth:"wide",formattingValues:Nn,defaultFormattingWidth:"wide"})},Cn=/^(第\s*)?\d+(日|时|分|秒)?/i,Ln=/\d+/i,Qn={narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},Rn={any:[/^(前)/i,/^(公元)/i]},$n={narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},Xn={any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},Gn={narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},Bn={narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},An={narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},jn={any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},Vn={any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},zn={any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},Sn={ordinalNumber:ue({matchPattern:Cn,parsePattern:Ln,valueCallback:r=>parseInt(r,10)}),era:Y({matchPatterns:Qn,defaultMatchWidth:"wide",parsePatterns:Rn,defaultParseWidth:"any"}),quarter:Y({matchPatterns:$n,defaultMatchWidth:"wide",parsePatterns:Xn,defaultParseWidth:"any",valueCallback:r=>r+1}),month:Y({matchPatterns:Gn,defaultMatchWidth:"wide",parsePatterns:Bn,defaultParseWidth:"any"}),day:Y({matchPatterns:An,defaultMatchWidth:"wide",parsePatterns:jn,defaultParseWidth:"any"}),dayPeriod:Y({matchPatterns:Vn,defaultMatchWidth:"any",parsePatterns:zn,defaultParseWidth:"any"})},or={code:"zh-CN",formatDistance:Mn,formatLong:Tn,formatRelative:Yn,localize:Fn,match:Sn,options:{weekStartsOn:1,firstWeekContainsDate:4}};function pe(r){var e,t,n="";if(typeof r=="string"||typeof r=="number")n+=r;else if(typeof r=="object")if(Array.isArray(r)){var a=r.length;for(e=0;e<a;e++)r[e]&&(t=pe(r[e]))&&(n&&(n+=" "),n+=t)}else for(t in r)r[t]&&(n&&(n+=" "),n+=t);return n}function ir(){for(var r,e,t=0,n="",a=arguments.length;t<a;t++)(r=arguments[t])&&(e=pe(r))&&(n&&(n+=" "),n+=e);return n}export{er as a,tr as b,Jn as c,nr as d,Un as e,rr as f,E as g,oe as h,_e as i,Kn as j,sr as k,xn as l,ir as m,Zn as n,ar as p,X as s,or as z};
