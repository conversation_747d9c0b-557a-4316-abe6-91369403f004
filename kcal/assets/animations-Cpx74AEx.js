import{c,a as o,e as i}from"./animations-DumLLhGv.js";i.defaults.duration=500;i.defaults.frameRate=60;const d=t=>o(t,{scale:[1,.95,1],duration:200,ease:"outElastic(1, .8)"}),l=(t,a)=>o(t,{width:`${a}%`,duration:600,ease:"outExpo"}),m=(t,a)=>o(t,{y:a?-4:0,scale:a?1.01:1,duration:300,ease:"outQuart"}),f=t=>{const a=c({defaults:{ease:"outExpo",duration:600}});return t.forEach((e,r)=>{a.add(e,{y:{from:50,to:0},opacity:{from:0,to:1}},r*100)}),a},p=(t,a,e=1500)=>{const s={value:0},n=t.querySelector("[data-react-component]")||t.closest("[data-react-component]")||t.innerHTML.includes("kcal");if(n){t.classList.add("animated");return}o(s,{value:a,duration:e,ease:"outQuart",update:()=>{n||(t.textContent=Math.round(s.value).toString())},complete:()=>{n||(t.textContent=Math.round(a).toString()),t.classList.add("animated")}})};export{d as createButtonAnimation,m as createCardHoverAnimation,p as createCountUpAnimation,f as createPageLoadAnimation,l as createProgressAnimation};
