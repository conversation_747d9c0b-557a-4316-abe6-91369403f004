/**
 * anime.js - ESM
 * @version v4.0.2
 * <AUTHOR>
 * @license MIT
 * @copyright (c) 2025 <PERSON>
 * @see https://animejs.com
 */const ye=typeof window<"u",bt=ye?window:null,Je=ye?document:null,N={OBJECT:0,ATTRIBUTE:1,CSS:2,TRANSFORM:3,CSS_VAR:4},g={NUMBER:0,UNIT:1,COLOR:2,COMPLEX:3},ne={NONE:0,AUTO:1,FORCE:2},J={replace:0,none:1,blend:2},Xt=Symbol(),Pt=Symbol(),ts=Symbol(),Tt=Symbol(),Ns=Symbol(),v=1e-11,ss=1e12,We=1e3,At=120,Ae="",tt=new Map;tt.set("x","translateX");tt.set("y","translateY");tt.set("z","translateZ");const wt=["translateX","translateY","translateZ","rotate","rotateX","rotateY","rotateZ","scale","scaleX","scaleY","scaleZ","skew","skewX","skewY","perspective","matrix","matrix3d"],ns=wt.reduce((t,e)=>({...t,[e]:e+"("}),{}),le=()=>{},Ds=/(^#([\da-f]{3}){1,2}$)|(^#([\da-f]{4}){1,2}$)/i,As=/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i,Es=/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(-?\d+|-?\d*.\d+)\s*\)/i,Os=/hsl\(\s*(-?\d+|-?\d*.\d+)\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)%\s*\)/i,xs=/hsla\(\s*(-?\d+|-?\d*.\d+)\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)%\s*,\s*(-?\d+|-?\d*.\d+)\s*\)/i,$t=/[-+]?\d*\.?\d+(?:e[-+]?\d)?/gi,is=/^([-+]?\d*\.?\d+(?:e[-+]?\d+)?)([a-z]+|%)$/i,Is=/([a-z])([A-Z])/g,Ps=/(\w+)(\([^)]+\)+)/g,ws=/(\*=|\+=|-=)/,os={id:null,keyframes:null,playbackEase:null,playbackRate:1,frameRate:At,loop:0,reversed:!1,alternate:!1,autoplay:!0,duration:We,delay:0,loopDelay:0,ease:"out(2)",composition:J.replace,modifier:t=>t,onBegin:le,onBeforeUpdate:le,onUpdate:le,onLoop:le,onPause:le,onComplete:le,onRender:le},w={defaults:os,root:Je,precision:4,timeScale:1,tickThreshold:200},rs={version:"4.0.2",engine:null};ye&&(bt.AnimeJS||(bt.AnimeJS=[]),bt.AnimeJS.push(rs));const Ls=t=>t.replace(Is,"$1-$2").toLowerCase(),Ue=(t,e)=>t.indexOf(e)===0,et=Date.now,He=Array.isArray,je=t=>t&&t.constructor===Object,gt=t=>typeof t=="number"&&!isNaN(t),st=t=>typeof t=="string",Ye=t=>typeof t=="function",m=t=>typeof t>"u",ct=t=>m(t)||t===null,as=t=>ye&&t instanceof SVGElement,ls=t=>Ds.test(t),cs=t=>Ue(t,"rgb"),us=t=>Ue(t,"hsl"),Ms=t=>ls(t)||cs(t)||us(t),ut=t=>!w.defaults.hasOwnProperty(t),Le=t=>st(t)?parseFloat(t):t,$e=Math.pow,fs=Math.sqrt,Us=Math.sin,Bs=Math.cos,Et=Math.abs,Fs=Math.ceil,_t=Math.floor,Vs=Math.asin,ks=Math.max,Lt=Math.PI,qt=Math.round,F=(t,e,s)=>t<e?e:t>s?s:t,zt={},A=(t,e)=>{if(e<0)return t;if(!e)return qt(t);let s=zt[e];return s||(s=zt[e]=10**e),qt(t*s)/s},Ie=(t,e,s)=>t+(e-t)*s,yt=t=>t===1/0?ss:t===-1/0?-1e12:t,qe=t=>t<=v?v:yt(A(t,11)),z=t=>He(t)?[...t]:t,ds=(t,e)=>{const s={...t};for(let i in e){const n=t[i];s[i]=m(n)?e[i]:n}return s},R=(t,e,s,i="_prev",n="_next")=>{let o=t._head,r=n;for(s&&(o=t._tail,r=i);o;){const c=o[r];e(o),o=c}},Ee=(t,e,s="_prev",i="_next")=>{const n=e[s],o=e[i];n?n[i]=o:t._head=o,o?o[s]=n:t._tail=n,e[s]=null,e[i]=null},Me=(t,e,s,i="_prev",n="_next")=>{let o=t._tail;for(;o&&s&&s(o,e);)o=o[i];const r=o?o[n]:t._head;o?o[n]=e:t._head=e,r?r[i]=e:t._tail=e,e[i]=o,e[n]=r};class hs{constructor(e=0){this.deltaTime=0,this._currentTime=e,this._elapsedTime=e,this._startTime=e,this._lastTime=e,this._scheduledTime=0,this._frameDuration=A(We/At,0),this._fps=At,this._speed=1,this._hasChildren=!1,this._head=null,this._tail=null}get fps(){return this._fps}set fps(e){const s=this._frameDuration,i=+e,n=i<v?v:i,o=A(We/n,0);this._fps=n,this._frameDuration=o,this._scheduledTime+=o-s}get speed(){return this._speed}set speed(e){const s=+e;this._speed=s<v?v:s}requestTick(e){const s=this._scheduledTime,i=this._elapsedTime;if(this._elapsedTime+=e-i,i<s)return ne.NONE;const n=this._frameDuration,o=i-s;return this._scheduledTime+=o<n?n:o,ne.AUTO}computeDeltaTime(e){const s=e-this._lastTime;return this.deltaTime=s,this._lastTime=e,s}}const ft=(t,e,s,i,n)=>{const o=t.parent,r=t.duration,c=t.completed,l=t.iterationDuration,a=t.iterationCount,u=t._currentIteration,d=t._loopDelay,p=t._reversed,h=t._alternate,S=t._hasChildren,y=t._delay,k=t._currentTime,ce=y+l,X=e-y,E=F(k,-y,r),L=F(X,-y,r),D=X-k,j=L>0,W=L>=r,H=r<=v,de=n===ne.FORCE;let be=0,$=X,C=0;if(a>1){const ue=~~(L/(l+(W?0:d)));t._currentIteration=F(ue,0,a),W&&t._currentIteration--,be=t._currentIteration%2,$=L%(l+d)||0}const Re=p^(h&&be),Y=t._ease;let O=W?Re?0:r:Re?l-$:$;Y&&(O=l*Y(O/l)||0);const ee=(o?o.backwards:X<k)?!Re:!!Re;if(t._currentTime=X,t._iterationTime=O,t.backwards=ee,j&&!t.began?(t.began=!0,!s&&!(o&&(ee||!o.began))&&t.onBegin(t)):X<=0&&(t.began=!1),!s&&!S&&j&&t._currentIteration!==u&&t.onLoop(t),de||n===ne.AUTO&&(e>=y&&e<=ce||e<=y&&E>y||e>=ce&&E!==r)||O>=ce&&E!==r||O<=y&&E>0||e<=E&&E===r&&c||W&&!c&&H){if(j&&(t.computeDeltaTime(E),s||t.onBeforeUpdate(t)),!S){const ue=de||(ee?D*-1:D)>=w.tickThreshold,x=t._offset+(o?o._offset:0)+y+O;let f=t._head,U,te,Be,Oe,ie=0;for(;f;){const Z=f._composition,oe=f._currentTime,se=f._changeDuration,Ke=f._absoluteStartTime+f._changeDuration,he=f._nextRep,re=f._prevRep,ve=Z!==J.none;if((ue||(oe!==se||x<=Ke+(he?he._delay:0))&&(oe!==0||x>=f._absoluteStartTime))&&(!ve||!f._isOverridden&&(!f._isOverlapped||x<=Ke)&&(!he||he._isOverridden||x<=he._absoluteStartTime)&&(!re||re._isOverridden||x>=re._absoluteStartTime+re._changeDuration+f._delay))){const Se=f._currentTime=F(O-f._startTime,0,se),P=f._ease(Se/f._updateDuration),K=f._modifier,fe=f._valueType,q=f._tweenType,G=q===N.OBJECT,Fe=fe===g.NUMBER,_e=Fe&&G||P===0||P===1?-1:w.precision;let Q,Ve;if(Fe)Q=Ve=K(A(Ie(f._fromNumber,f._toNumber,P),_e));else if(fe===g.UNIT)Ve=K(A(Ie(f._fromNumber,f._toNumber,P),_e)),Q=`${Ve}${f._unit}`;else if(fe===g.COLOR){const I=f._fromNumbers,pe=f._toNumbers,me=A(F(K(Ie(I[0],pe[0],P)),0,255),0),Ce=A(F(K(Ie(I[1],pe[1],P)),0,255),0),nt=A(F(K(Ie(I[2],pe[2],P)),0,255),0),ke=F(K(A(Ie(I[3],pe[3],P),_e)),0,1);if(Q=`rgba(${me},${Ce},${nt},${ke})`,ve){const xe=f._numbers;xe[0]=me,xe[1]=Ce,xe[2]=nt,xe[3]=ke}}else if(fe===g.COMPLEX){Q=f._strings[0];for(let I=0,pe=f._toNumbers.length;I<pe;I++){const me=K(A(Ie(f._fromNumbers[I],f._toNumbers[I],P),_e)),Ce=f._strings[I+1];Q+=`${Ce?me+Ce:me}`,ve&&(f._numbers[I]=me)}}if(ve&&(f._number=Ve),!i&&Z!==J.blend){const I=f.property;U=f.target,G?U[I]=Q:q===N.ATTRIBUTE?U.setAttribute(I,Q):(te=U.style,q===N.TRANSFORM?(U!==Be&&(Be=U,Oe=U[Tt]),Oe[I]=Q,ie=1):q===N.CSS?te[I]=Q:q===N.CSS_VAR&&te.setProperty(I,Q)),j&&(C=1)}else f._value=Q}if(ie&&f._renderTransforms){let Se=Ae;for(let P in Oe)Se+=`${ns[P]}${Oe[P]}) `;te.transform=Se,ie=0}f=f._next}!s&&C&&t.onRender(t)}!s&&j&&t.onUpdate(t)}return o&&H?!s&&(o.began&&!ee&&X>=r&&!c||ee&&X<=v&&c)&&(t.onComplete(t),t.completed=!ee):j&&W?a===1/0?t._startTime+=t.duration:t._currentIteration>=a-1&&(t.paused=!0,!c&&!S&&(t.completed=!0,!s&&!(o&&(ee||!o.began))&&(t.onComplete(t),t._resolve(t)))):t.completed=!1,C},we=(t,e,s,i,n)=>{const o=t._currentIteration;if(ft(t,e,s,i,n),t._hasChildren){const r=t,c=r.backwards,l=i?e:r._iterationTime,a=et();let u=0,d=!0;if(!i&&r._currentIteration!==o){const p=r.iterationDuration;R(r,h=>{if(!c)!h.completed&&!h.backwards&&h._currentTime<h.iterationDuration&&ft(h,p,s,1,ne.FORCE),h.began=!1,h.completed=!1;else{const S=h.duration,y=h._offset+h._delay,k=y+S;!s&&S<=v&&(!y||k===p)&&h.onComplete(h)}}),s||r.onLoop(r)}R(r,p=>{const h=A((l-p._offset)*p._speed,12),S=p._fps<r._fps?p.requestTick(a):n;u+=ft(p,h,s,i,S),!p.completed&&d&&(d=!1)},c),!s&&u&&r.onRender(r),d&&r._currentTime>=r.duration&&(r.paused=!0,r.completed||(r.completed=!0,s||(r.onComplete(r),r._resolve(r))))}},ze={animation:null,update:le},Xs=t=>{let e=ze.animation;return e||(e={duration:v,computeDeltaTime:le,_offset:0,_delay:0,_head:null,_tail:null},ze.animation=e,ze.update=()=>{t.forEach(s=>{for(let i in s){const n=s[i],o=n._head;if(o){const r=o._valueType,c=r===g.COMPLEX||r===g.COLOR?z(o._fromNumbers):null;let l=o._fromNumber,a=n._tail;for(;a&&a!==o;){if(c)for(let u=0,d=a._numbers.length;u<d;u++)c[u]+=a._numbers[u];else l+=a._number;a=a._prevAdd}o._toNumber=l,o._toNumbers=c}}}),ft(e,1,1,0,ne.FORCE)}),e},_s=ye?requestAnimationFrame:setImmediate,$s=ye?cancelAnimationFrame:clearImmediate;class qs extends hs{constructor(e){super(e),this.useDefaultMainLoop=!0,this.pauseOnDocumentHidden=!0,this.defaults=os,this.paused=!!(ye&&Je.hidden),this.reqId=null}update(){const e=this._currentTime=et();if(this.requestTick(e)){this.computeDeltaTime(e);const s=this._speed,i=this._fps;let n=this._head;for(;n;){const o=n._next;n.paused?(Ee(this,n),this._hasChildren=!!this._tail,n._running=!1,n.completed&&!n._cancelled&&n.cancel()):we(n,(e-n._startTime)*n._speed*s,0,0,n._fps<i?n.requestTick(e):ne.AUTO),n=o}ze.update()}}wake(){return this.useDefaultMainLoop&&!this.reqId&&!this.paused&&(this.reqId=_s(ps)),this}pause(){return this.paused=!0,zs()}resume(){if(this.paused)return this.paused=!1,R(this,e=>e.resetTime()),this.wake()}get speed(){return this._speed*(w.timeScale===1?1:We)}set speed(e){this._speed=e*w.timeScale,R(this,s=>s.speed=s._speed)}get timeUnit(){return w.timeScale===1?"ms":"s"}set timeUnit(e){const i=e==="s",n=i?.001:1;if(w.timeScale!==n){w.timeScale=n,w.tickThreshold=200*n;const o=i?.001:We;this.defaults.duration*=o,this._speed*=o}}get precision(){return w.precision}set precision(e){w.precision=e}}const M=(()=>{const t=new qs(et());return ye&&(rs.engine=t,Je.addEventListener("visibilitychange",()=>{t.pauseOnDocumentHidden&&(Je.hidden?t.pause():t.resume())})),t})(),ps=()=>{M._head?(M.reqId=_s(ps),M.update()):M.reqId=0},zs=()=>($s(M.reqId),M.reqId=0,M),Js=(t,e,s)=>{const i=t.style.transform;let n;if(i){const o=t[Tt];let r;for(;r=Ps.exec(i);){const c=r[1],l=r[2].slice(1,-1);o[c]=l,c===e&&(n=l,s&&(s[e]=l))}}return i&&!m(n)?n:Ue(e,"scale")?"1":Ue(e,"rotate")||Ue(e,"skew")?"0deg":"0px"};function Jt(t){const e=st(t)?w.root.querySelectorAll(t):t;if(e instanceof NodeList||e instanceof HTMLCollection)return e}function Mt(t){if(ct(t))return[];if(He(t)){const s=t.flat(1/0),i=[];for(let n=0,o=s.length;n<o;n++){const r=s[n];if(!ct(r)){const c=Jt(r);if(c)for(let l=0,a=c.length;l<a;l++){const u=c[l];if(!ct(u)){let d=!1;for(let p=0,h=i.length;p<h;p++)if(i[p]===u){d=!0;break}d||i.push(u)}}else{let l=!1;for(let a=0,u=i.length;a<u;a++)if(i[a]===r){l=!0;break}l||i.push(r)}}}return i}if(!ye)return[t];const e=Jt(t);return e?Array.from(e):[t]}function Ws(t){const e=Mt(t),s=e.length;if(s)for(let i=0;i<s;i++){const n=e[i];if(!n[Xt]){n[Xt]=!0;const o=as(n);(n.nodeType||o)&&(n[Pt]=!0,n[ts]=o,n[Tt]={})}}return e}const Hs=["opacity","rotate","overflow","color"],Ys=(t,e)=>{if(Hs.includes(e))return!1;if(t.getAttribute(e)||e in t){if(e==="scale"){const s=t.parentNode;return s&&s.tagName==="filter"}return!0}},Zs=t=>{const e=As.exec(t)||Es.exec(t),s=m(e[4])?1:+e[4];return[+e[1],+e[2],+e[3],s]},Ks=t=>{const e=t.length,s=e===4||e===5;return[+("0x"+t[1]+t[s?1:2]),+("0x"+t[s?2:3]+t[s?2:4]),+("0x"+t[s?3:5]+t[s?3:6]),e===5||e===9?+(+("0x"+t[s?4:7]+t[s?4:8])/255).toFixed(3):1]},Rt=(t,e,s)=>(s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+(e-t)*6*s:s<1/2?e:s<2/3?t+(e-t)*(2/3-s)*6:t),Gs=t=>{const e=Os.exec(t)||xs.exec(t),s=+e[1]/360,i=+e[2]/100,n=+e[3]/100,o=m(e[4])?1:+e[4];let r,c,l;if(i===0)r=c=l=n;else{const a=n<.5?n*(1+i):n+i-n*i,u=2*n-a;r=A(Rt(u,a,s+1/3)*255,0),c=A(Rt(u,a,s)*255,0),l=A(Rt(u,a,s-1/3)*255,0)}return[r,c,l,o]},Qs=t=>cs(t)?Zs(t):ls(t)?Ks(t):us(t)?Gs(t):[0,0,0,1],V=(t,e)=>m(t)?e:t,Ne=(t,e,s,i,n)=>{if(Ye(t)){const o=()=>{const r=t(e,s,i);return isNaN(+r)?r||0:+r};return n&&(n.func=o),o()}else return t},ms=(t,e)=>t[Pt]?t[ts]&&Ys(t,e)?N.ATTRIBUTE:wt.includes(e)||tt.get(e)?N.TRANSFORM:Ue(e,"--")?N.CSS_VAR:e in t.style?N.CSS:e in t?N.OBJECT:N.ATTRIBUTE:N.OBJECT,Wt=(t,e,s)=>{const i=t.style[e];i&&s&&(s[e]=i);const n=i||getComputedStyle(t[Ns]||t).getPropertyValue(e);return n==="auto"?"0":n},Ge=(t,e,s,i)=>{const n=m(s)?ms(t,e):s;return n===N.OBJECT?t[e]||0:n===N.ATTRIBUTE?t.getAttribute(e):n===N.TRANSFORM?Js(t,e,i):n===N.CSS_VAR?Wt(t,e,i).trimStart():Wt(t,e,i)},Ot=(t,e,s)=>s==="-"?t-e:s==="+"?t+e:t*e,Ut=()=>({t:g.NUMBER,n:0,u:null,o:null,d:null,s:null}),Te=(t,e)=>{if(e.t=g.NUMBER,e.n=0,e.u=null,e.o=null,e.d=null,e.s=null,!t)return e;const s=+t;if(isNaN(s)){let i=t;i[1]==="="&&(e.o=i[0],i=i.slice(2));const n=i.includes(" ")?!1:is.exec(i);if(n)return e.t=g.UNIT,e.n=+n[1],e.u=n[2],e;if(e.o)return e.n=+i,e;if(Ms(i))return e.t=g.COLOR,e.d=Qs(i),e;{const o=i.match($t);return e.t=g.COMPLEX,e.d=o?o.map(Number):[],e.s=i.split($t)||[],e}}else return e.n=s,e},Ht=(t,e)=>(e.t=t._valueType,e.n=t._toNumber,e.u=t._unit,e.o=null,e.d=z(t._toNumbers),e.s=z(t._strings),e),Pe=Ut(),pt={_rep:new WeakMap,_add:new Map},Bt=(t,e,s="_rep")=>{const i=pt[s];let n=i.get(t);return n||(n={},i.set(t,n)),n[e]?n[e]:n[e]={_head:null,_tail:null}},js=(t,e)=>t._isOverridden||t._absoluteStartTime>e._absoluteStartTime,dt=t=>{t._isOverlapped=1,t._isOverridden=1,t._changeDuration=v,t._currentTime=v},Ts=(t,e)=>{const s=t._composition;if(s===J.replace){const i=t._absoluteStartTime;Me(e,t,js,"_prevRep","_nextRep");const n=t._prevRep;if(n){const o=n.parent,r=n._absoluteStartTime+n._changeDuration;if(t.parent.id!==o.id&&o.iterationCount>1&&r+(o.duration-o.iterationDuration)>i){dt(n);let a=n._prevRep;for(;a&&a.parent.id===o.id;)dt(a),a=a._prevRep}const c=i-t._delay;if(r>c){const a=n._startTime,u=r-(a+n._updateDuration);n._changeDuration=c-u-a,n._currentTime=n._changeDuration,n._isOverlapped=1,n._changeDuration<v&&dt(n)}let l=!0;if(R(o,a=>{a._isOverlapped||(l=!1)}),l){const a=o.parent;if(a){let u=!0;R(a,d=>{d!==o&&R(d,p=>{p._isOverlapped||(u=!1)})}),u&&a.cancel()}else o.cancel()}}}else if(s===J.blend){const i=Bt(t.target,t.property,"_add"),n=Xs(pt._add);let o=i._head;o||(o={...t},o._composition=J.replace,o._updateDuration=v,o._startTime=0,o._numbers=z(t._fromNumbers),o._number=0,o._next=null,o._prev=null,Me(i,o),Me(n,o));const r=t._toNumber;if(t._fromNumber=o._fromNumber-r,t._toNumber=0,t._numbers=z(t._fromNumbers),t._number=0,o._fromNumber=r,t._toNumbers){const c=z(t._toNumbers);c&&c.forEach((l,a)=>{t._fromNumbers[a]=o._fromNumbers[a]-l,t._toNumbers[a]=0}),o._fromNumbers=c}Me(i,t,null,"_prevAdd","_nextAdd")}return t},gs=t=>{const e=t._composition;if(e!==J.none){const s=t.target,i=t.property,r=pt._rep.get(s)[i];if(Ee(r,t,"_prevRep","_nextRep"),e===J.blend){const c=pt._add,l=c.get(s);if(!l)return;const a=l[i],u=ze.animation;Ee(a,t,"_prevAdd","_nextAdd");const d=a._head;if(d&&d===a._tail){Ee(a,d,"_prevAdd","_nextAdd"),Ee(u,d);let p=!0;for(let h in l)if(l[h]._head){p=!1;break}p&&c.delete(s)}}}return t},Yt=t=>(t.paused=!0,t.began=!1,t.completed=!1,t),xt=t=>(t._cancelled&&(t._hasChildren?R(t,xt):R(t,e=>{e._composition!==J.none&&Ts(e,Bt(e.target,e.property))}),t._cancelled=0),t);let en=0;class Ft extends hs{constructor(e={},s=null,i=0){super(0);const{id:n,delay:o,duration:r,reversed:c,alternate:l,loop:a,loopDelay:u,autoplay:d,frameRate:p,playbackRate:h,onComplete:S,onLoop:y,onPause:k,onBegin:ce,onBeforeUpdate:X,onUpdate:E}=e,L=s?0:M._elapsedTime,D=s?s.defaults:w.defaults,j=Ye(o)||m(o)?D.delay:+o,W=Ye(r)||m(r)?1/0:+r,H=V(a,D.loop),de=V(u,D.loopDelay),be=H===!0||H===1/0||H<0?1/0:H+1;let $=0;if(s)$=i;else{let C=et();M.paused&&(M.requestTick(C),C=M._elapsedTime),$=C-M._startTime}this.id=m(n)?++en:n,this.parent=s,this.duration=yt((W+de)*be-de)||v,this.backwards=!1,this.paused=!0,this.began=!1,this.completed=!1,this.onBegin=ce||D.onBegin,this.onBeforeUpdate=X||D.onBeforeUpdate,this.onUpdate=E||D.onUpdate,this.onLoop=y||D.onLoop,this.onPause=k||D.onPause,this.onComplete=S||D.onComplete,this.iterationDuration=W,this.iterationCount=be,this._autoplay=s?!1:V(d,D.autoplay),this._offset=$,this._delay=j,this._loopDelay=de,this._iterationTime=0,this._currentIteration=0,this._resolve=le,this._running=!1,this._reversed=+V(c,D.reversed),this._reverse=this._reversed,this._cancelled=0,this._alternate=V(l,D.alternate),this._prev=null,this._next=null,this._elapsedTime=L,this._startTime=L,this._lastTime=L,this._fps=V(p,D.frameRate),this._speed=V(h,D.playbackRate)}get cancelled(){return!!this._cancelled}set cancelled(e){e?this.cancel():this.reset(1).play()}get currentTime(){return F(A(this._currentTime,w.precision),-this._delay,this.duration)}set currentTime(e){const s=this.paused;this.pause().seek(+e),s||this.resume()}get iterationCurrentTime(){return A(this._iterationTime,w.precision)}set iterationCurrentTime(e){this.currentTime=this.iterationDuration*this._currentIteration+e}get progress(){return F(A(this._currentTime/this.duration,5),0,1)}set progress(e){this.currentTime=this.duration*e}get iterationProgress(){return F(A(this._iterationTime/this.iterationDuration,5),0,1)}set iterationProgress(e){const s=this.iterationDuration;this.currentTime=s*this._currentIteration+s*e}get currentIteration(){return this._currentIteration}set currentIteration(e){this.currentTime=this.iterationDuration*F(+e,0,this.iterationCount-1)}get reversed(){return!!this._reversed}set reversed(e){e?this.reverse():this.play()}get speed(){return super.speed}set speed(e){super.speed=e,this.resetTime()}reset(e=0){return xt(this),this._reversed&&!this._reverse&&(this.reversed=!1),this._iterationTime=this.iterationDuration,we(this,0,1,e,ne.FORCE),Yt(this),this._hasChildren&&R(this,Yt),this}init(e=0){this.fps=this._fps,this.speed=this._speed,!e&&this._hasChildren&&we(this,this.duration,1,e,ne.FORCE),this.reset(e);const s=this._autoplay;return s===!0?this.resume():s&&!m(s.linked)&&s.link(this),this}resetTime(){const e=1/(this._speed*M._speed);return this._startTime=et()-(this._currentTime+this._delay)*e,this}pause(){return this.paused?this:(this.paused=!0,this.onPause(this),this)}resume(){return this.paused?(this.paused=!1,this.duration<=v&&!this._hasChildren?we(this,v,0,0,ne.FORCE):(this._running||(Me(M,this),M._hasChildren=!0,this._running=!0),this.resetTime(),this._startTime-=12,M.wake()),this):this}restart(){return this.reset(0).resume()}seek(e,s=0,i=0){xt(this),this.completed=!1;const n=this.paused;return this.paused=!0,we(this,e+this._delay,~~s,~~i,ne.AUTO),n?this:this.resume()}alternate(){const e=this._reversed,s=this.iterationCount,i=this.iterationDuration,n=s===1/0?_t(ss/i):s;return this._reversed=+(this._alternate&&!(n%2)?e:!e),s===1/0?this.iterationProgress=this._reversed?1-this.iterationProgress:this.iterationProgress:this.seek(i*n-this._currentTime),this.resetTime(),this}play(){return this._reversed&&this.alternate(),this.resume()}reverse(){return this._reversed||this.alternate(),this.resume()}cancel(){return this._hasChildren?R(this,e=>e.cancel(),!0):R(this,gs),this._cancelled=1,this.pause()}stretch(e){const s=this.duration,i=qe(e);if(s===i)return this;const n=e/s,o=e<=v;return this.duration=o?v:i,this.iterationDuration=o?v:qe(this.iterationDuration*n),this._offset*=n,this._delay*=n,this._loopDelay*=n,this}revert(){we(this,0,1,0,ne.AUTO);const e=this._autoplay;return e&&e.linked&&e.linked===this&&e.revert(),this.cancel()}complete(){return this.seek(this.duration).cancel()}then(e=le){const s=this.then,i=()=>{this.then=null,e(this),this.then=s,this._resolve=le};return new Promise(n=>(this._resolve=()=>n(i()),this.completed&&this._resolve(),this))}}const Ze=t=>t,ys=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t,tn=(t,e,s)=>{let i=0,n=1,o,r,c=0;do r=i+(n-i)/2,o=ys(r,e,s)-t,o>0?n=r:i=r;while(Et(o)>1e-7&&++c<100);return r},sn=(t=.5,e=0,s=.5,i=1)=>t===e&&s===i?Ze:n=>n===0||n===1?n:ys(tn(n,t,s),e,i),nn=(t=10,e)=>{const s=e?Fs:_t;return i=>s(F(i,0,1)*t)*(1/t)},vs=(...t)=>{const e=t.length;if(!e)return Ze;const s=e-1,i=t[0],n=t[s],o=[0],r=[Le(i)];for(let c=1;c<s;c++){const l=t[c],a=st(l)?l.trim().split(" "):[l],u=a[0],d=a[1];o.push(m(d)?c/s:Le(d)/100),r.push(Le(u))}return r.push(Le(n)),o.push(1),function(l){for(let a=1,u=o.length;a<u;a++){const d=o[a];if(l<=d){const p=o[a-1],h=r[a-1];return h+(r[a]-h)*(l-p)/(d-p)}}return r[r.length-1]}},on=(t=10,e=1)=>{const s=[0],i=t-1;for(let n=1;n<i;n++){const o=s[n-1],r=n/i,c=(n+1)/i,l=r+(c-r)*Math.random(),a=r*(1-e)+l*e;s.push(F(a,o,1))}return s.push(1),vs(...s)},rn=Lt/2,Zt=Lt*2,Qe=(t=1.68)=>e=>$e(e,+t),Kt={[Ae]:Qe,Quad:Qe(2),Cubic:Qe(3),Quart:Qe(4),Quint:Qe(5),Sine:t=>1-Bs(t*rn),Circ:t=>1-fs(1-t*t),Expo:t=>t?$e(2,10*t-10):0,Bounce:t=>{let e,s=4;for(;t<((e=$e(2,--s))-1)/11;);return 1/$e(4,3-s)-7.5625*$e((e*3-2)/22-t,2)},Back:(t=1.70158)=>e=>(+t+1)*e*e*e-+t*e*e,Elastic:(t=1,e=.3)=>{const s=F(+t,1,10),i=F(+e,v,2),n=i/Zt*Vs(1/s),o=Zt/i;return r=>r===0||r===1?r:-s*$e(2,-10*(1-r))*Us((1-r-n)*o)}},It={in:t=>e=>t(e),out:t=>e=>1-t(1-e),inOut:t=>e=>e<.5?t(e*2)/2:1-t(e*-2+2)/2,outIn:t=>e=>e<.5?(1-t(1-e*2))/2:(t(e*2-1)+1)/2},an=(t,e,s)=>{if(s[t])return s[t];if(t.indexOf("(")<=-1){const n=It[t]||t.includes("Back")||t.includes("Elastic")?e[t]():e[t];return n?s[t]=n:Ze}else{const i=t.slice(0,-1).split("("),n=e[i[0]];return n?s[t]=n(...i[1].split(",")):Ze}},ln=(()=>{const t={linear:vs,irregular:on,steps:nn,cubicBezier:sn};for(let e in It)for(let s in Kt){const i=Kt[s],n=It[e];t[e+s]=s===Ae||s==="Back"||s==="Elastic"?(o,r)=>n(i(o,r)):n(i)}return t})(),cn={linear:Ze},mt=t=>Ye(t)?t:st(t)?an(t,ln,cn):Ze,Gt={},Ss=(t,e,s)=>{if(s===N.TRANSFORM){const i=tt.get(t);return i||t}else if(s===N.CSS||s===N.ATTRIBUTE&&as(e)&&t in e.style){const i=Gt[t];if(i)return i;{const n=t&&Ls(t);return Gt[t]=n,n}}else return t},Nt={deg:1,rad:180/Lt,turn:360},Qt={},un=(t,e,s,i=!1)=>{const n=e.u,o=e.n;if(e.t===g.UNIT&&n===s)return e;const r=o+n+s,c=Qt[r];if(!m(c)&&!i)e.n=c;else{let l;if(n in Nt)l=o*Nt[n]/Nt[s];else{const u=t.cloneNode(),d=t.parentNode,p=d&&d!==Je?d:Je.body;p.appendChild(u);const h=u.style;h.width=100+n;const S=u.offsetWidth||100;h.width=100+s;const y=u.offsetWidth||100,k=S/y;p.removeChild(u),l=k*o}e.n=l,Qt[r]=l}return e.t,g.UNIT,e.u=s,e},Vt=t=>{if(t._hasChildren)R(t,Vt,!0);else{const e=t;e.pause(),R(e,s=>{const i=s.property,n=s.target;if(n[Pt]){const o=n.style,r=e._inlineStyles[i];if(s._tweenType===N.TRANSFORM){const c=n[Tt];if(m(r)||r===Ae?delete c[i]:c[i]=r,s._renderTransforms)if(!Object.keys(c).length)o.removeProperty("transform");else{let l=Ae;for(let a in c)l+=ns[a]+c[a]+") ";o.transform=l}}else m(r)||r===Ae?o.removeProperty(i):o[i]=r;e._tail===s&&e.targets.forEach(c=>{c.getAttribute&&c.getAttribute("style")===Ae&&c.removeAttribute("style")})}})}return t},_=Ut(),T=Ut(),rt={func:null},at=[null],Xe=[null,null],lt={to:null};let fn=0,De,ge;const dn=(t,e)=>{const s={};if(He(t)){const i=[].concat(...t.map(n=>Object.keys(n))).filter(ut);for(let n=0,o=i.length;n<o;n++){const r=i[n],c=t.map(l=>{const a={};for(let u in l){const d=l[u];ut(u)?u===r&&(a.to=d):a[u]=d}return a});s[r]=c}}else{const i=V(e.duration,w.defaults.duration);Object.keys(t).map(o=>({o:parseFloat(o)/100,p:t[o]})).sort((o,r)=>o.o-r.o).forEach(o=>{const r=o.o,c=o.p;for(let l in c)if(ut(l)){let a=s[l];a||(a=s[l]=[]);const u=r*i;let d=a.length,p=a[d-1];const h={to:c[l]};let S=0;for(let y=0;y<d;y++)S+=a[y].duration;d===1&&(h.from=p.to),c.ease&&(h.ease=c.ease),h.duration=u-(d?S:0),a.push(h)}return o});for(let o in s){const r=s[o];let c;for(let l=0,a=r.length;l<a;l++){const u=r[l],d=u.ease;u.ease=c||void 0,c=d}r[0].duration||r.shift()}}return s};class Cs extends Ft{constructor(e,s,i,n,o=!1,r=0,c=0){super(s,i,n);const l=Ws(e),a=l.length,u=s.keyframes,d=u?ds(dn(u,s),s):s,{delay:p,duration:h,ease:S,playbackEase:y,modifier:k,composition:ce,onRender:X}=d,E=i?i.defaults:w.defaults,L=V(y,E.playbackEase),D=L?mt(L):null,j=!m(S)&&!m(S.ease),W=j?S.ease:V(S,D?"linear":E.ease),H=j?S.duration:V(h,E.duration),de=V(p,E.delay),be=k||E.modifier,$=m(ce)&&a>=We?J.none:m(ce)?E.composition:ce,C={},Re=this._offset+(i?i._offset:0);let Y=NaN,O=NaN,ee=0,ue=0;for(let x=0;x<a;x++){const f=l[x],U=r||x,te=c||a;let Be=NaN,Oe=NaN;for(let ie in d)if(ut(ie)){const Z=ms(f,ie),oe=Ss(ie,f,Z);let se=d[ie];const Ke=He(se);if(o&&!Ke&&(Xe[0]=se,Xe[1]=se,se=Xe),Ke){const K=se.length,fe=!je(se[0]);K===2&&fe?(lt.to=se,at[0]=lt,De=at):K>2&&fe?(De=[],se.forEach((q,G)=>{G?G===1?(Xe[1]=q,De.push(Xe)):De.push(q):Xe[0]=q})):De=se}else at[0]=se,De=at;let he=null,re=null,ve=NaN,Se=0,P=0;for(let K=De.length;P<K;P++){const fe=De[P];je(fe)?ge=fe:(lt.to=fe,ge=lt),rt.func=null;const q=Ne(ge.to,f,U,te,rt);let G;je(q)&&!m(q.to)?(ge=q,G=q.to):G=q;const Fe=Ne(ge.from,f,U,te),_e=ge.ease,Q=!m(_e)&&!m(_e.ease),Ve=Q?_e.ease:_e||W,I=Q?_e.duration:Ne(V(ge.duration,K>1?Ne(H,f,U,te)/K:H),f,U,te),pe=Ne(V(ge.delay,P?0:de),f,U,te),me=Ne(V(ge.composition,$),f,U,te),Ce=gt(me)?me:J[me],nt=ge.modifier||be,ke=!m(Fe),xe=!m(G),it=He(G),Rs=it||ke&&xe,vt=re?Se+pe:pe,St=Re+vt;!ue&&(ke||it)&&(ue=1);let ae=re;if(Ce!==J.none){he||(he=Bt(f,oe));let b=he._head;for(;b&&!b._isOverridden&&b._absoluteStartTime<=St;)if(ae=b,b=b._nextRep,b&&b._absoluteStartTime>=St)for(;b;)dt(b),b=b._nextRep}if(Rs?(Te(it?Ne(G[0],f,U,te):Fe,_),Te(it?Ne(G[1],f,U,te,rt):G,T),_.t===g.NUMBER&&(ae?ae._valueType===g.UNIT&&(_.t=g.UNIT,_.u=ae._unit):(Te(Ge(f,oe,Z,C),Pe),Pe.t===g.UNIT&&(_.t=g.UNIT,_.u=Pe.u)))):(xe?Te(G,T):re?Ht(re,T):Te(i&&ae&&ae.parent.parent===i?ae._value:Ge(f,oe,Z,C),T),ke?Te(Fe,_):re?Ht(re,_):Te(i&&ae&&ae.parent.parent===i?ae._value:Ge(f,oe,Z,C),_)),_.o&&(_.n=Ot(ae?ae._toNumber:Te(Ge(f,oe,Z,C),Pe).n,_.n,_.o)),T.o&&(T.n=Ot(_.n,T.n,T.o)),_.t!==T.t){if(_.t===g.COMPLEX||T.t===g.COMPLEX){const b=_.t===g.COMPLEX?_:T,B=_.t===g.COMPLEX?T:_;B.t=g.COMPLEX,B.s=z(b.s),B.d=b.d.map(()=>B.n)}else if(_.t===g.UNIT||T.t===g.UNIT){const b=_.t===g.UNIT?_:T,B=_.t===g.UNIT?T:_;B.t=g.UNIT,B.u=b.u}else if(_.t===g.COLOR||T.t===g.COLOR){const b=_.t===g.COLOR?_:T,B=_.t===g.COLOR?T:_;B.t=g.COLOR,B.s=b.s,B.d=[0,0,0,1]}}if(_.u!==T.u){let b=T.u?_:T;b=un(f,b,T.u?T.u:_.u,!1)}if(T.d&&_.d&&T.d.length!==_.d.length){const b=_.d.length>T.d.length?_:T,B=b===_?T:_;B.d=b.d.map((gn,kt)=>m(B.d[kt])?0:B.d[kt]),B.s=z(b.s)}const Ct=A(+I||v,12),ot={parent:this,id:fn++,property:oe,target:f,_value:null,_func:rt.func,_ease:mt(Ve),_fromNumbers:z(_.d),_toNumbers:z(T.d),_strings:z(T.s),_fromNumber:_.n,_toNumber:T.n,_numbers:z(_.d),_number:_.n,_unit:T.u,_modifier:nt,_currentTime:0,_startTime:vt,_delay:+pe,_updateDuration:Ct,_changeDuration:Ct,_absoluteStartTime:St,_tweenType:Z,_valueType:T.t,_composition:Ce,_isOverlapped:0,_isOverridden:0,_renderTransforms:0,_prevRep:null,_nextRep:null,_prevAdd:null,_nextAdd:null,_prev:null,_next:null};Ce!==J.none&&Ts(ot,he),isNaN(ve)&&(ve=ot._startTime),Se=A(vt+Ct,12),re=ot,ee++,Me(this,ot)}(isNaN(O)||ve<O)&&(O=ve),(isNaN(Y)||Se>Y)&&(Y=Se),Z===N.TRANSFORM&&(Be=ee-P,Oe=ee)}if(!isNaN(Be)){let ie=0;R(this,Z=>{ie>=Be&&ie<Oe&&(Z._renderTransforms=1,Z._composition===J.blend&&R(ze.animation,oe=>{oe.id===Z.id&&(oe._renderTransforms=1)})),ie++})}}a||console.warn("No target found. Make sure the element you're trying to animate is accessible before creating your animation."),O?(R(this,x=>{x._startTime-x._delay||(x._delay-=O),x._startTime-=O}),Y-=O):O=0,Y||(Y=v,this.iterationCount=0),this.targets=l,this.duration=Y===v?v:yt((Y+this._loopDelay)*this.iterationCount-this._loopDelay)||v,this.onRender=X||E.onRender,this._ease=D,this._delay=O,this.iterationDuration=Y,this._inlineStyles=C,!this._autoplay&&ue&&this.onRender(this)}stretch(e){const s=this.duration;if(s===qe(e))return this;const i=e/s;return R(this,n=>{n._updateDuration=qe(n._updateDuration*i),n._changeDuration=qe(n._changeDuration*i),n._currentTime*=i,n._startTime*=i,n._absoluteStartTime*=i}),super.stretch(e)}refresh(){return R(this,e=>{const s=Ge(e.target,e.property,e._tweenType);Te(s,Pe),e._fromNumbers=z(Pe.d),e._fromNumber=Pe.n,e._func&&(Te(e._func(),T),e._toNumbers=z(T.d),e._strings=z(T.s),e._toNumber=T.n)}),this}revert(){return super.revert(),Vt(this)}then(e){return super.then(e)}}const yn=(t,e)=>new Cs(t,e,null,0,!1).init(),hn=["x","y","z"];[...hn,...wt.filter(t=>["X","Y","Z"].some(e=>t.endsWith(e)))];ye&&(m(CSS)||Object.hasOwnProperty.call(CSS,"registerProperty"));const jt={_head:null,_tail:null},_n=(t,e,s)=>{let i=jt._head;for(;i;){const n=i._next,o=i.$el===t,r=!e||i.property===e,c=!s||i.parent===s;if(o&&r&&c){const l=i.animation;try{l.commitStyles()}catch{}l.cancel(),Ee(jt,i);const a=i.parent;a&&(a._completed++,a.animations.length===a._completed&&(a.completed=!0,a.muteCallbacks||(a.paused=!0,a.onComplete(a),a._resolve(a))))}i=n}},es=(t,e,s)=>{let i=!1;return R(e,n=>{const o=n.target;if(t.includes(o)){const r=n.property,c=n._tweenType,l=Ss(s,o,c);(!l||l&&l===r)&&(n.parent._tail===n&&n._tweenType===N.TRANSFORM&&n._prev&&n._prev._tweenType===N.TRANSFORM&&(n._prev._renderTransforms=1),Ee(e,n),gs(n),i=!0)}},!0),i},bs=(t,e,s)=>{const i=Mt(t),n=e||M,o=e&&e.controlAnimation&&e;for(let c=0,l=i.length;c<l;c++){const a=i[c];_n(a,s,o)}let r;if(n._hasChildren){let c=0;R(n,l=>{if(!l._hasChildren)if(r=es(i,l,s),r&&!l._head)l.cancel(),Ee(n,l);else{const u=l._offset+l._delay+l.duration;u>c&&(c=u)}l._head?bs(t,l,s):l._hasChildren=!1},!0),m(n.iterationDuration)||(n.iterationDuration=c)}else r=es(i,n,s);return r&&!n._head&&(n._hasChildren=!1,n.cancel&&n.cancel()),i},pn=(t,e)=>{if(Ue(e,"<")){const s=e[1]==="<",i=t._tail,n=i?i._offset+i._delay:0;return s?n:n+i.duration}},ht=(t,e)=>{let s=t.iterationDuration;if(s===v&&(s=0),m(e))return s;if(gt(+e))return+e;const i=e,n=t?t.labels:null,o=!ct(n),r=pn(t,i),c=!m(r),l=ws.exec(i);if(l){const a=l[0],u=i.split(a),d=o&&u[0]?n[u[0]]:s,p=c?r:o?d:s,h=+u[1];return Ot(p,h,a[0])}else return c?r:o?m(n[i])?s:n[i]:s};function mn(t){return yt((t.iterationDuration+t._loopDelay)*t.iterationCount-t._loopDelay)||v}function Dt(t,e,s,i,n,o){const c=gt(t.duration)&&t.duration<=v?s-v:s;we(e,c,1,1,ne.AUTO);const l=i?new Cs(i,t,e,c,!1,n,o):new Ft(t,e,c);return l.init(1),Me(e,l),R(e,a=>{const d=a._offset+a._delay+a.duration;d>e.iterationDuration&&(e.iterationDuration=d)}),e.duration=mn(e),e}class Tn extends Ft{constructor(e={}){super(e,null,0),this.duration=0,this.labels={};const s=e.defaults,i=w.defaults;this.defaults=s?ds(s,i):i,this.onRender=e.onRender||i.onRender;const n=V(e.playbackEase,i.playbackEase);this._ease=n?mt(n):null,this.iterationDuration=0}add(e,s,i){const n=je(s),o=je(e);if(n||o){if(this._hasChildren=!0,n){const r=s;if(Ye(i)){const c=i,l=Mt(e),a=this.duration,u=this.iterationDuration,d=r.id;let p=0;const h=l.length;l.forEach(S=>{const y={...r};this.duration=a,this.iterationDuration=u,m(d)||(y.id=d+"-"+p),Dt(y,this,c(S,p,h,this),S,p,h),p++})}else Dt(r,this,ht(this,i),e)}else Dt(e,this,ht(this,s));return this.init(1)}}sync(e,s){if(m(e)||e&&m(e.pause))return this;e.pause();const i=+(e.effect?e.effect.getTiming().duration:e.duration);return this.add(e,{currentTime:[0,i],duration:i,ease:"linear"},s)}set(e,s,i){return m(s)?this:(s.duration=v,s.composition=J.replace,this.add(e,s,i))}call(e,s){return m(e)||e&&!Ye(e)?this:this.add({duration:0,onComplete:()=>e(this)},s)}label(e,s){return m(e)||e&&!st(e)?this:(this.labels[e]=ht(this,s),this)}remove(e,s){return bs(e,this,s),this}stretch(e){const s=this.duration;if(s===qe(e))return this;const i=e/s,n=this.labels;R(this,o=>o.stretch(o.duration*i));for(let o in n)n[o]*=i;return super.stretch(e)}refresh(){return R(this,e=>{e.refresh&&e.refresh()}),this}revert(){return super.revert(),R(this,e=>e.revert,!0),Vt(this)}then(e){return super.then(e)}}const vn=t=>new Tn(t).init(),Sn=(t,e={})=>{let s=[],i=0;const n=e.from,o=e.reversed,r=e.ease,c=!m(r),a=c&&!m(r.ease)?r.ease:c?mt(r):null,u=e.grid,d=e.axis,p=m(n)||n===0||n==="first",h=n==="center",S=n==="last",y=He(t),k=Le(y?t[0]:t),ce=y?Le(t[1]):0,X=is.exec((y?t[1]:t)+Ae),E=e.start||0+(y?k:0);let L=p?0:gt(n)?n:0;return(D,j,W,H)=>{if(h&&(L=(W-1)/2),S&&(L=W-1),!s.length){for(let C=0;C<W;C++){if(!u)s.push(Et(L-C));else{const Re=h?(u[0]-1)/2:L%u[0],Y=h?(u[1]-1)/2:_t(L/u[0]),O=C%u[0],ee=_t(C/u[0]),ue=Re-O,x=Y-ee;let f=fs(ue*ue+x*x);d==="x"&&(f=-ue),d==="y"&&(f=-x),s.push(f)}i=ks(...s)}a&&(s=s.map(C=>a(C/i)*i)),o&&(s=s.map(C=>d?C<0?C*-1:-C:Et(i-C)))}const de=y?(ce-k)/i:k;let $=(H?ht(H,m(e.start)?H.iterationDuration:E):E)+(de*A(s[j],2)||0);return e.modifier&&($=e.modifier($)),X&&($=`${$}${X[2]}`),$}};export{yn as a,vn as c,M as e,Sn as s};
