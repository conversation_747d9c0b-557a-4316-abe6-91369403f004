const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/animations-Cpx74AEx.js","assets/animations-DumLLhGv.js"])))=>i.map(i=>d[i]);
import{t as St,c as Qe,r as b,u as mt,j as e,R as se,z as At,F as It,a as Lt,b as Et,d as Dt,e as Pt,f as Se,g as xt,h as Rt,i as Tt,L as $t,B as Ot,D as ut,k as Bt,N as Fe,l as zt,m as Wt}from"./vendor-eYbLVx36.js";import{i as ht,f as Ut,e as Xe,s as Ze,a as gt,b as pt,d as _t,g as Ft,p as Kt,z as ze,h as Vt,j as et,k as Ht,l as qt,m as Gt,n as Je}from"./utils-DXIKCEYF.js";import{a as H,c as Qt,s as Jt}from"./animations-DumLLhGv.js";import{C as Yt,a as Xt,b as Zt,P as es,c as ts,d as ss,A as rs,p as as,e as ns,f as is,i as ls}from"./charts-DTefc40_.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function s(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerPolicy&&(i.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?i.credentials="include":a.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(a){if(a.ep)return;a.ep=!0;const i=s(a);fetch(a.href,i)}})();function os(n){const{weight:t,height:s,age:r,gender:a}=n,i=10*t+6.25*s-5*r,l=a==="male"?i+5:i-161;return Math.round(l)}const ds={sedentary:1.2,light:1.375,moderate:1.55,active:1.725,veryActive:1.9};function cs(n,t){const s=n*ds[t];return Math.round(s)}function ms(n,t,s){const a=t*7700/s,i=n*.8,l=n-a;return Math.round(Math.max(l,i))}function xs(n,t){return{breakfast:Math.round(n*t.breakfast),lunch:Math.round(n*t.lunch),dinner:Math.round(n*t.dinner)}}function tt(n){const t=os(n),s=cs(t,n.activityLevel),r=n.weight-n.targetWeight,a=ms(s,r,n.targetDays),i={breakfast:.3,lunch:.4,dinner:.3},l=n.mealRatios||i,o=xs(a,l),c=(s-a)*7/7700;return{bmr:t,tdee:s,dailyCalorieLimit:a,weightLossRate:c,mealCalories:o}}function us(n,t,s,r){const a=t/100,i=n/(a*a),l=18.5*a*a,o=24.9*a*a,c={sedentary:.8,light:1,moderate:1.2,active:1.4,veryActive:1.6}[r]||1,d=s/7,N=.5*c,w=d*N;let f;if(i>24.9){const m=n-o,y=Math.min(w,m);f=n-Math.max(y,2)}else if(i<18.5)f=l;else{const m=Math.min(w*.7,5);f=Math.max(n-m,l)}return f=Math.max(f,l),f=Math.min(f,n),Math.round(f*100)/100}function hs(n,t,s,r,a){if(r&&a){const o=us(n,r,s,a);if(Math.abs(t-o)<=.1)return{isValid:!0}}const i=n-t,l=i/s*7;return i<=0?{isValid:!0,isWarning:!0,message:"目标体重应低于当前体重"}:l>1.2?{isValid:!0,isWarning:!0,message:"减重速度过快，建议每周减重不超过1.2kg"}:l<.1?{isValid:!0,isWarning:!0,message:"减重速度过慢，建议每周减重至少0.1kg"}:l>1?{isValid:!0,isWarning:!0,message:"减重速度较快，请注意营养均衡和身体状况"}:l<.3?{isValid:!0,isWarning:!0,message:"减重速度较慢，可以适当增加运动量"}:{isValid:!0}}function q(n,t="yyyy-MM-dd"){const s=We(n);return ht(s)?Ut(s,t,{locale:ze}):""}function We(n){if(n instanceof Date)return n;if(typeof n=="string"){const t=new Date(n);if(ht(t))return t;try{return Kt(n,"yyyy-MM-dd",new Date)}catch(s){return console.error("日期解析错误:",s),new Date}}return typeof n=="number"?new Date(n):new Date}function gs(n,t=new Date){const s=We(t);switch(n){case"day":return{start:Ze(s),end:Xe(s)};case"week":return{start:Ft(s,{locale:ze}),end:_t(s,{locale:ze})};case"month":return{start:pt(s),end:gt(s)};default:return{start:Ze(s),end:Xe(s)}}}function st(n,t,s){const r=We(n),a=We(t);switch(s){case"day":return et(r,a);case"week":return qt(r,a,{locale:ze});case"month":return Ht(r,a);default:return et(r,a)}}function ps(n,t){const s=new Date(n,t-1,1),r=[],a=pt(s),i=gt(s);let l=a;for(;l<=i;)r.push(new Date(l)),l=Vt(l,1);return r}function V(...n){return St(Gt(n))}function F(n,t){if(n==null){const a=t.fallback||"0";return`${t.prefix||""}${a}${t.unit?` ${t.unit}`:""}${t.suffix||""}`}const s=typeof n=="string"?parseFloat(n):n;if(isNaN(s)||!isFinite(s)){const a=t.fallback||"0";return`${t.prefix||""}${a}${t.unit?` ${t.unit}`:""}${t.suffix||""}`}let r;switch(t.precision){case"integer":r=Math.round(s).toString();break;case"decimal":s%1===0?r=s.toString():r=s.toFixed(2);break;case"auto":r=s%1===0?s.toString():parseFloat(s.toFixed(2)).toString();break;default:typeof t.precision=="number"?(r=s.toFixed(t.precision),t.precision===0&&(r=Math.round(s).toString())):r=s%1===0?s.toString():parseFloat(s.toFixed(2)).toString()}return`${t.prefix||""}${r}${t.unit?` ${t.unit}`:""}${t.suffix||""}`}function rt(n,t){return F(n,{precision:"decimal",unit:t})}function at(n){return F(n,{precision:"integer",unit:"kcal"})}function Y(n,t){return F(n,{precision:"integer",unit:t})}function nt(n,t){return F(n,{precision:1,unit:t})}class it{static set(t,s){try{const r=JSON.stringify(s);return localStorage.setItem(t,r),!0}catch(r){return console.error("LocalStorage set error:",r),!1}}static get(t,s){try{const r=localStorage.getItem(t);return r===null?s||null:JSON.parse(r)}catch(r){return console.error("LocalStorage get error:",r),s||null}}static remove(t){try{return localStorage.removeItem(t),!0}catch(s){return console.error("LocalStorage remove error:",s),!1}}static clear(){try{return localStorage.clear(),!0}catch(t){return console.error("LocalStorage clear error:",t),!1}}static has(t){return localStorage.getItem(t)!==null}static keys(){return Object.keys(localStorage)}static getSize(){let t=0;for(const s in localStorage)localStorage.hasOwnProperty(s)&&(t+=localStorage[s].length+s.length);return t}}class bs{static cache=new Map;static set(t,s,r=60){const a=r*60*1e3;this.cache.set(t,{data:s,timestamp:Date.now(),ttl:a})}static get(t){const s=this.cache.get(t);return s?Date.now()-s.timestamp>s.ttl?(this.cache.delete(t),null):s.data:null}static delete(t){return this.cache.delete(t)}static clear(){this.cache.clear()}static cleanup(){const t=Date.now();for(const[s,r]of this.cache.entries())t-r.timestamp>r.ttl&&this.cache.delete(s)}static size(){return this.cache.size}}setInterval(()=>{bs.cleanup()},5*60*1e3);const ge=Qe()(Je((n,t)=>({profile:null,isProfileComplete:!1,loading:!1,error:null,createProfile:async s=>{try{n({loading:!0,error:null});const r=tt({weight:s.weight,height:s.height,age:s.age,gender:s.gender,targetWeight:s.targetWeight,targetDays:s.targetDays,activityLevel:s.activityLevel}),a={id:`user_${Date.now()}`,createdAt:new Date,updatedAt:new Date,name:s.name,height:s.height,weight:s.weight,age:s.age,gender:s.gender,targetWeight:s.targetWeight,targetDays:s.targetDays,activityLevel:s.activityLevel,bmr:r.bmr,tdee:r.tdee,dailyCalorieLimit:r.dailyCalorieLimit,mealRatios:{breakfast:.3,lunch:.4,dinner:.3},preferences:{theme:"system",language:"zh-CN",notifications:{mealReminders:!0,dailySummary:!0,weeklyReport:!1},units:{weight:"kg",height:"cm"}}};n({profile:a,isProfileComplete:!0,loading:!1})}catch(r){const a=r instanceof Error?r.message:"创建档案失败";throw n({error:a,loading:!1}),r}},updateProfile:async s=>{try{const r=t().profile;if(!r)throw new Error("用户档案不存在");n({loading:!0,error:null});let a={...r,...s,updatedAt:new Date,preferences:s.preferences?{...r.preferences,...s.preferences}:r.preferences};if(s.weight!==void 0||s.height!==void 0||s.age!==void 0||s.gender!==void 0||s.targetWeight!==void 0||s.targetDays!==void 0||s.activityLevel!==void 0){const l=tt({weight:a.weight,height:a.height,age:a.age,gender:a.gender,targetWeight:a.targetWeight,targetDays:a.targetDays,activityLevel:a.activityLevel});a={...a,bmr:l.bmr,tdee:l.tdee,dailyCalorieLimit:l.dailyCalorieLimit}}n({profile:a,loading:!1})}catch(r){const a=r instanceof Error?r.message:"更新档案失败";throw n({error:a,loading:!1}),r}},clearProfile:()=>{n({profile:null,isProfileComplete:!1,error:null})},setLoading:s=>{n({loading:s})},setError:s=>{n({error:s})}}),{name:"user-profile-storage",partialize:n=>({profile:n.profile,isProfileComplete:n.isProfileComplete})})),fs=(n,t={})=>H(n,{opacity:[0,1],duration:t.duration||500,delay:t.delay||0,easing:t.easing||"easeOutQuart",complete:t.complete}),vs=(n,t={})=>H(n,{translateY:[20,0],opacity:[0,1],duration:t.duration||600,delay:t.delay||0,easing:t.easing||"easeOutExpo",complete:t.complete}),js=(n,t={})=>H(n,{scale:[.8,1],opacity:[0,1],duration:t.duration||400,delay:t.delay||0,easing:t.easing||"easeOutBack",complete:t.complete}),ys=(n,t={})=>H(n,{translateY:[30,0],opacity:[0,1],duration:t.duration||800,delay:Jt(t.stagger||100),easing:t.easing||"easeOutExpo",complete:t.complete}),ws=(n,t={})=>H(n,{translateY:[0,-10,0],duration:t.duration||600,delay:t.delay||0,easing:"easeInOutQuad",loop:t.loop||!1,complete:t.complete}),Ns=(n,t={})=>H(n,{scale:[1,1.05,1],duration:t.duration||1e3,delay:t.delay||0,easing:"easeInOutSine",loop:t.loop||!0,complete:t.complete}),ks=(n,t,s={})=>H(n,{width:`${t}%`,duration:s.duration||1e3,delay:s.delay||0,easing:s.easing||"easeOutExpo",complete:s.complete}),Cs=(n,t,s={})=>{const a={value:s.startValue||0},i=n.querySelector("[data-react-component]")||n.closest("[data-react-component]")||n.innerHTML.includes("kcal");return i?(s.complete&&setTimeout(s.complete,s.delay||0),Promise.resolve()):H(a,{value:t,duration:s.duration||1500,delay:s.delay||0,easing:s.easing||"easeOutExpo",round:1,update:()=>{i||(n.textContent=Math.round(a.value).toString())},complete:s.complete})},Ms=(n,t={})=>H(n,{translateX:[0,-10,10,-10,10,0],duration:t.duration||500,delay:t.delay||0,easing:"easeInOutSine",complete:t.complete}),Ss=(n,t,s={})=>{const r=typeof n=="string"?document.querySelector(n):n;if(!r)return;const a=new IntersectionObserver(i=>{i.forEach(l=>{l.isIntersecting&&(t(),s.once!==!1&&a.unobserve(l.target))})},{threshold:.1,rootMargin:`${s.offset||0}px`});return a.observe(r),a},As=(n={})=>Qt({autoplay:n.autoplay!==!1}),He={fadeIn:fs,slideUp:vs,scaleIn:js,staggerIn:ys,bounce:ws,pulse:Ns,progressBar:ks,countUp:Cs,shake:Ms,createScrollTrigger:Ss,createTimeline:As},Is=(n="fadeIn",t={})=>{const s=b.useRef(null),[r,a]=b.useState(!1);return b.useEffect(()=>{if(!s.current)return;const i=new IntersectionObserver(l=>{l.forEach(o=>{if(o.isIntersecting&&!r){switch(a(!0),n){case"fadeIn":He.fadeIn(o.target);break;case"slideUp":He.slideUp(o.target);break;case"scaleIn":He.scaleIn(o.target);break}t.once!==!1&&i.unobserve(o.target)}})},{threshold:t.threshold||.1,rootMargin:t.rootMargin||"0px"});return i.observe(s.current),()=>{i.disconnect()}},[n,r,t]),s},bt=(n,t)=>{const s=b.useRef(null),r=b.useRef(null),a=b.useCallback(()=>{s.current&&(r.current&&r.current.pause(),r.current=H(s.current,{...n}))},[n]),i=b.useCallback(()=>{s.current&&(r.current&&r.current.pause(),r.current=H(s.current,{...t}))},[t]);return b.useEffect(()=>{const l=s.current;if(l)return l.addEventListener("mouseenter",a),l.addEventListener("mouseleave",i),()=>{l.removeEventListener("mouseenter",a),l.removeEventListener("mouseleave",i)}},[a,i]),s},ft=()=>{const[n,t]=b.useState(!1);b.useEffect(()=>{const r=window.matchMedia("(prefers-reduced-motion: reduce)");t(r.matches);const a=i=>{t(i.matches)};return r.addEventListener("change",a),()=>r.removeEventListener("change",a)},[]);const s=b.useCallback((r,a)=>{if(n){const i=typeof r=="string"?document.querySelector(r):r instanceof NodeList?r[0]:r;if(i&&i instanceof HTMLElement&&a.opacity!==void 0){const l=Array.isArray(a.opacity)?a.opacity[a.opacity.length-1]:a.opacity;i.style.opacity=String(l)}return null}return H(r,{...a})},[n]);return{prefersReducedMotion:n,createAccessibleAnimation:s}},ye=({children:n,className:t=""})=>{const s=mt(),r=b.useRef(null),{prefersReducedMotion:a,createAccessibleAnimation:i}=ft(),l=b.useRef(s.pathname);return b.useEffect(()=>{const o=r.current;if(!o)return;l.current!==s.pathname?(i(o,{opacity:[0,1],duration:600,easing:"easeOutQuart",begin:()=>{o.style.opacity="0"},complete:()=>{o.style.transform="none"}}),l.current=s.pathname):(o.style.opacity="1",o.style.transform="none")},[s.pathname,i]),e.jsx("div",{ref:r,className:t,style:{opacity:a?1:0,transform:"none",position:"relative",zIndex:"auto"},children:n})};class we{static instance;prompts=new Map;constructor(){this.initializeDefaultPrompts()}static getInstance(){return we.instance||(we.instance=new we),we.instance}initializeDefaultPrompts(){this.prompts.set("unified_image_food_recognition",{id:"unified_image_food_recognition",name:"统一图片食物识别",description:"用于识别单张或多张图片中的食物信息",category:"food_recognition",version:"3.0.0",template:`你是一个专业的营养师和食物识别专家。请仔细分析{{imageCount}}张食物图片，识别其中的所有食物并提供详细的营养信息。

**核心任务：**
1. **精确识别**：识别图片中的所有食物，包括主食、配菜、调料等
2. **重量/体积估算**：基于视觉线索（餐具大小、食物比例等）估算每种食物的重量(克)或体积(毫升)
3. **营养分析**：提供准确的营养成分数据，优先使用营养标签信息
4. **数据来源标注**：明确标注营养数据的来源（营养标签/视觉估算）
5. **单位适配**：根据食物类型选择合适的计量单位（固体用克，液体用毫升，个数用个）
{{#if multiImage}}6. **跨图片分析**：分析所有图片，识别不同角度或状态的同一食物，避免重复计算{{/if}}
// {{ AURA-X: Modify - 增强食物单位支持，包含毫升、个数等多种单位. Approval: 寸止(ID:1737099400). }}

**输出格式要求：**
1.内容上必须是中文的语言
2.必须严格返回纯JSON数据，禁止包含任何推理过程、解释文字、markdown标记或其他内容：

{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation",
      {{#if multiImage}}"imageIndex": 主要出现的图片索引(0开始),{{/if}}
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": true/false,
        "confidence": 置信度(0-1),
        "readableText": "标签文字内容"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low",
        "unitReasoning": "单位选择的理由"
      }
    }
  ],
  // {{ AURA-X: Modify - 增加unit和unitValue字段支持多种食物单位. Approval: 寸止(ID:1737099400). }}
  "analysisMetadata": {
    "hasNutritionLabel": true/false,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "visual_estimation",
    "processingNotes": "处理说明"
  }{{#if multiImage}},
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "duplicatesFound": 去重数量,
    "crossReferenceNotes": "跨图片分析说明"
  }{{/if}}
}

{{additionalContext}}`,variables:["imageCount","multiImage","additionalContext"],createdAt:new Date,updatedAt:new Date}),this.prompts.set("single_image_food_recognition",{id:"single_image_food_recognition",name:"增强版单图片食物识别",description:"从geminiService.ts迁移的高质量单图片食物识别提示词",category:"food_recognition",version:"3.0.0",template:`**系统角色：** 你是一位专业的营养师和食物识别专家，具备精确的营养成分分析能力和OCR文字识别技能。

**核心任务：** 分析图片中的食物并提供准确的营养信息。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

**分析优先级（按重要性排序）：**

1. **营养标签优先原则**
   - 首先仔细扫描图片中的营养成分表、营养标签、包装信息
   - 如发现营养标签，请精确读取以下信息：
     * 每份重量/体积
     * 每份卡路里
     * 蛋白质、脂肪、碳水化合物含量
     * 纤维、糖分、钠含量
     * 标注数据来源为 "nutrition_label"
     * 置信度设为 0.9-1.0（基于标签清晰度）

2. **视觉食物识别**
   - 识别所有可见的食物项目
   - 估算每种食物的重量（克）
   - 基于标准营养数据库计算营养成分
   - 标注数据来源为 "visual_estimation"
   - 置信度设为 0.6-0.8（基于识别清晰度）

3. **用户上下文整合**
{{#if additionalContext}}   - 用户提供的额外信息：{{additionalContext}}
   - 将此信息作为重要参考，用于修正识别结果
   - 如用户描述与视觉不符，优先考虑用户描述{{else}}   - 无额外用户信息{{/if}}

**详细分析要求：**

• **食物识别精度**：准确识别每种食物的名称、品牌（如有）
• **重量/体积估算**：基于视觉线索（餐具、包装、手部比例）估算重量(克)或体积(毫升)，根据食物类型选择合适单位
• **营养计算**：使用标准营养数据库进行精确计算
• **置信度评估**：真实反映识别的准确性和可靠性
• **数据来源标注**：明确区分营养标签数据和视觉估算数据

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "brand": "品牌名称（如有）",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "confidence": 置信度(0-1),
        "readableText": "营养标签文字内容",
        "servingSize": "每份重量/体积"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体（餐具、手部等）",
        "confidenceLevel": "high" | "medium" | "low",
        "visualCues": "视觉线索描述",
        "unitReasoning": "单位选择的理由"
      }
    }
    // {{ AURA-X: Modify - 单图片识别也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "analysisMetadata": {
    "hasNutritionLabel": 布尔值,
    "imageQuality": "high" | "medium" | "low",
    "recognitionMethod": "label_priority" | "visual_only" | "hybrid",
    "processingNotes": "分析过程中的重要注意事项"
  }
}

**关键处理规则：**
• **空图片检测**：如图片无食物内容，返回 {"foods": [], "analysisMetadata": {"hasNutritionLabel": false, "imageQuality": "unknown", "recognitionMethod": "none", "processingNotes": "未检测到食物内容"}}
• **营养标签优先**：发现标签时，优先使用标签数据，置信度0.9+
• **视觉估算补充**：无标签时使用视觉估算，置信度0.6-0.8
• **混合模式**：标签+视觉结合时，dataSource设为"hybrid"
• **分量计算**：区分包装总重量和实际食用重量
• **品质控制**：确保所有数值合理，营养成分总和符合卡路里计算

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`,variables:["additionalContext"],createdAt:new Date,updatedAt:new Date}),this.prompts.set("multi_image_food_recognition",{id:"multi_image_food_recognition",name:"增强版多图片食物识别",description:"从geminiService.ts迁移的高质量多图片食物识别提示词，支持个性化建议",category:"food_recognition",version:"3.0.0",template:`**系统角色：** 你是一位顶级的营养师和多模态食物识别专家，具备同时分析多张图片并提供综合营养建议的能力。

**核心任务：** 分析这{{imageCount}}张图片中的所有食物，提供准确的营养信息和个性化建议。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

{{#if userContext}}
用户个人信息：
- 年龄：{{userContext.age}}岁
- 性别：{{userContext.gender}}
- 体重：{{userContext.weight}}kg
- 身高：{{userContext.height}}cm
- 目标体重：{{userContext.targetWeight}}kg
- 活动水平：{{userContext.activityLevel}}
- 基础代谢率：{{userContext.bmr}} kcal/天
- 总消耗：{{userContext.tdee}} kcal/天
- 目标天数：{{userContext.targetDays}}天

请根据以上个人信息提供个性化的营养建议和运动建议。
{{/if}}

{{#if additionalContext}}**用户补充信息：** {{additionalContext}}{{/if}}

**多图片分析策略：**

1. **跨图片食物识别**
   - 逐张分析每张图片中的食物
   - 识别重复出现的食物（避免重复计算）
   - 检测食物的不同角度或状态
   - 合并相同食物的营养信息

2. **营养标签优先处理**
   - 优先识别任何图片中的营养标签
   - 将标签信息与对应的食物匹配
   - 使用标签数据校正视觉估算结果

3. **综合营养分析**
   - 计算所有食物的总营养成分
   - 分析营养搭配的合理性
   - 识别营养缺口或过量风险

4. **个性化建议生成**
   - 基于用户的个人数据提供定制建议
   - 计算与目标的差距和改进方向
   - 提供具体的运动消耗建议

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "brand": "品牌名称（如有）",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "nutrition_label" | "visual_estimation" | "hybrid",
      "imageIndex": 主要出现的图片索引(0开始),
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "labelInfo": {
        "hasLabel": 布尔值,
        "confidence": 置信度(0-1),
        "readableText": "营养标签文字内容",
        "servingSize": "每份重量/体积"
      },
      "portionAnalysis": {
        "estimatedPortion": "份量描述",
        "referenceObject": "参考物体",
        "confidenceLevel": "high" | "medium" | "low",
        "visualCues": "视觉线索描述",
        "unitReasoning": "单位选择的理由"
      }
    }
    // {{ AURA-X: Modify - 多图片识别也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "personalizedAdvice": "基于用户个人数据的详细个性化营养建议",
  "exerciseAdvice": "针对用户的专属运动消耗建议",
  "multiImageAnalysis": {
    "totalImages": {{imageCount}},
    "foodItemsFound": 识别到的食物项目总数,
    "duplicatesDetected": 检测到的重复食物数量,
    "overallNutritionSummary": {
      "totalCalories": 总卡路里,
      "totalProtein": 总蛋白质,
      "totalFat": 总脂肪,
      "totalCarbs": 总碳水化合物
    }
  }
}

**关键处理要求：**
• **跨图片一致性**：确保相同食物在不同图片中的识别结果一致
• **营养标签优先**：任何图片中的营养标签都应优先使用
• **重复检测**：避免将同一食物在不同图片中重复计算
• **个性化建议**：基于用户信息提供具体的营养和运动建议
• **质量控制**：所有营养数据必须合理且相互一致
• **图片索引**：为每个食物标注来源图片的索引号

**输出要求：直接返回JSON，无任何额外文字、标记或解释**`,variables:["imageCount","additionalContext","userContext"],createdAt:new Date,updatedAt:new Date}),this.prompts.set("text_food_analysis",{id:"text_food_analysis",name:"增强版文本食物分析",description:"从geminiService.ts迁移的高质量文本食物分析提示词",category:"text_analysis",version:"3.0.0",template:`**系统角色：** 你是一位专业的营养师和食物分析专家，具备从文字描述中精确提取食物信息的能力。

**核心任务：** 从用户的文字描述中识别食物并提供准确的营养分析。

**重要：
1.必须严格返回纯JSON格式，绝对不要任何markdown标记、代码块符号或解释文字
2.内容上必须是中文的语言
**

**用户输入文本：** "{{text}}"

**分析策略：**

1. **智能文本解析**
   - 识别所有明确提到的食物名称
   - 提取数量、重量(克)、体积(毫升)、个数等量化信息，选择最合适的单位
   - 识别烹饪方式、调料、配菜等影响营养的因素
   - 理解口语化表达（如"一碗"、"一份"、"半个"等）

2. **上下文推理**
   - 根据描述推断食物的大致分量
   - 考虑常见的食物搭配和用餐习惯
   - 基于烹饪方式调整营养成分（如油炸vs蒸煮）
   - 识别可能的隐含食物（如汉堡包含面包、肉饼、蔬菜等）

3. **营养数据估算**
   - 使用标准营养数据库进行计算
   - 根据烹饪方式调整营养成分
   - 考虑食物组合的营养交互作用
   - 提供合理的营养成分分布

**严格JSON输出格式：**
{
  "foods": [
    {
      "name": "食物名称",
      "weight": 重量数值(克),
      "unit": "g" | "ml" | "个" | "份",
      "unitValue": 对应单位的数值,
      "calories": 卡路里数值,
      "confidence": 置信度(0-1),
      "dataSource": "text_analysis",
      "nutrition": {
        "protein": 蛋白质(克),
        "fat": 脂肪(克),
        "carbs": 碳水化合物(克),
        "fiber": 膳食纤维(克),
        "sugar": 糖分(克),
        "sodium": 钠含量(毫克)
      },
      "textAnalysis": {
        "originalText": "原始描述文字",
        "inferredWeight": "重量推理依据",
        "contextClues": "上下文线索",
        "cookingMethod": "烹饪方式",
        "portionReference": "分量参考标准",
        "unitReasoning": "单位选择的理由"
      },
      "ambiguityFactors": {
        "weightUncertainty": "重量不确定性说明",
        "preparationVariance": "制作方式变化影响",
        "portionVariability": "分量变化范围"
      }
    }
    // {{ AURA-X: Modify - 文本分析也增加多单位支持. Approval: 寸止(ID:1737099400). }}
  ],
  "analysisMetadata": {
    "textQuality": "high" | "medium" | "low",
    "ambiguityLevel": "low" | "medium" | "high",
    "extractedFoodCount": 提取的食物数量,
    "processingNotes": "分析过程中的重要注意事项",
    "linguisticComplexity": "语言复杂度评估",
    "contextualRichness": "上下文丰富度"
  }
}

**关键处理规则：**
• **空文本检测**：如文字无食物信息，返回 {"foods": [], "analysisMetadata": {"textQuality": "low", "ambiguityLevel": "high", "extractedFoodCount": 0, "processingNotes": "未识别到明确的食物信息"}}
• **量化推理**：将模糊描述转换为具体重量（如"一碗米饭"→150g）
• **营养调整**：根据烹饪方式调整营养成分（如炒菜增加油脂）
• **置信度评估**：基于描述的明确程度和常见程度设定置信度
• **上下文理解**：考虑食物搭配和用餐场景的合理性

**输出要求：
1、直接返回JSON，无任何额外文字、标记或解释**
2、内容上必须是中文的语言
`,variables:["text"],createdAt:new Date,updatedAt:new Date}),this.prompts.set("nutrition_advice",{id:"nutrition_advice",name:"营养建议分析",description:"基于用户数据提供个性化营养建议",category:"nutrition_analysis",version:"1.0.0",template:`你是一位专业的营养师。请基于用户的个人信息和当前饮食记录，提供个性化的营养建议。

用户信息：
- 年龄：{{age}}岁
- 性别：{{gender}}
- 身高：{{height}}cm
- 体重：{{weight}}kg
- 目标体重：{{targetWeight}}kg
- 活动水平：{{activityLevel}}
- 每日卡路里目标：{{dailyCalorieLimit}}卡

当前饮食记录：
{{nutritionData}}

请提供简洁实用的营养建议，包括：
1. 当前饮食评估
2. 营养平衡建议
3. 具体改进措施
4. 健康提醒

建议应该实用、具体、易于执行。`,variables:["age","gender","height","weight","targetWeight","activityLevel","dailyCalorieLimit","nutritionData"],createdAt:new Date,updatedAt:new Date})}addPrompt(t){const s={...t,createdAt:new Date,updatedAt:new Date};this.prompts.set(t.id,s)}getPrompt(t){return this.prompts.get(t)}renderPrompt(t,s={}){const r=this.getPrompt(t);if(!r)throw new Error(`提示词模板不存在: ${t}`);let a=r.template;return a=a.replace(/\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g,(i,l,o)=>s[l]?o:""),r.variables&&r.variables.forEach(i=>{const l=s[i]||"",o=`{{${i}}}`;a=a.replace(new RegExp(o,"g"),String(l))}),a=a.replace(/\{\{(\w+)\.(\w+)\}\}/g,(i,l,o)=>{const x=s[l];return x&&typeof x=="object"&&o in x?String(x[o]):""}),s.additionalContext?a=a.replace("{{additionalContext}}",`

**补充信息：**
${s.additionalContext}`):a=a.replace("{{additionalContext}}",""),a.trim()}getAllPrompts(){return Array.from(this.prompts.values())}getPromptsByCategory(t){return this.getAllPrompts().filter(s=>s.category===t)}updatePrompt(t,s){const r=this.prompts.get(t);if(!r)throw new Error(`提示词模板不存在: ${t}`);const a={...r,...s,updatedAt:new Date};this.prompts.set(t,a)}deletePrompt(t){return this.prompts.delete(t)}}const Ue=we.getInstance();class Ls{name="gemini";apiKey="";baseUrl="";modelName="";timeout=3e4;initialize(t){this.apiKey=t.apiKey,this.baseUrl=t.baseUrl,this.modelName=t.modelName,this.timeout=t.timeout||3e4}async recognizeFood(t,s,r){if(!this.apiKey)throw new Error("Gemini API密钥未配置");try{const a=await Promise.all(t.map(l=>this.fileToBase64(l))),i=await this.sendImageRequest(a,s,r);return this.parseImageResponse(i)}catch(a){throw console.error("Gemini食物识别失败:",a),new Error(a instanceof Error?a.message:"Gemini食物识别失败")}}async analyzeText(t){if(!this.apiKey)throw new Error("Gemini API密钥未配置");try{const s=await this.sendTextRequest(t);return this.parseTextResponse(s)}catch(s){throw console.error("Gemini文本分析失败:",s),new Error(s instanceof Error?s.message:"Gemini文本分析失败")}}async analyzeNutritionAdvice(t){if(!this.apiKey)throw new Error("Gemini API密钥未配置");try{const s=await this.sendAdviceRequest(t);return this.parseAdviceResponse(s)}catch(s){throw console.error("Gemini营养建议分析失败:",s),new Error(s instanceof Error?s.message:"Gemini营养建议分析失败")}}async validateConfig(t){try{const s=await fetch(`${t.baseUrl}/v1beta/models`,{headers:{"x-goog-api-key":t.apiKey,"Content-Type":"application/json"}});if(!s.ok)throw new Error(`API请求失败: ${s.status} ${s.statusText}`);const r=await s.json();if(!r.models||!Array.isArray(r.models))throw new Error("API响应格式不正确");return{isValid:!0,modelList:r.models.map(a=>a.name.replace("models/",""))}}catch(s){return{isValid:!1,error:s instanceof Error?s.message:"验证失败"}}}async getAvailableModels(t){const s=await fetch(`${t.baseUrl}/v1beta/models`,{headers:{"x-goog-api-key":t.apiKey,"Content-Type":"application/json"}});if(!s.ok)throw new Error(`获取Gemini模型列表失败: ${s.status}`);return(await s.json()).models.map(a=>a.name.replace("models/",""))}async fileToBase64(t){return new Promise((s,r)=>{const a=new FileReader;a.onload=()=>{const l=a.result.split(",")[1];s(l)},a.onerror=r,a.readAsDataURL(t)})}async sendImageRequest(t,s,r){const a=new AbortController,i=setTimeout(()=>a.abort(),this.timeout);try{const o=t.length>1?"multi_image_food_recognition":"single_image_food_recognition",x=Ue.renderPrompt(o,{imageCount:t.length,additionalContext:s||"",userContext:r||null}),c=t.map(p=>({inline_data:{mime_type:"image/jpeg",data:p}})),d=await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`,{method:"POST",signal:a.signal,headers:{"Content-Type":"application/json","x-goog-api-key":this.apiKey},body:JSON.stringify({contents:[{parts:[{text:x},...c]}],generationConfig:{temperature:.05,candidateCount:1},safetySettings:[{category:"HARM_CATEGORY_HARASSMENT",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_HATE_SPEECH",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_SEXUALLY_EXPLICIT",threshold:"BLOCK_MEDIUM_AND_ABOVE"},{category:"HARM_CATEGORY_DANGEROUS_CONTENT",threshold:"BLOCK_MEDIUM_AND_ABOVE"}]})});if(!d.ok){const p=await d.text();throw new Error(`API请求失败: ${d.status} ${d.statusText} - ${p}`)}return await d.json()}catch(l){throw l instanceof Error&&l.name==="AbortError"?new Error("请求超时，请重试"):l}finally{clearTimeout(i)}}async sendTextRequest(t){const s=new AbortController,r=setTimeout(()=>s.abort(),this.timeout);try{const a=Ue.renderPrompt("text_food_analysis",{text:t}),i=await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`,{method:"POST",signal:s.signal,headers:{"Content-Type":"application/json","x-goog-api-key":this.apiKey},body:JSON.stringify({contents:[{parts:[{text:a}]}],generationConfig:{temperature:.05,candidateCount:1}})});if(!i.ok){const l=await i.text();throw new Error(`API请求失败: ${i.status} ${i.statusText} - ${l}`)}return await i.json()}catch(a){throw a instanceof Error&&a.name==="AbortError"?new Error("请求超时，请重试"):a}finally{clearTimeout(r)}}async sendAdviceRequest(t){const s=new AbortController,r=setTimeout(()=>s.abort(),this.timeout);try{const a=await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`,{method:"POST",signal:s.signal,headers:{"Content-Type":"application/json","x-goog-api-key":this.apiKey},body:JSON.stringify({contents:[{parts:[{text:t}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:2048}})});if(!a.ok){const i=await a.text();throw new Error(`API请求失败: ${a.status} ${a.statusText} - ${i}`)}return await a.json()}catch(a){throw a instanceof Error&&a.name==="AbortError"?new Error("请求超时，请重试"):a}finally{clearTimeout(r)}}parseImageResponse(t){try{if(!t.candidates||!t.candidates[0]||!t.candidates[0].content)throw new Error("API响应格式不正确");const r=t.candidates[0].content.parts[0].text.replace(/```json\s*|\s*```/g,"").trim(),a=JSON.parse(r);if(!a.foods||!Array.isArray(a.foods))throw new Error("响应中缺少foods字段或格式不正确");return a}catch(s){throw console.error("解析图片识别响应失败:",s),new Error("解析AI响应失败，请重试")}}parseTextResponse(t){try{if(!t.candidates||!t.candidates[0]||!t.candidates[0].content)throw new Error("API响应格式不正确");const r=t.candidates[0].content.parts[0].text.replace(/```json\s*|\s*```/g,"").trim(),a=JSON.parse(r);if(!a.foods||!Array.isArray(a.foods))throw new Error("响应中缺少foods字段或格式不正确");return a}catch(s){throw console.error("解析文本分析响应失败:",s),new Error("解析AI响应失败，请重试")}}parseAdviceResponse(t){try{if(!t.candidates||!t.candidates[0]||!t.candidates[0].content)throw new Error("API响应格式不正确");return{advice:t.candidates[0].content.parts[0].text.trim(),rawResponse:t}}catch(s){throw console.error("解析营养建议响应失败:",s),new Error("解析AI响应失败，请重试")}}}class Es{name="openai";apiKey="";baseUrl="";originalBaseUrl="";modelName="";timeout=6e4;initialize(t){this.apiKey=t.apiKey,this.originalBaseUrl=t.baseUrl,this.baseUrl=this.normalizeBaseUrl(t.baseUrl),this.modelName=t.modelName,this.timeout=t.timeout||3e4}normalizeBaseUrl(t){if(console.log("🔍 [OpenAIProvider.normalizeBaseUrl] 输入URL:",t),t.endsWith("#")){console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 检测到#结尾URL");const r=t.slice(0,-1);return console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 直接使用#之前的完整地址:",r),r}else if(t.includes("/chat/completions")||t.includes("/completions")){console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 检测到完整API URL");const r=t.split("/");console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 路径部分:",r);const a=r.findIndex(i=>i==="completions"||i==="chat");if(a>0){const i=r.slice(0,a).join("/");return console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 提取的基础URL:",i),i}}const s=t.replace(/\/+$/,"");return console.log("🔧 [OpenAIProvider.normalizeBaseUrl] 普通URL处理结果:",s),s}buildApiUrl(t){if(console.log("🔍 [OpenAIProvider.buildApiUrl] 构建URL:",{endpoint:t,originalBaseUrl:this.originalBaseUrl,normalizedBaseUrl:this.baseUrl,endsWithHash:this.originalBaseUrl.endsWith("#")}),this.originalBaseUrl.endsWith("#")){const r=this.baseUrl;return console.log("🚀 [OpenAIProvider.buildApiUrl] #结尾URL直接使用完整地址:",r),r}const s=`${this.baseUrl}/v1${t}`;return console.log("🚀 [OpenAIProvider.buildApiUrl] 普通URL构建结果:",s),s}async recognizeFood(t,s,r){if(!this.apiKey)throw new Error("OpenAI API密钥未配置");try{const a=await Promise.all(t.map(l=>this.fileToBase64(l))),i=await this.sendImageRequest(a,s,r);return this.parseImageResponse(i)}catch(a){throw console.error("OpenAI食物识别失败:",a),new Error(a instanceof Error?a.message:"OpenAI食物识别失败")}}async analyzeText(t){if(!this.apiKey)throw new Error("OpenAI API密钥未配置");try{const s=await this.sendTextRequest(t);return this.parseTextResponse(s)}catch(s){throw console.error("OpenAI文本分析失败:",s),new Error(s instanceof Error?s.message:"OpenAI文本分析失败")}}async analyzeNutritionAdvice(t){if(!this.apiKey)throw new Error("OpenAI API密钥未配置");try{const s=await this.sendAdviceRequest(t);return this.parseAdviceResponse(s)}catch(s){throw console.error("OpenAI营养建议分析失败:",s),new Error(s instanceof Error?s.message:"OpenAI营养建议分析失败")}}async validateConfig(t){try{const s=this.normalizeBaseUrl(t.baseUrl),r=t.baseUrl.endsWith("#")?`${s}/models`:`${s}/v1/models`,a=await fetch(r,{headers:{Authorization:`Bearer ${t.apiKey}`,"Content-Type":"application/json"}});if(!a.ok)throw new Error(`API请求失败: ${a.status} ${a.statusText}`);const i=await a.json();if(!i.data||!Array.isArray(i.data))throw new Error("API响应格式不正确");return{isValid:!0,modelList:i.data.map(l=>l.id)}}catch(s){return{isValid:!1,error:s instanceof Error?s.message:"验证失败"}}}async getAvailableModels(t){console.log("🔍 [OpenAIProvider.getAvailableModels] 开始获取模型列表:",{baseUrl:t.baseUrl,endsWithHash:t.baseUrl.endsWith("#")});const s=this.normalizeBaseUrl(t.baseUrl);console.log("🔧 [OpenAIProvider.getAvailableModels] 标准化后的URL:",s);const r=t.baseUrl.endsWith("#")?`${s}/models`:`${s}/v1/models`;console.log("🚀 [OpenAIProvider.getAvailableModels] 最终API URL:",r);try{console.log("🔄 [OpenAIProvider.getAvailableModels] 尝试GET方法获取模型列表");const a=await fetch(r,{method:"GET",headers:{Authorization:`Bearer ${t.apiKey}`,"Content-Type":"application/json"}});if(a.ok){const o=await a.json();return console.log("✅ [OpenAIProvider.getAvailableModels] GET方法成功获取模型列表"),o.data.map(x=>x.id)}console.log("⚠️ [OpenAIProvider.getAvailableModels] GET方法失败，尝试POST方法回退");const i=await fetch(r,{method:"POST",headers:{Authorization:`Bearer ${t.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({})});if(!i.ok)throw new Error(`获取OpenAI模型列表失败: GET ${a.status}, POST ${i.status}`);const l=await i.json();return console.log("✅ [OpenAIProvider.getAvailableModels] POST方法成功获取模型列表"),l.data.map(o=>o.id)}catch(a){throw console.error("❌ [OpenAIProvider.getAvailableModels] 获取模型列表失败:",a),a}}async fileToBase64(t){return new Promise((s,r)=>{const a=new FileReader;a.onload=()=>{const i=a.result;s(i)},a.onerror=r,a.readAsDataURL(t)})}async sendImageRequest(t,s,r){const a=new AbortController,i=setTimeout(()=>a.abort(),this.timeout);try{const o=t.length>1?"multi_image_food_recognition":"single_image_food_recognition",c=[{type:"text",text:Ue.renderPrompt(o,{imageCount:t.length,additionalContext:s||"",userContext:r||null})}];t.forEach(N=>{c.push({type:"image_url",image_url:{url:N,detail:"high"}})});const d=this.buildApiUrl("/chat/completions"),p=await fetch(d,{method:"POST",signal:a.signal,headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:this.modelName,messages:[{role:"user",content:c}],temperature:.05})});if(!p.ok){const N=await p.text();throw new Error(`API请求失败: ${p.status} ${p.statusText} - ${N}`)}return await p.json()}catch(l){throw l instanceof Error&&l.name==="AbortError"?new Error("请求超时，请重试"):l}finally{clearTimeout(i)}}async sendTextRequest(t){const s=new AbortController,r=setTimeout(()=>s.abort(),this.timeout);try{const a=Ue.renderPrompt("text_food_analysis",{text:t}),i=this.buildApiUrl("/chat/completions"),l=await fetch(i,{method:"POST",signal:s.signal,headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:this.modelName,messages:[{role:"user",content:a}],temperature:.05})});if(!l.ok){const o=await l.text();throw new Error(`API请求失败: ${l.status} ${l.statusText} - ${o}`)}return await l.json()}catch(a){throw a instanceof Error&&a.name==="AbortError"?new Error("请求超时，请重试"):a}finally{clearTimeout(r)}}async sendAdviceRequest(t){const s=new AbortController,r=setTimeout(()=>s.abort(),this.timeout);try{const a=this.buildApiUrl("/chat/completions"),i=await fetch(a,{method:"POST",signal:s.signal,headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:this.modelName,messages:[{role:"user",content:t}],temperature:.7})});if(!i.ok){const l=await i.text();throw new Error(`API请求失败: ${i.status} ${i.statusText} - ${l}`)}return await i.json()}catch(a){throw a instanceof Error&&a.name==="AbortError"?new Error("请求超时，请重试"):a}finally{clearTimeout(r)}}parseImageResponse(t){try{if(!t.choices||!t.choices[0]||!t.choices[0].message)throw new Error("API响应格式不正确");const r=t.choices[0].message.content.replace(/```json\s*|\s*```/g,"").trim(),a=JSON.parse(r);if(!a.foods||!Array.isArray(a.foods))throw new Error("响应中缺少foods字段或格式不正确");return a}catch(s){throw console.error("解析OpenAI图片识别响应失败:",s),new Error("解析AI响应失败，请重试")}}parseTextResponse(t){try{if(!t.choices||!t.choices[0]||!t.choices[0].message)throw new Error("API响应格式不正确");const r=t.choices[0].message.content.replace(/```json\s*|\s*```/g,"").trim(),a=JSON.parse(r);if(!a.foods||!Array.isArray(a.foods))throw new Error("响应中缺少foods字段或格式不正确");return a}catch(s){throw console.error("解析OpenAI文本分析响应失败:",s),new Error("解析AI响应失败，请重试")}}parseAdviceResponse(t){try{if(!t.choices||!t.choices[0]||!t.choices[0].message)throw new Error("API响应格式不正确");return{advice:t.choices[0].message.content.trim(),rawResponse:t}}catch(s){throw console.error("解析OpenAI营养建议响应失败:",s),new Error("解析AI响应失败，请重试")}}}class Ds{providers=new Map;currentProvider=null;constructor(){this.initializeProviders()}initializeProviders(){this.providers.set("gemini",new Ls),this.providers.set("openai",new Es)}setProvider(t,s){const r=this.providers.get(t);if(!r)throw new Error(`不支持的AI提供商: ${t}`);r.initialize(s),this.currentProvider=r}getCurrentProvider(){return this.currentProvider}getProvider(t){return this.providers.get(t)||null}getSupportedProviders(){return Array.from(this.providers.keys())}async validateProviderConfig(t,s){const r=this.providers.get(t);return r?await r.validateConfig(s):{isValid:!1,error:`不支持的AI提供商: ${t}`}}async getProviderModels(t,s){const r=this.providers.get(t);if(!r)throw new Error(`不支持的AI提供商: ${t}`);return await r.getAvailableModels(s)}}const Ne=new Ds;function Te(n){const{provider:t,apiKey:s,baseUrl:r,modelName:a}=n;return btoa(`${t}-${s}-${r}-${a||""}`).replace(/[^a-zA-Z0-9]/g,"")}function Ps(){return typeof crypto<"u"&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const t=Math.random()*16|0;return(n==="x"?t:t&3|8).toString(16)})}function lt(n){const{provider:t,apiKey:s,baseUrl:r}=n;return btoa(`${t}-${s}-${r}`).replace(/[^a-zA-Z0-9]/g,"")}const je=Qe()(Je((n,t)=>({models:[],activeModelId:null,cachedModelData:{},validationStates:{},addModel:s=>{const r={...s,id:Ps(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n(a=>({models:[...a.models,r],activeModelId:a.models.length===0?r.id:a.activeModelId}))},updateModel:(s,r)=>{n(a=>({models:a.models.map(i=>i.id===s?{...i,...r,updatedAt:new Date().toISOString()}:i)}))},deleteModel:s=>{n(r=>({models:r.models.filter(a=>a.id!==s),activeModelId:r.activeModelId===s?null:r.activeModelId}))},setActiveModel:s=>{n({activeModelId:s})},getActiveModel:()=>{const s=t();return s.models.find(r=>r.id===s.activeModelId)||null},fixModelVisionSupport:()=>{const s=(r,a)=>{if(!a||a.trim()==="")return null;const i=a.toLowerCase();switch(r){case"openai":return i.includes("o")||i.startsWith("gpt")?!0:null;case"gemini":return i.includes("gemini")?!0:null;default:return null}};n(r=>({models:r.models.map(a=>({...a,supportsVision:s(a.provider,a.modelName),updatedAt:new Date().toISOString()}))}))},validateModel:async s=>{try{if(!s.provider||!s.apiKey||!s.baseUrl||!s.modelName)return{isValid:!1,error:"请填写完整的配置信息"};const r=await Ne.validateProviderConfig(s.provider,{apiKey:s.apiKey,baseUrl:s.baseUrl,modelName:s.modelName});return{isValid:r.isValid,error:r.error,modelList:r.modelList}}catch(r){return{isValid:!1,error:r instanceof Error?r.message:"验证失败"}}},getModelList:async(s,r,a)=>{try{return await Ne.getProviderModels(s,{apiKey:r,baseUrl:a,modelName:""})}catch(i){return console.error("获取模型列表失败:",i),[]}},getCachedModels:s=>{const r=lt(s),a=t().cachedModelData[r];return a?a.models:null},setCachedModels:(s,r)=>{const a=lt(s),i={models:r,timestamp:Date.now(),provider:s.provider,apiKey:s.apiKey,baseUrl:s.baseUrl};n(l=>({cachedModelData:{...l.cachedModelData,[a]:i}}))},getValidationState:s=>{const r=Te(s);return t().validationStates[r]||null},setValidationState:(s,r)=>{const a=Te(s),i={...r,configHash:a};n(l=>({validationStates:{...l.validationStates,[a]:i}}))},clearValidationState:s=>{const r=Te(s);n(a=>{const i={...a.validationStates};return delete i[r],{validationStates:i}})},isConfigurationChanged:s=>{const r=Te(s),a=t().validationStates[r];return!a||a.configHash!==r}}),{name:"ai-model-store",version:2,migrate:(n,t)=>{if(t<2){const s=n.models?.filter(r=>r.provider==="openai"||r.provider==="gemini")||[];return{...n,models:s,cachedModelData:{},validationStates:{},activeModelId:s.length>0?s[0].id:null}}return n}})),vt=({isOpen:n,onClose:t})=>{const{models:s,activeModelId:r,addModel:a,updateModel:i,deleteModel:l,setActiveModel:o,validateModel:x,getModelList:c,getCachedModels:d,setCachedModels:p,getValidationState:N,setValidationState:w,clearValidationState:f,isConfigurationChanged:m}=je(),[y,g]=b.useState(!1),[u,P]=b.useState(null),[j,C]=b.useState({name:"",provider:"openai",apiKey:"",baseUrl:"",modelName:""}),[E,R]=b.useState([]),[L,D]=b.useState(!1),[B,z]=b.useState(!1),[T,Q]=b.useState(!1),[h,S]=b.useState(""),[$,K]=b.useState(!1),[J,ee]=b.useState(""),[re,ne]=b.useState("add"),[le,A]=b.useState(!1),[I,O]=b.useState(null),[U,G]=b.useState(!1),de=()=>{S(""),D(!1),z(!1),Q(!1),t()};b.useEffect(()=>{if(j.apiKey&&j.baseUrl&&j.provider){const v=d({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl});if(v&&R(v),j.modelName){const M=N({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl,modelName:j.modelName});M&&M.isValidated&&S(M.validationResult)}}},[j.provider,j.apiKey,j.baseUrl,j.modelName]);const oe=()=>{C({name:"",provider:"openai",apiKey:"",baseUrl:"",modelName:""}),R([]),S(""),P(null),A(!1),K(!1),ee("")},Ae=[{name:"夏目官方模型1",provider:"openai",apiKey:"sk-fCr04jsVUMdECSXyfSBYreh6da0ePzTwVmdctxSIQnGgfSxh",baseUrl:"https://x666.me",modelName:"gemini-2.5-pro",supportsVision:!0,isActive:!1},{name:"夏目官方模型2",provider:"openai",apiKey:"sk-YnVBTQs0OLXNk2aQFElOtFPOEqQLe0kaQEbivMvkuFFKqYpn",baseUrl:"https://tbai.xin",modelName:"gemini-2.5-pro",supportsVision:!0,isActive:!1}],Ke=v=>v==="xiamu",De=()=>{ne("add"),K(!0)},k=()=>{Ke(J)?(K(!1),ee(""),re==="add"?(Ae.forEach(v=>{s.find(W=>W.name===v.name||W.apiKey===v.apiKey)||a(v)}),setTimeout(()=>{const v=s.find(M=>M.name===Ae[0].name);v&&o(v.id)},100),S("✅ 官方模型已添加到列表")):re==="view"&&(A(!0),S("✅ 密码验证成功"),setTimeout(()=>S(""),2e3))):(S("❌ 密码错误，请重试"),setTimeout(()=>{S(""),ee("")},2e3))},_=(v,M)=>{if(!M||M.trim()==="")return null;const W=M.toLowerCase();switch(v){case"openai":return W.startsWith("o")||W.startsWith("gpt")||W.includes("gemini")?!0:null;case"gemini":return W.includes("gemini")?!0:null;default:return null}},X=v=>{if(v instanceof Error){const M=v.message.toLowerCase();return M.includes("network")||M.includes("fetch")?"网络连接失败，请检查网络连接或API端点地址":M.includes("401")||M.includes("unauthorized")||M.includes("api key")?"API密钥无效，请检查密钥是否正确或是否已过期":M.includes("403")||M.includes("forbidden")?"API密钥权限不足，请检查密钥是否有访问模型列表的权限":M.includes("500")||M.includes("502")||M.includes("503")?"API服务器暂时不可用，请稍后重试":M.includes("timeout")?"请求超时，请检查网络连接或稍后重试":v.message}return"未知错误，请检查网络连接和API配置"},Z=()=>{Ae.some(M=>M.apiKey===j.apiKey)&&!le?(ne("view"),K(!0)):A(!le)},ae=v=>{P(v),C({name:v.name,provider:v.provider,apiKey:v.apiKey,baseUrl:v.baseUrl,modelName:v.modelName}),g(!0)},Ve=async(v=!0)=>{if(!j.apiKey||!j.baseUrl){S("请先填写API Key和Base URL");return}if(v)S("🔄 正在重新获取最新模型列表...");else{const M=d({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl});if(M){R(M),S(`从缓存加载 ${M.length} 个模型`);return}}z(!0);try{const M=await kt({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl});if(M.success){const W=M.models||[];R(W),p({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl},W),S(`成功获取到 ${W.length} 个模型`)}else{const W=M.error||"服务器返回了无效的响应格式",ie=X(new Error(W));S(`❌ 获取模型列表失败: ${ie}`)}}catch(M){const W=X(M);S(`❌ 获取模型列表失败: ${W}`)}finally{z(!1)}},wt=async()=>{if(!j.apiKey||!j.baseUrl||!j.modelName){S("❌ 请填写完整的配置信息");return}D(!0),S("🔄 正在验证配置...");try{if((await Nt({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl,modelName:j.modelName})).success){const M="✅ 配置验证成功！API响应正常";S(M),w({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl,modelName:j.modelName},{isValidated:!0,validationTimestamp:Date.now(),validationResult:M})}else S("❌ 验证失败: API响应异常"),f({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl,modelName:j.modelName})}catch(v){const W=`❌ 验证失败: ${X(v)}`;S(W),f({provider:j.provider,apiKey:j.apiKey,baseUrl:j.baseUrl,modelName:j.modelName})}finally{D(!1)}},Nt=async v=>{const M={"Content-Type":"application/json"};let W="",ie;v.provider==="openai"?(M.Authorization=`Bearer ${v.apiKey}`,W=`${v.baseUrl}/v1/chat/completions`,ie={model:v.modelName,messages:[{role:"user",content:"Hello"}],max_tokens:10}):(M["x-goog-api-key"]=v.apiKey,W=`${v.baseUrl}/v1beta/models/${v.modelName}:generateContent`,ie={contents:[{parts:[{text:"Hello"}]}]});const pe=await fetch(W,{method:"POST",headers:M,body:JSON.stringify(ie)});if(!pe.ok)throw new Error(`API请求失败: ${pe.status} ${pe.statusText}`);const ce=await pe.json();if(v.provider==="openai"){if(!ce.choices||!Array.isArray(ce.choices))throw new Error("OpenAI API响应格式不正确")}else if(!ce.candidates||!Array.isArray(ce.candidates))throw new Error("Gemini API响应格式不正确");return{success:!0,data:ce}},kt=async v=>{try{const M={"Content-Type":"application/json"};let W="";v.provider==="openai"?(M.Authorization=`Bearer ${v.apiKey}`,W=`${v.baseUrl}/v1/models`):(M["x-goog-api-key"]=v.apiKey,W=`${v.baseUrl}/v1beta/models`);const ie=await fetch(W,{method:"GET",headers:M});if(!ie.ok){const me=await ie.text();return{success:!1,error:`HTTP ${ie.status}: ${ie.statusText}${me?` - ${me}`:""}`,models:[]}}const pe=await ie.json();let ce=[];return v.provider==="openai"?ce=pe.data?.map(me=>{const Pe=me.features?.some(Re=>Re.toLowerCase().includes("vision")||Re.toLowerCase().includes("image")||Re.toLowerCase().includes("multimodal"))||!1;return{id:me.id,supportsVision:Pe?!0:_("openai",me.id)}})||[]:ce=pe.models?.map(me=>{const Pe=me.name||me.id;return{id:Pe?.replace("models/","")||Pe,supportsVision:!0}})||[],{success:!0,models:ce}}catch(M){return{success:!1,error:M instanceof Error?M.message:"网络请求失败",models:[]}}},Ct=async()=>{if(!j.name||!j.apiKey||!j.baseUrl||!j.modelName){S("❌ 请填写完整的配置信息");return}Q(!0),S("🔄 正在保存模型配置...");try{const v={...j,supportsVision:_(j.provider,j.modelName),isActive:!1},M=v.supportsVision;let W="✅ 模型保存成功！";M||(W+=`
⚠️ 该模型不支持图片识别功能，图片识别可能无法正常工作`),u?i(u.id,v):a(v),S(W),g(!1),oe()}catch(v){S("❌ 保存失败: "+(v instanceof Error?v.message:"未知错误"))}finally{Q(!1)}},Mt=v=>{switch(v){case"openai":return"https://api.openai.com";case"gemini":return"https://generativelanguage.googleapis.com";default:return""}};return b.useEffect(()=>{j.provider&&C(v=>({...v,baseUrl:Mt(v.provider)}))},[j.provider]),n?e.jsxs("div",{className:"fixed inset-0 z-[60] flex items-center justify-center p-4 pb-20",style:{backgroundColor:"rgba(0, 0, 0, 0.4)",backdropFilter:"blur(8px)"},onClick:de,children:[e.jsxs("div",{className:"relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md xl:max-w-4xl 2xl:max-w-5xl h-[calc(100vh-10rem)] sm:h-[calc(100vh-8rem)] xl:h-[calc(100vh-6rem)] overflow-hidden shadow-2xl border border-gray-100 flex flex-col",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)",minHeight:"400px",maxHeight:"calc(100vh - 8rem)"},onClick:v=>v.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between p-6 xl:p-8 pb-4 xl:pb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl xl:text-2xl font-bold text-gray-900 mb-1",children:"AI模型管理"}),e.jsx("p",{className:"text-sm xl:text-base text-gray-500",children:"配置和管理AI模型"})]}),e.jsx("button",{onClick:de,className:"btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500",tabIndex:0,children:e.jsx("svg",{className:"w-4 h-4 xl:w-5 xl:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4 xl:p-6 pt-2 space-y-4 xl:space-y-6 pb-16 xl:pb-20 min-h-0",children:y?e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:u?"编辑模型":"添加新模型"}),e.jsx("button",{onClick:()=>{g(!1),oe()},className:"btn btn-outline btn-sm rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200",children:"返回列表"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["服务商 ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:j.name,onChange:v=>C(M=>({...M,name:v.target.value})),onFocus:()=>O("name"),onBlur:()=>O(null),className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"输入名称"}),j.name&&I==="name"&&e.jsx("button",{type:"button",onMouseDown:v=>{v.preventDefault(),C(M=>({...M,name:""}))},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",title:"清空",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["服务商类型 ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"dropdown dropdown-bottom w-full",children:[e.jsxs("div",{tabIndex:0,role:"button",className:"btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500",children:[e.jsx("span",{children:j.provider==="openai"?"OpenAI":j.provider==="gemini"?"Google Gemini":"请选择提供商"}),e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),e.jsxs("ul",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200",children:[e.jsx("li",{children:e.jsxs("a",{onClick:v=>{C(M=>({...M,provider:"openai"})),v.target.closest(".dropdown")?.removeAttribute("open"),document.activeElement?.blur()},className:`flex items-center justify-between ${j.provider==="openai"?"bg-emerald-50 text-emerald-700":""}`,children:[e.jsx("span",{children:"OpenAI"}),j.provider==="openai"&&e.jsx("svg",{className:"w-4 h-4 text-emerald-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]})}),e.jsx("li",{children:e.jsxs("a",{onClick:v=>{C(M=>({...M,provider:"gemini"})),v.target.closest(".dropdown")?.removeAttribute("open"),document.activeElement?.blur()},className:`flex items-center justify-between ${j.provider==="gemini"?"bg-emerald-50 text-emerald-700":""}`,children:[e.jsx("span",{children:"Google Gemini"}),j.provider==="gemini"&&e.jsx("svg",{className:"w-4 h-4 text-emerald-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]})})]})]})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["API Key ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:le?"text":"password",value:j.apiKey,onChange:v=>C(M=>({...M,apiKey:v.target.value})),onFocus:()=>O("apiKey"),onBlur:()=>O(null),className:"w-full px-3 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"输入API密钥"}),e.jsxs("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 gap-1",children:[j.apiKey&&I==="apiKey"&&e.jsx("button",{type:"button",onMouseDown:v=>{v.preventDefault(),C(M=>({...M,apiKey:""}))},className:"text-gray-400 hover:text-gray-600 p-1",title:"清空",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsx("button",{type:"button",onClick:()=>Z(),className:"text-gray-400 hover:text-gray-600 p-1",children:le?e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):e.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Base URL ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"url",value:j.baseUrl,onChange:v=>C(M=>({...M,baseUrl:v.target.value})),onFocus:()=>O("baseUrl"),onBlur:()=>O(null),className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"API服务地址"}),j.baseUrl&&I==="baseUrl"&&e.jsx("button",{type:"button",onMouseDown:v=>{v.preventDefault(),C(M=>({...M,baseUrl:""}))},className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",title:"清空",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:'💡 提示：URL以"#"结尾时将直接使用您的地址'})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["模型名称 ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("button",{type:"button",onClick:()=>G(!U),className:"text-gray-400 hover:text-gray-600 p-1",title:U?"切换到下拉选择":"切换到手动输入",children:U?e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})}):e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})})})]}),e.jsx("button",{onClick:()=>Ve(!0),disabled:B,className:"text-sm text-emerald-600 hover:text-emerald-700 disabled:opacity-50",children:B?"获取中...":"手动获取模型"})]}),e.jsx("div",{className:"relative",children:U?e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:j.modelName,onChange:v=>C(M=>({...M,modelName:v.target.value})),onFocus:()=>O("modelName"),onBlur:()=>O(null),className:"w-full px-3 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500",placeholder:"输入模型名称，如：gpt-4、gemini-pro"}),e.jsxs("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 gap-1",children:[j.modelName&&e.jsx("span",{className:"flex items-center",children:_(j.provider,j.modelName)===!0?e.jsx("div",{title:"支持视觉",children:e.jsxs("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 616 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}):e.jsx("span",{className:"text-yellow-500",title:"无法判断",children:"❓"})}),j.modelName&&I==="modelName"&&e.jsx("button",{type:"button",onMouseDown:v=>{v.preventDefault(),C(M=>({...M,modelName:""}))},className:"text-gray-400 hover:text-gray-600 p-1",title:"清空",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}):E.length>0?e.jsxs("div",{className:"dropdown dropdown-bottom w-full",children:[e.jsxs("div",{tabIndex:0,role:"button",className:"btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500",children:[e.jsxs("span",{className:"flex items-center gap-2",children:[j.modelName||"请选择模型",j.modelName&&E.find(v=>v.id===j.modelName)&&e.jsxs("span",{className:"flex items-center",children:[E.find(v=>v.id===j.modelName)?.supportsVision===!0&&e.jsxs("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),E.find(v=>v.id===j.modelName)?.supportsVision!==!0&&e.jsx("span",{className:"text-yellow-500",children:"❓"})]})]}),e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),e.jsx("ul",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200 max-h-60 overflow-y-auto",children:E.length>0?E.map(v=>e.jsx("li",{children:e.jsxs("a",{onClick:M=>{C(W=>({...W,modelName:v.id})),M.target.closest(".dropdown")?.removeAttribute("open"),document.activeElement?.blur()},className:`flex items-center justify-between ${j.modelName===v.id?"bg-emerald-50 text-emerald-700":""}`,children:[e.jsxs("span",{className:"flex items-center gap-2",children:[v.id,j.modelName===v.id&&e.jsx("svg",{className:"w-4 h-4 text-emerald-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]}),e.jsxs("span",{className:"flex items-center gap-1",children:[v.supportsVision===!0&&e.jsx("div",{title:"支持视觉",children:e.jsxs("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 616 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),v.supportsVision!==!0&&e.jsx("span",{className:"text-yellow-500",title:"无法判断",children:"❓"})]})]})},v.id)):e.jsx("li",{children:e.jsx("span",{className:"text-gray-500",children:"暂无模型，请手动获取"})})})]}):e.jsxs("div",{className:"dropdown dropdown-bottom w-full",children:[e.jsxs("div",{tabIndex:0,role:"button",className:"btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500",children:[e.jsx("span",{children:j.modelName||"暂无模型，请手动获取"}),e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),e.jsx("ul",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200",children:e.jsx("li",{children:e.jsx("span",{className:"text-gray-500",children:"暂无模型，请手动获取"})})})]})}),E.length>0&&e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["已获取 ",E.length," 个可用模型，可直接选择"]})]}),h&&e.jsx("div",{className:`p-3 rounded-lg text-sm ${h.includes("✅")?"bg-green-50 text-green-800":h.includes("❌")?"bg-red-50 text-red-800":"bg-blue-50 text-blue-800"}`,children:e.jsx("div",{className:"whitespace-pre-line",children:h})}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100",children:[e.jsx("button",{onClick:wt,disabled:L,className:"btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50",children:L?"验证中...":"验证配置"}),e.jsx("button",{onClick:Ct,disabled:T||L,className:"btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50",children:T?"保存中...":"保存模型"})]})]})]}):e.jsxs("div",{children:[s.length>0&&e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"模型列表"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>De(),className:"btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]",children:"官方模型"}),e.jsx("button",{onClick:()=>g(!0),className:"btn btn-success text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]",children:"+ 自定义模型"})]})]}),s.length===0?e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"text-6xl mb-6",children:"🤖"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"开始配置AI模型"}),e.jsx("p",{className:"text-gray-500 mb-6 max-w-md mx-auto",children:"配置AI模型后，您就可以使用智能食物识别和营养分析功能了。 支持文本识别和图片识别两种方式。"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-6",children:[e.jsxs("button",{onClick:()=>De(),className:"btn btn-primary text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] px-8",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),"使用官方模型"]}),e.jsxs("button",{onClick:()=>g(!0),className:"btn btn-outline rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px] px-8",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"自定义模型"]})]}),e.jsx("p",{className:"text-sm text-gray-400",children:"推荐使用官方模型，配置简单且稳定可靠"})]}):e.jsx("div",{className:"space-y-3",children:s.map(v=>e.jsx("div",{className:`border rounded-xl p-3 ${v.id===r?"border-emerald-500 bg-emerald-50":"border-gray-200"}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("h4",{className:"font-semibold text-gray-900",children:v.name}),e.jsxs("div",{className:"flex items-center gap-1",children:[v.id===r&&e.jsx("span",{className:"bg-emerald-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center",title:"当前使用",children:e.jsx("svg",{className:"w-2.5 h-2.5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),v.supportsVision&&e.jsx("span",{className:"bg-blue-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center",title:"支持图像识别",children:e.jsxs("svg",{className:"w-2.5 h-2.5",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e.jsx("path",{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"})]})})]})]}),e.jsx("div",{className:"text-sm text-gray-600",children:e.jsxs("p",{children:["模型: ",v.modelName]})})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[v.id!==r&&e.jsx("button",{onClick:()=>o(v.id),className:"btn btn-success btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0",title:"设为当前模型",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("button",{onClick:()=>ae(v),className:"btn btn-primary btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0",title:"编辑模型",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),e.jsx("button",{onClick:()=>l(v.id),className:"btn btn-error btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0",title:"删除模型",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},v.id))})]})})]}),$&&e.jsx("div",{className:"fixed inset-0 z-[70] flex items-center justify-center p-4",style:{backgroundColor:"rgba(0, 0, 0, 0.4)",backdropFilter:"blur(8px)"},onClick:v=>{v.stopPropagation(),K(!1),ee("")},children:e.jsxs("div",{className:"relative bg-white rounded-2xl w-full max-w-sm shadow-2xl border border-gray-100 flex flex-col",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"},onClick:v=>v.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between p-6 pb-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:re==="add"?"官方模型验证":"API密钥查看验证"}),e.jsx("p",{className:"text-sm text-gray-500",children:re==="add"?"请输入密码以使用官方模型配置":"请输入密码以查看API密钥"})]}),e.jsx("button",{onClick:()=>{K(!1),ee("")},className:"btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500",tabIndex:0,children:e.jsx("svg",{className:"w-4 h-4 xl:w-5 xl:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"flex-1 p-6 pt-2 pb-20",children:e.jsx("input",{type:"password",placeholder:"请输入密码",value:J,onChange:v=>ee(v.target.value),className:"input input-bordered w-full rounded-xl",onKeyDown:v=>v.key==="Enter"&&k(),autoFocus:!0})}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100",children:[e.jsx("button",{onClick:k,className:"btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]",children:"确认"}),e.jsx("button",{onClick:()=>{K(!1),ee("")},className:"btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]",children:"取消"})]})]})})]}):null},Rs=({onSubmit:n,loading:t=!1,isEditMode:s=!1,initialData:r=null})=>{const[a,i]=b.useState(1),[l,o]=b.useState(!1),{models:x}=je(),[c,d]=b.useState(()=>s&&r?{name:r.name,age:r.age,gender:r.gender,height:r.height,weight:r.weight,targetWeight:r.targetWeight,targetDays:r.targetDays,activityLevel:r.activityLevel}:{}),[p,N]=b.useState({}),[w,f]=b.useState({}),m=b.useRef(null),y=b.useRef({});b.useEffect(()=>{H(".step-indicator",{opacity:[0,1],translateY:[-20,0],duration:600,ease:"outQuad"}),H(".form-container",{opacity:[0,1],translateY:[30,0],duration:800,delay:200,ease:"outQuad"}),H(".navigation-buttons",{opacity:[0,1],translateY:[20,0],duration:600,delay:400,ease:"outQuad"})},[]);const g=(h,S,$)=>{const K=$==="forward"?-30:30,J=$==="forward"?30:-30;H(h,{opacity:[1,0],translateX:[0,K],duration:300,ease:"outQuad"}),setTimeout(()=>{H(S,{opacity:[0,1],translateX:[J,0],duration:400,ease:"outQuad"})},150)},u=h=>{H(h,{translateX:[-10,10,-10,10,0],duration:400,ease:"outQuad"})},P=h=>{H(h,{scale:[1,.95,1],duration:200,ease:"outQuad"})},j=h=>{const S=y.current[a],$=y.current[h];if(S&&$){const K=h>a?"forward":"backward";g(`[data-step="${a}"]`,`[data-step="${h}"]`,K)}},C=(h,S)=>{d($=>({...$,[h]:S})),p[h]&&N($=>({...$,[h]:""})),w[h]&&f($=>{const K={...$};return delete K[h],K})},E=()=>{const h={};if(a===1&&((!c.height||c.height<100||c.height>250)&&(h.height="身高需在100-250cm之间"),(!c.weight||c.weight<20||c.weight>500)&&(h.weight="体重需在20-500kg之间"),(!c.age||c.age<10||c.age>100)&&(h.age="年龄需在10-100岁之间"),c.gender||(h.gender="请选择性别")),a===2&&((!c.targetWeight||c.targetWeight<20||c.targetWeight>500)&&(h.targetWeight="目标体重需在20-500kg之间"),(!c.targetDays||c.targetDays<7||c.targetDays>365)&&(h.targetDays="目标天数需在7-365天之间"),c.activityLevel||(h.activityLevel="请选择活动水平"),c.weight&&c.targetWeight&&c.targetDays)){const S=hs(c.weight,c.targetWeight,c.targetDays,c.height,c.activityLevel);S.isWarning&&S.message?f({targetWeight:S.message}):f({})}return a===3&&x.length===0&&(h.aiModel="请至少配置一个AI模型以使用智能识别功能"),N(h),Object.keys(h).length>0&&setTimeout(()=>{Object.keys(h).forEach(S=>{const $=document.querySelector(`input[value="${c[S]||""}"]`);$&&u($)})},100),Object.keys(h).length===0},R=()=>{if(E()){const h=a+1;j(h),setTimeout(()=>i(h),200)}else Object.keys(p).forEach(h=>{const S=document.querySelector(`[name="${h}"]`);S&&u(S)})},L=()=>{const h=a-1;j(h),setTimeout(()=>i(h),200)},D=async()=>{E()&&(H(".submit-button",{scale:[1,1.05,1],backgroundColor:["#6366f1","#10b981","#6366f1"],duration:600,ease:"outQuad"}),await n(c))},B=h=>{P(h.currentTarget)},z=h=>{H(h.currentTarget,{scale:[1,1.02,1],duration:300,ease:"outQuad"})},T=()=>{if(!c.height||!c.weight||!c.targetDays||!c.activityLevel)return;const h=c.height/100,S=c.weight/(h*h),$=18.5*h*h,K=24.9*h*h,ee={sedentary:.8,light:1,moderate:1.2,active:1.4,veryActive:1.6}[c.activityLevel]||1,re=c.targetDays/7,le=.5*ee,A=re*le;let I;if(S>24.9){const U=c.weight-K,G=Math.min(A,U);I=c.weight-Math.max(G,2)}else if(S<18.5)I=$;else{const U=Math.min(A*.7,5);I=Math.max(c.weight-U,$)}I=Math.max(I,$),I=Math.min(I,c.weight);const O=Math.round(I*10)/10;C("targetWeight",O),setTimeout(()=>{const U=document.querySelector('input[placeholder="60"]');U&&H(U,{scale:[1,1.1,1],backgroundColor:["#ffffff","#f0fdf4","#ffffff"],duration:800,ease:"outQuad"})},100)},Q=[{value:"sedentary",label:"久坐不动",desc:"很少或不运动",emoji:"🪑"},{value:"light",label:"轻度活动",desc:"每周轻度运动1-3次",emoji:"🚶"},{value:"moderate",label:"中度活动",desc:"每周中度运动3-5次",emoji:"🏃"},{value:"active",label:"高度活动",desc:"每周高强度运动6-7次",emoji:"🏋️"},{value:"veryActive",label:"极高活动",desc:"每天高强度运动或体力劳动",emoji:"⚡"}];return e.jsxs("div",{className:"min-h-screen bg-gradient-to-b from-indigo-50 via-white to-purple-50",ref:m,children:[e.jsxs("div",{className:"px-4 py-6 lg:px-8 xl:px-12 2xl:px-16",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-3",children:s?"编辑个人档案":"设置个人档案"}),e.jsx("p",{className:"text-base text-slate-600",children:s?"更新您的个人信息和健康目标":"为您制定个性化的健康计划"})]}),e.jsx("div",{className:"mb-8 sm:mb-12 step-indicator",children:e.jsxs("ul",{className:"steps steps-horizontal w-full text-sm sm:text-base",children:[e.jsxs("li",{className:`step ${a>=1?"step-primary":""}`,"data-content":a>1?"✓":"1",children:[e.jsx("span",{className:"hidden sm:inline",children:"基本信息"}),e.jsx("span",{className:"sm:hidden",children:"基本"})]}),e.jsxs("li",{className:`step ${a>=2?"step-primary":""}`,"data-content":a>2?"✓":"2",children:[e.jsx("span",{className:"hidden sm:inline",children:"目标设置"}),e.jsx("span",{className:"sm:hidden",children:"目标"})]}),e.jsxs("li",{className:`step ${a>=3?"step-primary":""}`,"data-content":a>3?"✓":"3",children:[e.jsx("span",{className:"hidden sm:inline",children:"AI配置"}),e.jsx("span",{className:"sm:hidden",children:"AI"})]}),e.jsxs("li",{className:`step ${a>=4?"step-primary":""}`,"data-content":a>4?"✓":"4",children:[e.jsx("span",{className:"hidden sm:inline",children:"确认信息"}),e.jsx("span",{className:"sm:hidden",children:"确认"})]})]})}),e.jsx("div",{className:"bg-white rounded-2xl shadow-lg border border-slate-200 max-w-none lg:max-w-4xl xl:max-w-5xl 2xl:max-w-6xl mx-auto",children:e.jsxs("div",{className:"p-6 lg:p-8 xl:p-10",children:[a===1&&e.jsxs("div",{className:"space-y-6 step-container","data-step":"1",ref:h=>{y.current[1]=h},children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"text-5xl mb-4",children:"👋"}),e.jsx("h2",{className:"text-xl font-bold mb-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"告诉我们关于你的信息"}),e.jsx("p",{className:"text-sm text-slate-600",children:"我们需要一些基本信息来为你定制个性化的健康计划"})]}),e.jsxs("div",{className:"space-y-6 lg:space-y-8",children:[e.jsxs("div",{className:"form-control w-full",children:[e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text font-medium text-slate-700",children:"姓名（可选）"})}),e.jsx("input",{type:"text",placeholder:"请输入您的姓名",className:"input input-bordered h-12 text-base bg-white w-full",value:c.name||"",onChange:h=>C("name",h.target.value),onFocus:z})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6",children:[e.jsxs("div",{className:"form-control",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-base sm:text-lg font-medium",children:["身高 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsxs("div",{className:"join",children:[e.jsx("input",{type:"number",placeholder:"170",className:`input input-bordered input-lg join-item flex-1 text-lg ${p.height?"input-error":""}`,value:c.height||"",onChange:h=>C("height",Number(h.target.value)),onFocus:z}),e.jsx("div",{className:"btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500",children:"cm"})]}),p.height&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error text-xs sm:text-sm",children:p.height})})]}),e.jsxs("div",{className:"form-control",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-base sm:text-lg font-medium",children:["体重 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsxs("div",{className:"join",children:[e.jsx("input",{type:"number",placeholder:"65",className:`input input-bordered input-lg join-item flex-1 text-lg ${p.weight?"input-error":""}`,value:c.weight||"",onChange:h=>C("weight",Number(h.target.value)),onFocus:z}),e.jsx("div",{className:"btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500",children:"kg"})]}),p.weight&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error text-xs sm:text-sm",children:p.weight})})]}),e.jsxs("div",{className:"form-control",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-base sm:text-lg font-medium",children:["年龄 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsxs("div",{className:"join",children:[e.jsx("input",{type:"number",placeholder:"25",className:`input input-bordered input-lg join-item flex-1 text-lg ${p.age?"input-error":""}`,value:c.age||"",onChange:h=>C("age",Number(h.target.value)),onFocus:z}),e.jsx("div",{className:"btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500",children:"岁"})]}),p.age&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error text-xs sm:text-sm",children:p.age})})]})]}),e.jsxs("div",{className:"form-control",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-base sm:text-lg font-medium",children:["性别 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsx("div",{className:"grid grid-cols-2 gap-3 sm:gap-4",children:["male","female"].map(h=>e.jsx("button",{type:"button",onClick:S=>{B(S),C("gender",h)},className:`btn btn-lg h-20 sm:h-24 min-h-20 sm:min-h-24 ${c.gender===h?"btn-primary":"btn-outline"}`,children:e.jsxs("div",{className:"flex flex-col items-center space-y-1 sm:space-y-2",children:[e.jsx("div",{className:"text-3xl sm:text-4xl",children:h==="male"?"👨":"👩"}),e.jsx("span",{className:"text-base sm:text-lg font-bold",children:h==="male"?"男性":"女性"})]})},h))}),p.gender&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error",children:p.gender})})]})]})]}),a===2&&e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-8xl mb-6",children:"🎯"}),e.jsx("h2",{className:"text-3xl font-bold mb-2",children:"设定你的目标"}),e.jsx("p",{className:"text-base-content/60",children:"告诉我们你想要达到什么目标"})]}),e.jsxs("div",{className:"max-w-none lg:max-w-4xl mx-auto space-y-6 lg:space-y-8",children:[e.jsxs("div",{className:"form-control w-full",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-lg font-medium",children:["目标天数 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsxs("div",{className:"join w-full",children:[e.jsx("input",{type:"number",placeholder:"90",className:`input input-bordered input-lg join-item flex-1 text-lg ${p.targetDays?"input-error":""}`,value:c.targetDays||"",onChange:h=>C("targetDays",Number(h.target.value))}),e.jsx("div",{className:"btn btn-lg join-item no-animation bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500",children:"天"})]}),p.targetDays&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error",children:p.targetDays})})]}),e.jsxs("div",{className:"form-control w-full",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-lg font-medium",children:["活动水平 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsx("div",{className:"space-y-3 w-full",children:Q.map(h=>e.jsx("button",{type:"button",onClick:()=>C("activityLevel",h.value),className:`btn btn-lg w-full justify-start h-auto p-6 ${c.activityLevel===h.value?"btn-primary":"btn-outline"}`,children:e.jsxs("div",{className:"flex items-center space-x-4 w-full",children:[e.jsx("span",{className:"text-3xl",children:h.emoji}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"text-lg font-bold",children:h.label}),e.jsx("div",{className:"text-sm opacity-70",children:h.desc})]})]})},h.value))}),p.activityLevel&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error",children:p.activityLevel})})]}),e.jsxs("div",{className:"form-control w-full",children:[e.jsx("label",{className:"label",children:e.jsxs("span",{className:"label-text text-lg font-medium",children:["目标体重 ",e.jsx("span",{className:"text-error",children:"*"})]})}),e.jsxs("div",{className:"join w-full",children:[e.jsx("input",{type:"number",placeholder:"60",className:`input input-bordered input-lg join-item flex-1 text-lg ${p.targetWeight?"input-error":""}`,value:c.targetWeight||"",onChange:h=>C("targetWeight",Number(h.target.value))}),e.jsx("button",{type:"button",onClick:T,disabled:!c.targetDays||!c.height||!c.weight||!c.activityLevel,className:"btn btn-lg join-item bg-gradient-to-r from-blue-500 to-indigo-600 text-white border-blue-500 hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 whitespace-nowrap",children:e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"kg"}),e.jsx("span",{className:"text-xs opacity-75",children:"|"}),e.jsx("span",{children:"推荐"})]})})]}),p.targetWeight&&e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text-alt text-error",children:p.targetWeight})})]})]})]}),a===3&&e.jsxs("div",{className:"space-y-6 step-container","data-step":"3",ref:h=>{y.current[3]=h},children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"text-5xl mb-4",children:"🤖"}),e.jsx("h2",{className:"text-xl font-bold mb-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"配置AI智能识别"}),e.jsx("p",{className:"text-sm text-slate-600",children:"配置AI模型后，您就可以使用智能食物识别和营养分析功能"})]}),e.jsx("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-6 border border-blue-100",children:x.length>0?e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-green-600 text-4xl mb-3",children:"✅"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AI模型配置完成"}),e.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:["已配置 ",x.length," 个AI模型，您可以使用智能食物识别功能了"]}),e.jsx("button",{onClick:()=>o(!0),className:"btn btn-outline btn-sm rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200",children:"管理AI模型"})]}):e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-gray-400 text-4xl mb-3",children:"⚠️"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"需要配置AI模型"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"配置AI模型后，您就可以使用文本识别和图片识别功能，让记录食物变得更简单"}),e.jsxs("button",{onClick:()=>o(!0),className:"btn btn-primary text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"配置AI模型"]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📝"}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-1",children:"文本识别"}),e.jsx("p",{className:"text-sm text-gray-600",children:"输入食物描述，AI自动识别营养信息"})]}),e.jsxs("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:[e.jsx("div",{className:"text-2xl mb-2",children:"📷"}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-1",children:"图片识别"}),e.jsx("p",{className:"text-sm text-gray-600",children:"拍照上传食物图片，AI自动分析营养"})]})]}),p.aiModel&&e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-red-500 text-xl mr-3",children:"⚠️"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-red-800",children:"需要配置AI模型"}),e.jsx("p",{className:"text-sm text-red-600",children:p.aiModel})]})]})})]}),a===4&&e.jsxs("div",{className:"space-y-6 step-container","data-step":"4",ref:h=>{y.current[4]=h},children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("div",{className:"text-5xl mb-4",children:"✨"}),e.jsx("h2",{className:"text-xl font-bold mb-3 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"确认你的信息"}),e.jsx("p",{className:"text-sm text-slate-600",children:"请确认以下信息无误"})]}),e.jsxs("div",{className:"space-y-4 lg:space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-slate-200 p-5 lg:p-6",children:[e.jsxs("h3",{className:"text-lg font-bold mb-4 text-indigo-700 flex items-center",children:[e.jsx("span",{className:"text-xl mr-2",children:"👤"}),"基本信息"]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[e.jsxs("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"姓名"}),e.jsx("div",{className:"font-semibold text-slate-800",children:c.name||"未设置"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"性别"}),e.jsx("div",{className:"font-semibold text-slate-800",children:c.gender==="male"?"👨 男性":"👩 女性"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"年龄"}),e.jsxs("div",{className:"font-semibold text-slate-800",children:[c.age," 岁"]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"身高"}),e.jsxs("div",{className:"font-semibold text-slate-800",children:[c.height," cm"]})]})]}),e.jsx("div",{className:"mt-4 lg:mt-6",children:e.jsxs("div",{className:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-3 lg:p-4 max-w-xs",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"当前体重"}),e.jsx("div",{className:"font-semibold text-slate-800 text-lg",children:c.weight?`${c.weight%1===0?c.weight:parseFloat(c.weight.toFixed(1))} kg`:"0 kg"})]})})]}),e.jsxs("div",{className:"bg-white rounded-2xl shadow-lg border border-slate-200 p-5",children:[e.jsxs("h3",{className:"text-lg font-bold mb-4 text-purple-700 flex items-center",children:[e.jsx("span",{className:"text-xl mr-2",children:"🎯"}),"目标设置"]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-3",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"目标体重"}),e.jsx("div",{className:"font-semibold text-slate-800 text-lg",children:c.targetWeight?`${c.targetWeight%1===0?c.targetWeight:parseFloat(c.targetWeight.toFixed(1))} kg`:"0 kg"})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-3",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-1",children:"目标天数"}),e.jsxs("div",{className:"font-semibold text-slate-800 text-lg",children:[c.targetDays," 天"]})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-4",children:[e.jsx("div",{className:"text-xs text-slate-600 mb-2",children:"活动水平"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl",children:Q.find(h=>h.value===c.activityLevel)?.emoji}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold text-slate-800",children:Q.find(h=>h.value===c.activityLevel)?.label}),e.jsx("div",{className:"text-xs text-slate-600",children:Q.find(h=>h.value===c.activityLevel)?.desc})]})]})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-emerald-50 to-cyan-50 rounded-2xl border border-emerald-200 p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-bold text-emerald-800 mb-1",children:"准备就绪！"}),e.jsx("p",{className:"text-sm text-emerald-700",children:"点击完成按钮，系统将为您计算个性化的营养计划。"})]})]})})]})]})]})}),e.jsxs("div",{className:"flex justify-between items-center mt-8 navigation-buttons gap-4",children:[e.jsx("div",{className:"flex-1",children:a>1&&e.jsx("button",{onClick:h=>{B(h),L()},className:"btn btn-outline btn-lg w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap",disabled:t,children:e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("svg",{className:"w-5 h-5 transition-transform group-hover:-translate-x-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),e.jsx("span",{className:"font-medium",children:"上一步"})]})})}),e.jsx("div",{className:"flex-1 flex justify-end",children:a<4?e.jsx("button",{onClick:h=>{B(h),R()},className:"btn btn-primary btn-lg w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap",disabled:t,children:e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("span",{className:"font-medium",children:"下一步"}),e.jsx("svg",{className:"w-5 h-5 transition-transform group-hover:translate-x-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})}):e.jsx("button",{onClick:h=>{B(h),D()},className:"btn btn-primary btn-lg submit-button w-full sm:w-auto h-14 min-h-14 px-6 sm:px-8 group whitespace-nowrap",disabled:t,children:t?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("span",{className:"loading loading-spinner loading-sm flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s?"更新中...":"设置中..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("span",{className:"text-xl flex-shrink-0",children:s?"💾":"🎉"}),e.jsx("span",{className:"font-medium",children:s?"保存更新":"完成设置"})]})})})]})]}),e.jsx(vt,{isOpen:l,onClose:()=>o(!1)})]})},qe=se.forwardRef(({className:n,variant:t="primary",size:s="md",loading:r=!1,fullWidth:a=!1,disabled:i,children:l,...o},x)=>{const{createAccessibleAnimation:c}=ft(),d=bt({scale:[1,1.02],duration:200,easing:"easeOutQuart"},{scale:[1.02,1],duration:200,easing:"easeOutQuart"}),p=["inline-flex items-center justify-center rounded-lg font-medium","transition-all duration-200 ease-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","touch-manipulation","transform-gpu","hover:scale-[1.02] active:scale-[0.98]","select-none"],N={primary:["text-white shadow-sm font-medium","focus:ring-2 focus:ring-green-300 focus:ring-offset-2"],secondary:["bg-gray-100 text-gray-900 border border-gray-300","hover:bg-gray-200 hover:border-gray-400 focus:ring-gray-500","active:bg-gray-300"],danger:["text-white shadow-sm","focus:ring-2 focus:ring-red-300 focus:ring-offset-2"],ghost:["text-gray-700 bg-transparent","hover:bg-gray-100 hover:text-gray-900 focus:ring-gray-500","active:bg-gray-200"]},w={sm:"px-4 py-2.5 text-sm min-h-[40px]",md:"px-6 py-3 text-base min-h-[44px]",lg:"px-8 py-4 text-lg min-h-[48px]"},f=V(p,N[t],w[s],a&&"w-full",r&&"cursor-wait",n),m=()=>{const g={};return t==="primary"?(g.backgroundColor="#16a34a",g.color="#ffffff"):t==="danger"&&(g.backgroundColor="#dc2626",g.color="#ffffff"),g},y=g=>{!i&&!r&&("vibrate"in navigator&&navigator.vibrate(10),c(g.currentTarget,{scale:[1,.95,1],duration:150,easing:"easeOutQuart"})),o.onClick?.(g)};return e.jsxs("button",{className:f,style:m(),ref:g=>{typeof x=="function"?x(g):x&&(x.current=g),d.current=g},disabled:i||r,onClick:y,onMouseEnter:g=>{t==="primary"?g.currentTarget.style.backgroundColor="#15803d":t==="danger"&&(g.currentTarget.style.backgroundColor="#b91c1c")},onMouseLeave:g=>{t==="primary"?g.currentTarget.style.backgroundColor="#16a34a":t==="danger"&&(g.currentTarget.style.backgroundColor="#dc2626")},"aria-disabled":i||r,"aria-busy":r,role:"button",tabIndex:i?-1:0,...o,children:[r&&e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e.jsx("span",{className:"relative",children:l})]})});qe.displayName="Button";const jt=se.forwardRef(({className:n,label:t,error:s,helperText:r,leftIcon:a,rightIcon:i,fullWidth:l=!1,id:o,...x},c)=>{const d=o||`input-${Math.random().toString(36).substr(2,9)}`,p=V("block w-full rounded-lg border px-4 py-3 text-base","placeholder:text-gray-400","focus:outline-none focus:ring-2 focus:ring-offset-1","disabled:cursor-not-allowed disabled:opacity-50","transition-colors duration-200","touch-manipulation","min-h-[44px]","text-[16px]",s?"border-red-300 text-red-900":"border-gray-300 text-gray-900",a&&"pl-12",i&&"pr-12",n),N=()=>s?{borderColor:"#fca5a5",color:"#7f1d1d"}:{borderColor:"#d1d5db",color:"#111827"},w=()=>({onFocus:m=>{s?(m.target.style.borderColor="#ef4444",m.target.style.boxShadow="0 0 0 2px rgba(239, 68, 68, 0.2)"):(m.target.style.borderColor="#16a34a",m.target.style.boxShadow="0 0 0 2px rgba(22, 163, 74, 0.2)")},onBlur:m=>{m.target.style.borderColor=s?"#fca5a5":"#d1d5db",m.target.style.boxShadow="none"}}),f=V("relative",l?"w-full":"w-auto");return e.jsxs("div",{className:f,children:[t&&e.jsx("label",{htmlFor:d,className:"block text-sm font-medium text-gray-700 mb-1",children:t}),e.jsxs("div",{className:"relative",children:[a&&e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx("div",{className:"h-5 w-5 text-gray-400",children:a})}),e.jsx("input",{ref:c,id:d,className:p,style:N(),...w(),...x}),i&&e.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:e.jsx("div",{className:"h-5 w-5 text-gray-400",children:i})})]}),(s||r)&&e.jsx("p",{className:V("mt-1 text-sm",s?"":"text-gray-500"),style:s?{color:"#dc2626"}:{},children:s||r})]})});jt.displayName="Input";const fe=se.forwardRef(({className:n,variant:t="default",padding:s="md",children:r,animateOnScroll:a=!1,hoverable:i=!0,...l},o)=>{const x=Is("fadeIn",{threshold:.1,once:!0}),c=bt({translateY:[0,-2],boxShadow:["0 1px 3px 0 rgba(0, 0, 0, 0.1)","0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"],duration:300,easing:"easeOutQuart"},{translateY:[-2,0],boxShadow:["0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)","0 1px 3px 0 rgba(0, 0, 0, 0.1)"],duration:300,easing:"easeOutQuart"}),d=["bg-white rounded-lg transition-all duration-300 ease-out","touch-manipulation","transform-gpu"],p={default:"border border-gray-200 shadow-sm",outlined:"border-2 border-gray-300",elevated:"shadow-lg border border-gray-100"},N={none:"",sm:"p-3",md:"p-4",lg:"p-6"},w=i?["hover:shadow-lg hover:-translate-y-0.5","cursor-pointer"]:[],f=V(d,p[t],N[s],w,n);return e.jsx("div",{ref:m=>{typeof o=="function"?o(m):o&&(o.current=m),a&&(x.current=m),i&&(c.current=m)},className:f,...l,children:r})});fe.displayName="Card";const Ce=se.forwardRef(({className:n,children:t,...s},r)=>e.jsx("div",{ref:r,className:V("border-b border-gray-200 pb-3 mb-4","transition-colors duration-200",n),...s,children:t}));Ce.displayName="CardHeader";const Me=se.forwardRef(({className:n,children:t,...s},r)=>e.jsx("h3",{ref:r,className:V("text-lg font-semibold text-gray-900","transition-colors duration-200",n),...s,children:t}));Me.displayName="CardTitle";const ve=se.forwardRef(({className:n,children:t,...s},r)=>e.jsx("div",{ref:r,className:V("text-gray-700","transition-colors duration-200",n),...s,children:t}));ve.displayName="CardContent";const Ts=se.forwardRef(({className:n,children:t,...s},r)=>e.jsx("div",{ref:r,className:V("border-t border-gray-200 pt-3 mt-4","transition-colors duration-200",n),...s,children:t}));Ts.displayName="CardFooter";const te=({value:n,unit:t,className:s="",precision:r="auto",fallback:a="0"})=>{const i=F(n,{precision:r,unit:t,fallback:a});return e.jsx("span",{className:s,"data-react-component":"number-display",children:i})},Ye=n=>e.jsx("span",{"data-react-component":"weight-display",children:e.jsx(te,{...n,unit:"kg",precision:"decimal"})}),he=n=>e.jsx("span",{"data-react-component":"calorie-display",children:e.jsx(te,{...n,unit:"kcal",precision:"integer"})}),$s=n=>e.jsx("span",{"data-react-component":"days-display",children:e.jsx(te,{...n,unit:"天",precision:"integer"})}),be=se.forwardRef(({className:n,variant:t="default",size:s="md",children:r,...a},i)=>{const l=["inline-flex items-center font-medium rounded-full","transition-colors duration-200"],o={default:"bg-gray-100 text-gray-800",primary:"bg-primary-100 text-primary-800",success:"bg-green-100 text-green-800",warning:"bg-warning-100 text-warning-800",danger:"bg-danger-100 text-danger-800"},x={sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},c=V(l,o[t],x[s],n);return e.jsx("span",{ref:i,className:c,...a,children:r})});be.displayName="Badge";const Os=se.forwardRef(({className:n,size:t="md",color:s="primary",...r},a)=>{const i={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},l={primary:"text-primary-600",white:"text-white",gray:"text-gray-600"},o=V("animate-spin",i[t],l[s],n);return e.jsx("div",{ref:a,className:o,...r,children:e.jsxs("svg",{className:"w-full h-full",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})});Os.displayName="Spinner";const Bs=se.forwardRef(({label:n,error:t,required:s=!1,description:r,className:a,...i},l)=>e.jsxs("div",{className:V("space-y-1",a),children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:[n,s&&e.jsx("span",{className:"text-danger-500 ml-1",children:"*"})]}),r&&e.jsx("p",{className:"text-sm text-gray-500",children:r}),e.jsx(jt,{ref:l,error:t,fullWidth:!0,...i})]}));Bs.displayName="FormField";const ot=({type:n="info",title:t,message:s,duration:r=5e3,isVisible:a,onClose:i,showCloseButton:l=!0})=>{b.useEffect(()=>{if(a&&r>0){const p=setTimeout(()=>{i()},r);return()=>clearTimeout(p)}},[a,r,i]);const o={success:Dt,error:Et,warning:Lt,info:It},x={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"},c={success:"text-green-400",error:"text-red-400",warning:"text-yellow-400",info:"text-blue-400"},d=o[n];return e.jsx(At,{show:a,as:b.Fragment,enter:"transform ease-out duration-300 transition",enterFrom:"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2",enterTo:"translate-y-0 opacity-100 sm:translate-x-0",leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx("div",{className:V("max-w-sm w-full shadow-lg rounded-lg pointer-events-auto border",x[n]),children:e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(d,{className:V("h-6 w-6",c[n]),"aria-hidden":"true"})}),e.jsxs("div",{className:"ml-3 w-0 flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:t}),s&&e.jsx("p",{className:"mt-1 text-sm opacity-90",children:s})]}),l&&e.jsx("div",{className:"ml-4 flex-shrink-0 flex",children:e.jsxs("button",{className:"inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",onClick:i,children:[e.jsx("span",{className:"sr-only",children:"关闭"}),e.jsx(Pt,{className:"h-5 w-5","aria-hidden":"true"})]})})]})})})})},zs=({message:n,type:t,duration:s=3e3,onClose:r})=>{const[a,i]=b.useState(!0);b.useEffect(()=>{const x=setTimeout(()=>{i(!1),setTimeout(r,300)},s);return()=>clearTimeout(x)},[s,r]);const l=()=>{const x="rounded-2xl shadow-2xl transition-all duration-500 transform backdrop-blur-sm border",c={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-600 border-red-700 text-white",info:"bg-blue-50 border-blue-200 text-blue-800",warning:"bg-amber-50 border-amber-200 text-amber-800"}[t];return`${x} ${c} ${a?"translate-y-0 opacity-100 scale-100":"-translate-y-4 opacity-0 scale-95"}`},o=()=>{switch(t){case"success":return e.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})});case"error":return e.jsx("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})});case"warning":return e.jsx("svg",{className:"w-5 h-5 text-amber-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"info":default:return e.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}};return e.jsx("div",{className:"fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] max-w-sm min-w-[280px]",children:e.jsx("div",{className:l(),children:e.jsxs("div",{className:"flex items-start gap-3 p-4",children:[e.jsx("div",{className:"flex-shrink-0 mt-0.5",children:o()}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsx("p",{className:"text-sm font-medium leading-5",children:n})}),e.jsx("button",{onClick:()=>{i(!1),setTimeout(r,500)},className:"flex-shrink-0 ml-2 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors duration-200",children:e.jsx("svg",{className:"w-4 h-4 text-gray-500",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})})})},yt=b.createContext(void 0),Ws=()=>{const n=b.useContext(yt);if(!n)throw new Error("useToast must be used within a ToastProvider");return n},Us=({children:n})=>{const[t,s]=b.useState([]),r=b.useCallback((d,p="info",N=3e3)=>{const w=Date.now().toString(),f={id:w,message:d,type:p,duration:N};s(m=>[...m,f]),setTimeout(()=>{s(m=>m.filter(y=>y.id!==w))},N+300)},[]),a=b.useCallback(d=>{s(p=>p.filter(N=>N.id!==d))},[]),i=b.useCallback((d,p)=>{r(d,"success",p)},[r]),l=b.useCallback((d,p)=>{r(d,"error",p)},[r]),o=b.useCallback((d,p)=>{r(d,"info",p)},[r]),x=b.useCallback((d,p)=>{r(d,"warning",p)},[r]),c={showToast:r,showSuccess:i,showError:l,showInfo:o,showWarning:x};return e.jsxs(yt.Provider,{value:c,children:[n,e.jsx("div",{className:"fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] space-y-2",children:t.map(d=>e.jsx(zs,{message:d.message,type:d.type,duration:d.duration,onClose:()=>a(d.id)},d.id))})]})},_s=()=>{const n=Se(),[t]=xt(),{createProfile:s,updateProfile:r,profile:a,loading:i,error:l,setError:o}=ge(),[x,c]=se.useState(!1),d=t.get("mode")==="edit",p=async N=>{try{d?(await r(N),c(!0),setTimeout(()=>{n("/profile")},2e3)):(await s(N),c(!0),setTimeout(()=>{n("/dashboard")},2e3))}catch(w){console.error(d?"更新档案失败:":"创建档案失败:",w)}};return e.jsx("div",{className:"min-h-screen bg-base-200 py-4 px-3 sm:py-8 sm:px-4",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsx(Rs,{onSubmit:p,loading:i,isEditMode:d,initialData:d?a:void 0}),l&&e.jsx("div",{className:"fixed top-4 right-4 z-50",children:e.jsx(ot,{type:"error",title:"设置失败",message:l,isVisible:!!l,onClose:()=>o(null)})}),x&&e.jsx("div",{className:"fixed top-4 right-4 z-50",children:e.jsx(ot,{type:"success",title:d?"更新成功":"设置成功",message:d?"正在返回个人页面...":"正在跳转到主页面...",isVisible:x,onClose:()=>c(!1),duration:2e3})})]})})},Fs="modulepreload",Ks=function(n){return"/"+n},dt={},ue=function(t,s,r){let a=Promise.resolve();if(s&&s.length>0){let x=function(c){return Promise.all(c.map(d=>Promise.resolve(d).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),o=l?.nonce||l?.getAttribute("nonce");a=x(s.map(c=>{if(c=Ks(c),c in dt)return;dt[c]=!0;const d=c.endsWith(".css"),p=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${p}`))return;const N=document.createElement("link");if(N.rel=d?"stylesheet":Fs,d||(N.as="script"),N.crossOrigin="",N.href=c,o&&N.setAttribute("nonce",o),document.head.appendChild(N),d)return new Promise((w,f)=>{N.addEventListener("load",w),N.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${c}`)))})}))}function i(l){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=l,window.dispatchEvent(o),!o.defaultPrevented)throw l}return a.then(l=>{for(const o of l||[])o.status==="rejected"&&i(o.reason);return t().catch(i)})},Le=Qe()(Je((n,t)=>({dailySummaries:{},dailyFoodRecords:{},currentDate:new Date,loading:!1,error:null,setCurrentDate:s=>{n({currentDate:s})},getDailySummary:s=>{const r=q(s,"yyyy-MM-dd");return t().dailySummaries[r]||null},updateDailySummary:(s,r)=>{const a=q(s,"yyyy-MM-dd"),i=t().dailySummaries,l=i[a],x={...{id:`summary_${Date.now()}`,createdAt:new Date,updatedAt:new Date,date:s,totalCalories:0,calorieLimit:2e3,remainingCalories:2e3,mealBreakdown:{breakfast:{mealType:"breakfast",calories:0,calorieLimit:600,foodCount:0,percentage:0},lunch:{mealType:"lunch",calories:0,calorieLimit:800,foodCount:0,percentage:0},dinner:{mealType:"dinner",calories:0,calorieLimit:600,foodCount:0,percentage:0},snack:{mealType:"snack",calories:0,calorieLimit:0,foodCount:0,percentage:0}},nutrition:{protein:0,fat:0,carbs:0,fiber:0,sugar:0,sodium:0},status:"under",percentage:0},...l,...r,updatedAt:new Date},c=x.totalCalories/x.calorieLimit*100;x.percentage=c,x.remainingCalories=x.calorieLimit-x.totalCalories,c<90?x.status="under":c<=110?x.status="normal":c<=130?x.status="over":x.status="exceed",n({dailySummaries:{...i,[a]:x}})},addFoodRecord:(s,r,a,i)=>{const l=Number(a)||0,o=Number(i)||2e3,c=["breakfast","lunch","dinner","snack"].includes(r)?r:"snack",d=q(s,"yyyy-MM-dd"),p=t().dailySummaries,N=p[d],w={id:`summary_${d}_${Date.now()}`,createdAt:new Date,updatedAt:new Date,date:new Date(d),totalCalories:0,calorieLimit:o,remainingCalories:o,mealBreakdown:{breakfast:{mealType:"breakfast",calories:0,calorieLimit:o*.3,foodCount:0,percentage:0},lunch:{mealType:"lunch",calories:0,calorieLimit:o*.4,foodCount:0,percentage:0},dinner:{mealType:"dinner",calories:0,calorieLimit:o*.3,foodCount:0,percentage:0},snack:{mealType:"snack",calories:0,calorieLimit:0,foodCount:0,percentage:0}},nutrition:{protein:0,fat:0,carbs:0,fiber:0,sugar:0,sodium:0},status:"under",percentage:0},f=N||w,m={...f.mealBreakdown};m[c]||(m[c]={mealType:c,calories:0,calorieLimit:c==="snack"?0:o*.3,foodCount:0,percentage:0}),m[c]={...m[c],calories:m[c].calories+l,foodCount:m[c].foodCount+1},m[c].percentage=m[c].calorieLimit>0?m[c].calories/m[c].calorieLimit*100:0;const y=f.totalCalories+a,g=y/i*100,u={...f,totalCalories:y,remainingCalories:i-y,mealBreakdown:m,percentage:g,status:g<90?"under":g<=110?"normal":"exceed",updatedAt:new Date};n({dailySummaries:{...p,[d]:u}})},calculateMealSummary:(s,r,a,i)=>{const l=i>0?a/i*100:0;return{mealType:r,calories:a,calorieLimit:i,foodCount:1,percentage:l}},getWeeklyAnalysis:s=>{const{start:r,end:a}=gs("week",s),i=t().dailySummaries,l=[];let o=0,x=0,c=0;for(let w=new Date(r);w<=a;w.setDate(w.getDate()+1)){const f=q(w,"yyyy-MM-dd"),m=i[f];m?(l.push({date:new Date(w),calories:m.totalCalories}),o+=m.totalCalories,x+=m.calorieLimit,c++):l.push({date:new Date(w),calories:0})}const d=c>0?o/c:0,p=c>0?x/c:0,N=p>0?d/p:0;return{period:"weekly",startDate:r,endDate:a,calories:{average:d,total:o,target:p,adherenceRate:N},nutrition:{protein:{average:0,total:0,recommended:0,percentage:0},fat:{average:0,total:0,recommended:0,percentage:0},carbs:{average:0,total:0,recommended:0,percentage:0},fiber:{average:0,total:0,recommended:0,percentage:0}},trends:l}},getMonthlyAnalysis:(s,r)=>{const a=new Date(s,r-1,1),i=new Date(s,r,0),l=t().dailySummaries,o=[];let x=0,c=0,d=0;for(let f=new Date(a);f<=i;f.setDate(f.getDate()+1)){const m=q(f,"yyyy-MM-dd"),y=l[m];y?(o.push({date:new Date(f),calories:y.totalCalories}),x+=y.totalCalories,c+=y.calorieLimit,d++):o.push({date:new Date(f),calories:0})}const p=d>0?x/d:0,N=d>0?c/d:0,w=N>0?p/N:0;return{period:"monthly",startDate:a,endDate:i,calories:{average:p,total:x,target:N,adherenceRate:w},nutrition:{protein:{average:0,total:0,recommended:0,percentage:0},fat:{average:0,total:0,recommended:0,percentage:0},carbs:{average:0,total:0,recommended:0,percentage:0},fiber:{average:0,total:0,recommended:0,percentage:0}},trends:o}},clearData:()=>{n({dailySummaries:{},error:null})},setLoading:s=>{n({loading:s})},setError:s=>{n({error:s})},addDetailedFoodRecord:(s,r)=>{const a=q(s,"yyyy-MM-dd"),i=t().dailyFoodRecords,l=`food_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,o=new Date,x={...r,id:l,createdAt:o,updatedAt:o},d=[...(i[a]||{records:[]}).records,x],p={breakfast:d.filter(m=>m.mealType==="breakfast"),lunch:d.filter(m=>m.mealType==="lunch"),dinner:d.filter(m=>m.mealType==="dinner"),snack:d.filter(m=>m.mealType==="snack")},N={date:a,records:d,mealRecords:p};n({dailyFoodRecords:{...i,[a]:N}});const w=Number(r.calories)||0,f=r.mealType||"snack";if(w>0&&typeof w=="number"&&!isNaN(w)){t().addFoodRecord(s,f,w,2e3);const y=r.nutrition||{protein:0,fat:0,carbs:0,fiber:0,sugar:0,sodium:0};t().updateNutritionData(s,y)}else console.warn("无效的卡路里数据，跳过营养汇总更新:",{calories:w,foodRecord:r})},getDailyFoodRecords:s=>{const r=q(s,"yyyy-MM-dd");return t().dailyFoodRecords[r]||null},getFoodRecordsByMeal:(s,r)=>t().getDailyFoodRecords(s)?.mealRecords[r]||[],updateFoodRecord:(s,r,a)=>{const i=q(s,"yyyy-MM-dd"),l=t().dailyFoodRecords,o=l[i];if(!o)return;const x=o.records.find(y=>y.id===r);if(!x)return;const c=o.records.map(y=>y.id===r?{...y,...a,updatedAt:new Date,isEdited:!0}:y),d=c.find(y=>y.id===r);if(!d)return;const p={breakfast:c.filter(y=>y.mealType==="breakfast"),lunch:c.filter(y=>y.mealType==="lunch"),dinner:c.filter(y=>y.mealType==="dinner"),snack:c.filter(y=>y.mealType==="snack")},N={date:i,records:c,mealRecords:p};n({dailyFoodRecords:{...l,[i]:N}});const w=d.calories-x.calories;w!==0&&t().addFoodRecord(s,d.mealType,w,2e3);const f={protein:d.nutrition.protein-x.nutrition.protein,fat:d.nutrition.fat-x.nutrition.fat,carbs:d.nutrition.carbs-x.nutrition.carbs,fiber:d.nutrition.fiber-x.nutrition.fiber,sugar:d.nutrition.sugar-x.nutrition.sugar,sodium:d.nutrition.sodium-x.nutrition.sodium};Object.values(f).some(y=>y!==0)&&t().updateNutritionData(s,f)},deleteFoodRecord:(s,r)=>{const a=q(s,"yyyy-MM-dd"),i=t().dailyFoodRecords,l=i[a];if(!l)return;const o=l.records.find(N=>N.id===r);if(!o)return;const x=l.records.filter(N=>N.id!==r),c={breakfast:x.filter(N=>N.mealType==="breakfast"),lunch:x.filter(N=>N.mealType==="lunch"),dinner:x.filter(N=>N.mealType==="dinner"),snack:x.filter(N=>N.mealType==="snack")},d={date:a,records:x,mealRecords:c};n({dailyFoodRecords:{...i,[a]:d}}),t().addFoodRecord(s,o.mealType,-o.calories,2e3);const p={protein:-o.nutrition.protein,fat:-o.nutrition.fat,carbs:-o.nutrition.carbs,fiber:-o.nutrition.fiber,sugar:-o.nutrition.sugar,sodium:-o.nutrition.sodium};t().updateNutritionData(s,p)},updateNutritionData:(s,r)=>{const a=q(s,"yyyy-MM-dd"),i=t().dailySummaries,l=i[a];if(!l)return;const o={protein:Math.max(0,l.nutrition.protein+r.protein),fat:Math.max(0,l.nutrition.fat+r.fat),carbs:Math.max(0,l.nutrition.carbs+r.carbs),fiber:Math.max(0,l.nutrition.fiber+r.fiber),sugar:Math.max(0,l.nutrition.sugar+r.sugar),sodium:Math.max(0,l.nutrition.sodium+r.sodium)},x={...l,nutrition:o,updatedAt:new Date};n({dailySummaries:{...i,[a]:x}})}}),{name:"nutrition-data-storage",partialize:n=>({dailySummaries:n.dailySummaries,dailyFoodRecords:n.dailyFoodRecords})})),xe=[{id:"morning",name:"上午加餐",emoji:"🥐",timeRange:"10:00-11:00",startHour:10,endHour:11,description:"早餐后补充"},{id:"afternoon",name:"下午加餐",emoji:"🍎",timeRange:"14:00-17:00",startHour:14,endHour:17,description:"午后能量"},{id:"evening",name:"晚间加餐",emoji:"🥛",timeRange:"21:00-00:00",startHour:21,endHour:24,description:"睡前小食"}];function _e(n=new Date){const t=n.getHours(),s=n.getMinutes(),r=t*60+s,a=xe.find(o=>{const x=o.startHour*60,c=o.endHour===24?24*60:o.endHour*60;return console.log(`检查加餐时间段: ${o.name} (${o.startHour}:00-${o.endHour}:00), 当前时间分钟: ${r}, 范围: ${x}-${c}`),r>=x&&r<c});let i=null;if(a){const o=xe.findIndex(x=>x.id===a.id);i=xe[(o+1)%xe.length]}else i=xe.find(o=>r<o.startHour*60)||xe[0];const l=Vs(n,i);return{currentPeriod:a,nextPeriod:i,isSnackTime:!!a,timeUntilNext:l}}function Vs(n,t){if(!t)return"";const s=new Date(n),r=new Date(s);r.setHours(t.startHour,0,0,0),r<=s&&r.setDate(r.getDate()+1);const a=r.getTime()-s.getTime(),i=Math.floor(a/(1e3*60*60)),l=Math.floor(a%(1e3*60*60)/(1e3*60));return i>0?`${i}小时${l}分钟后`:`${l}分钟后`}function ct(n,t=.12){const s=Math.round(n*t),r=Math.round(s/xe.length);return{totalSnackCalories:s,perSnackCalories:r}}function $e(n=new Date){const t=n.getHours();console.log(`当前时间判断 - 小时: ${t}, 完整时间: ${n.toLocaleTimeString()}`);const s=_e(n);if(s.isSnackTime&&s.currentPeriod)switch(console.log(`检测到加餐时间段: ${s.currentPeriod.name} (${s.currentPeriod.timeRange})`),s.currentPeriod.id){case"morning":return"morning-snack";case"afternoon":return"afternoon-snack";case"evening":return"evening-snack";default:return"morning-snack"}return t>=0&&t<6||t>=6&&t<11?(console.log("判断为早餐时间"),"breakfast"):t>=11&&t<17?(console.log("判断为午餐时间"),"lunch"):t>=17&&t<21?(console.log("判断为晚餐时间"),"dinner"):t>=21?(console.log("21:00后非加餐时间，判断为晚餐"),"dinner"):(console.log("默认判断为早餐"),"breakfast")}class ke{static instance;constructor(){}static getInstance(){return ke.instance||(ke.instance=new ke),ke.instance}initializeCurrentProvider(){const{getActiveModel:t}=je.getState(),s=t();if(!s)throw new Error("未配置活跃的AI模型，请先在设置中配置AI模型");Ne.setProvider(s.provider,{apiKey:s.apiKey,baseUrl:s.baseUrl,modelName:s.modelName})}async recognizeFood(t,s,r){this.initializeCurrentProvider();const a=Ne.getCurrentProvider();if(!a)throw new Error("AI提供商未初始化");return await a.recognizeFood(t,s,r)}async analyzeText(t){this.initializeCurrentProvider();const s=Ne.getCurrentProvider();if(!s)throw new Error("AI提供商未初始化");return await s.analyzeText(t)}async analyzeNutritionAdvice(t){this.initializeCurrentProvider();const s=Ne.getCurrentProvider();if(!s)throw new Error("AI提供商未初始化");return await s.analyzeNutritionAdvice(t)}getCurrentModelInfo(){const{getActiveModel:t}=je.getState(),s=t();return s?{provider:s.provider,modelName:s.modelName}:null}hasActiveModel(){const{getActiveModel:t}=je.getState();return t()!==null}}const Be=ke.getInstance(),Hs=()=>{const[n,t]=b.useState({isProcessing:!1,processingStep:"",error:null}),[s,r]=b.useState(null),{showSuccess:a,showError:i}=Ws(),{getActiveModel:l}=je(),o=b.useCallback(p=>{t(N=>({...N,...p}))},[]),x=b.useCallback(async(p,N,w,f,m)=>{o({isProcessing:!0,error:null,processingStep:"正在连接AI服务..."});try{const y=new AbortController;if(r(y),!Be.hasActiveModel())throw new Error("AI服务未配置，请在设置中配置AI模型");let g;if(p==="text")o({processingStep:"正在分析食物描述..."}),g=await Be.analyzeText(w);else{if(!f)throw new Error("请选择要识别的图片");if(!l())throw new Error("未选择AI模型，请先在设置中配置并选择AI模型");o({processingStep:"正在分析食物图片..."});const E=w?.trim()||void 0,R=Array.isArray(f)?f:[f];g=await Be.recognizeFood(R,E)}o({processingStep:"正在生成营养记录..."});const u=g.foods.map(C=>({name:C.name,calories:C.calories,quantity:`${Math.round(C.weight)}g`,timestamp:new Date().toISOString(),dataSource:C.dataSource||"visual_estimation",nutrition:C.nutrition||{protein:Math.round(C.calories*.15/4),fat:Math.round(C.calories*.25/9),carbs:Math.round(C.calories*.6/4),fiber:Math.round(C.calories*.05/4),sugar:Math.round(C.calories*.1/4),sodium:Math.round(C.calories*.5)},confidence:C.confidence,weight:C.weight,labelInfo:C.labelInfo}));if(u.length===0){o({isProcessing:!1,error:p==="image"?"未识别到食物内容，请上传清晰的食物图片进行识别":"未从文字中识别到食物信息，请提供更详细的食物描述",processingStep:""});return}const j={method:p,meal:N,content:p==="text"?w:Array.isArray(f)?f.length>1?`${f.length}张图片: ${f.map(C=>C.name).join(", ")}`:f[0]?.name:f?.name,timestamp:new Date().toISOString(),foods:u};m(j)}catch(y){console.error("AI识别失败:",y);let g="识别失败，请重试";y instanceof Error&&(y.message.includes("API密钥")?g="AI服务配置错误，请联系管理员":y.message.includes("超时")?g="网络超时，请检查网络连接后重试":y.message.includes("API请求失败")?g="AI服务暂时不可用，请稍后重试":g=y.message),o({error:g})}finally{o({isProcessing:!1,processingStep:""}),r(null)}},[o,a,i,l,r]),c=b.useCallback(()=>{s&&(s.abort(),r(null)),o({isProcessing:!1,processingStep:"",error:null})},[o,s]),d=b.useCallback(()=>{o({error:null})},[o]);return{state:n,startRecognition:x,stopRecognition:c,clearError:d}},qs={running:12,walking:4,swimming:10,cycling:8,jumping:15,yoga:3,basketball:9,tennis:8,dancing:6,climbing:11},Gs={running:"跑步",walking:"快走",swimming:"游泳",cycling:"骑车",jumping:"跳绳",yoga:"瑜伽",basketball:"篮球",tennis:"网球",dancing:"跳舞",climbing:"爬楼梯"};function Qs(n,t){const s=qs[t];return Math.ceil(n/s)}function Js(n){return[{type:"running",icon:"🏃‍♂️"},{type:"walking",icon:"🚶‍♂️"},{type:"swimming",icon:"🏊‍♂️"},{type:"cycling",icon:"🚴‍♂️"},{type:"jumping",icon:"🪢"},{type:"dancing",icon:"💃"}].map(({type:s,icon:r})=>({type:s,name:Gs[s],minutes:Qs(n,s),icon:r}))}function Ys(n){if(n<60)return`${n}分钟`;const t=Math.floor(n/60),s=n%60;return s===0?`${t}小时`:`${t}小时${s}分钟`}const Xs=({isOpen:n,onClose:t,method:s,onRecognitionComplete:r,currentDate:a,onProcessingStateChange:i})=>{const[l,o]=b.useState(""),[x,c]=b.useState([]),[d,p]=b.useState([]),[N,w]=b.useState($e()),[f,m]=b.useState(null),[y,g]=b.useState(""),[u,P]=b.useState(!1),[j,C]=b.useState(!1),[E,R]=b.useState(null),[L,D]=b.useState(a||new Date),B=b.useRef(null),z=b.useRef(null),{state:T,startRecognition:Q,stopRecognition:h}=Hs(),{profile:S}=ge(),$=[{value:"breakfast",label:"早餐",icon:"🌅"},{value:"lunch",label:"午餐",icon:"☀️"},{value:"dinner",label:"晚餐",icon:"🌙"},{value:"morning-snack",label:"上午加餐",icon:"🍎"},{value:"afternoon-snack",label:"下午加餐",icon:"🥨"},{value:"evening-snack",label:"晚间加餐",icon:"🥛"}],K=()=>$.find(k=>k.value===N)||$[0],J=k=>{w(k),C(!1)},ee=()=>{o(""),c([]),p([]),m(null),g(""),P(!1),C(!1),R(null),w($e()),B.current&&(B.current.value=""),z.current&&(z.current.value=""),h()};se.useEffect(()=>{n&&w($e())},[n]);const re=()=>{T.isProcessing||(ee(),i?.(!1),setTimeout(()=>{t()},0))},ne=k=>{if(x.length>=5){alert("最多只能上传5张图片");return}if(!k||!(k instanceof File)){console.error("Invalid file object:",k);return}const _=[...x,k];c(_);const X=new FileReader;X.onload=Z=>{const ae=Z.target?.result;if(typeof ae=="string"){const Ve=[...d,ae];p(Ve)}},X.onerror=Z=>{console.error("FileReader error:",Z),alert("图片读取失败，请重试")},X.readAsDataURL(k)},le=k=>{const _=x.filter((Z,ae)=>ae!==k),X=d.filter((Z,ae)=>ae!==k);c(_),p(X)},A=()=>{z.current?.click()},I=k=>{const _=k.target.files?.[0];if(!_||!(_ instanceof File)){console.error("Invalid camera file:",_);return}ne(_),k.target&&(k.target.value="")},O=()=>{B.current&&B.current.click()},U=async()=>{i?.(!0);try{const k=N||$e(),_=s==="image"&&y.trim()?`${y.trim()}`:l,X=x.length>0?x:null,Z=S?{age:S.age,gender:S.gender,weight:S.weight,height:S.height,targetWeight:S.targetWeight,activityLevel:S.activityLevel,bmr:S.bmr,tdee:S.tdee,targetDays:S.targetDays}:null;await Q(s,k,_,X,ae=>{m(ae),P(!0),i?.(!1)})}catch(k){console.error("识别失败:",k),i?.(!1)}},G=()=>{if(console.log("handleFinalSubmit 被调用",{recognitionResult:f,selectedMealType:N,selectedDate:L}),!f){console.error("recognitionResult 为空，无法提交"),alert("识别结果丢失，请重新识别");return}if(!f.foods||f.foods.length===0){console.error("识别结果中没有食物数据"),alert("没有识别到食物，请重新识别");return}try{const k={...f,meal:N,selectedDate:L,additionalContext:y.trim()||void 0};console.log("准备调用 onRecognitionComplete",k),typeof r=="function"?(i?.(!1),r(k),setTimeout(()=>{re()},100)):(console.error("onRecognitionComplete 不是一个函数"),i?.(!1),alert("提交失败，请重试"))}catch(k){console.error("提交过程中发生错误:",k),i?.(!1),alert("提交失败，请重试")}},de=()=>{m(null),P(!1)},oe=k=>k>=.8?"bg-green-100 text-green-800":k>=.6?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",Ae=k=>k>=.8?"高准确度":k>=.6?"中等准确度":"低准确度",Ke=()=>{h(),i?.(!1)},De=()=>s==="text"?l.trim().length>0:x.length>0;return n?e.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4 pb-20",style:{backgroundColor:"rgba(0, 0, 0, 0.4)",backdropFilter:"blur(8px)",willChange:"auto",transform:"translateZ(0)",...T.isProcessing&&{touchAction:"none"}},onClick:re,onTouchMove:T.isProcessing?k=>k.preventDefault():void 0,children:e.jsxs("div",{className:"relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)",backfaceVisibility:"hidden",perspective:"1000px"},onClick:k=>k.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between p-6 pb-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-1",children:s==="text"?"文本识别":"视觉识别"}),e.jsx("p",{className:"text-sm text-gray-500",children:s==="text"?"通过对话描述识别食物":"通过拍照识别食物"})]}),e.jsx("button",{onClick:re,className:"btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500",disabled:T.isProcessing,style:T.isProcessing?{opacity:.5,pointerEvents:"none"}:{},tabIndex:0,children:e.jsx("svg",{className:"w-4 h-4 xl:w-5 xl:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-16",children:[u?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"识别结果"})}),f&&e.jsxs("div",{className:"space-y-3",children:[f.foods.map((k,_)=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-gray-900",children:k.name}),k.dataSource==="nutrition_label"&&e.jsxs("div",{className:"flex items-center gap-1 mt-1",children:[e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full",children:"📝 营养标签"}),k.labelInfo?.servingSize&&e.jsxs("span",{className:"text-xs text-gray-500",children:["份量: ",k.labelInfo.servingSize]})]}),k.dataSource==="visual_estimation"&&e.jsx("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full mt-1 inline-block",children:"👁️ 视觉估算"})]}),e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${oe(k.confidence)}`,children:Ae(k.confidence)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"whitespace-nowrap",children:[e.jsx("span",{className:"font-medium",children:"卡路里："}),e.jsx(he,{value:k.calories})]}),e.jsxs("div",{className:"whitespace-nowrap",children:[e.jsx("span",{className:"font-medium",children:"重量："}),e.jsx(Ye,{value:k.weight})]})]})]},_)),(()=>{const k=f.foods.reduce((Z,ae)=>Z+ae.calories,0),_=Js(k),X=f.personalizedAdvice||`根据您的个人数据分析，这份食物含有 ${k} 卡路里。`;return e.jsxs("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx("span",{className:"text-lg",children:"🔥"}),e.jsx("h5",{className:"font-medium text-gray-900",children:"个性化运动建议"})]}),e.jsx("p",{className:"text-sm text-gray-600 mb-3",children:X}),e.jsx("div",{className:"text-sm text-gray-700 mb-2",children:e.jsxs("span",{className:"font-medium",children:["消耗 ",k," kcal 需要："]})}),e.jsx("div",{className:"grid grid-cols-2 gap-2 text-xs",children:_.slice(0,4).map(Z=>e.jsxs("div",{className:"flex items-center gap-2 bg-white rounded-lg p-2",children:[e.jsx("span",{children:Z.icon}),e.jsxs("span",{children:[Z.name," ",Ys(Z.minutes)]})]},Z.type))}),f.exerciseAdvice&&e.jsx("div",{className:"mt-3 p-2 bg-white rounded-lg",children:e.jsxs("div",{className:"text-xs text-gray-700",children:[e.jsx("span",{className:"font-medium",children:"💪 专属建议："}),f.exerciseAdvice]})}),e.jsx("div",{className:"mt-3 text-xs text-gray-500 text-center",children:"💡 基于您的个人数据提供专属建议"})]})})()]}),u&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-base font-medium text-gray-800 mb-3",children:"确认餐次"}),!j&&e.jsx("button",{onClick:()=>C(!0),className:"w-full p-3 rounded-xl border-2 border-emerald-500 bg-emerald-50 text-emerald-700 transition-all duration-200 hover:bg-emerald-100",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-lg flex-shrink-0",children:K().icon}),e.jsx("span",{className:"font-medium",children:K().label})]}),e.jsx("svg",{className:"w-5 h-5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),j&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"grid grid-cols-3 gap-2",children:$.map(k=>e.jsx("button",{onClick:()=>J(k.value),className:`p-2 rounded-lg border-2 transition-all duration-200 whitespace-nowrap ${N===k.value?"border-emerald-500 bg-emerald-50 text-emerald-700":"border-gray-200 bg-white text-gray-700 hover:border-emerald-300 hover:bg-emerald-50"}`,children:e.jsxs("div",{className:"flex flex-col items-center gap-1",children:[e.jsx("span",{className:"text-sm flex-shrink-0",children:k.icon}),e.jsx("span",{className:"text-xs font-medium",children:k.label})]})},k.value))}),e.jsx("button",{onClick:()=>C(!1),className:"w-full text-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200",children:"收起选项 ↑"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-base font-medium text-gray-800",children:"补充信息（可选）"}),e.jsx("textarea",{value:y,onChange:k=>g(k.target.value),placeholder:"如果识别结果需要修正或补充，请在此描述...",className:"textarea textarea-bordered w-full h-24 resize-none text-sm",disabled:T.isProcessing}),e.jsx("div",{className:"text-xs text-gray-500",children:"💡 例如：重量应该是150g，或者这是低脂版本"})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"选择餐次"}),!j&&e.jsx("button",{onClick:()=>C(!0),className:"w-full p-3 rounded-xl border-2 border-emerald-500 bg-emerald-50 text-emerald-700 transition-all duration-200 hover:bg-emerald-100",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-lg flex-shrink-0",children:K().icon}),e.jsx("span",{className:"font-medium",children:K().label})]}),e.jsx("svg",{className:"w-5 h-5 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})}),j&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"grid grid-cols-2 gap-3",children:$.map(k=>e.jsx("button",{onClick:()=>J(k.value),className:`p-3 rounded-xl border-2 transition-all duration-200 whitespace-nowrap ${N===k.value?"border-emerald-500 bg-emerald-50 text-emerald-700":"border-gray-200 bg-white text-gray-700 hover:border-emerald-300 hover:bg-emerald-50"}`,children:e.jsxs("div",{className:"flex flex-col items-center gap-1",children:[e.jsx("span",{className:"text-lg flex-shrink-0",children:k.icon}),e.jsx("span",{className:"text-sm font-medium",children:k.label})]})},k.value))}),e.jsx("button",{onClick:()=>C(!1),className:"w-full text-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200",children:"收起选项 ↑"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"选择记录日期"}),e.jsxs("div",{className:"bg-blue-50 rounded-xl p-4 border border-blue-100",children:[e.jsx("div",{className:"flex items-center justify-between mb-2"}),e.jsx("input",{type:"date",value:L.toISOString().split("T")[0],onChange:k=>D(new Date(k.target.value)),max:new Date().toISOString().split("T")[0],className:"input input-bordered input-sm w-full bg-white"}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["当前选择：",L.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})]})]})]})]}),!u&&e.jsx(e.Fragment,{children:s==="text"?e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-3",children:"描述食物"}),e.jsx("textarea",{value:l,onChange:k=>o(k.target.value),placeholder:"例如：一碗白米饭，一个苹果，200ml牛奶...",className:"textarea textarea-bordered w-full h-32 resize-none",disabled:T.isProcessing,style:T.isProcessing?{opacity:.5,pointerEvents:"none"}:{}}),e.jsx("div",{className:"text-xs text-slate-500 mt-2",children:"💡 提示：描述越详细，AI识别越准确"})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-base font-medium text-slate-800 mb-2",children:["选择图片 (",x.length,"/5)"]}),d.length>0&&e.jsx("div",{className:"grid grid-cols-3 gap-2 mb-3",children:d.map((k,_)=>e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:k,alt:`预览 ${_+1}`,className:"w-full h-20 object-cover rounded-lg border border-slate-200 cursor-pointer hover:opacity-80 transition-opacity",onClick:()=>R(_)}),e.jsx("button",{onClick:X=>{X.stopPropagation(),le(_)},className:"absolute -top-1 -right-1 btn btn-ghost btn-xs btn-circle bg-red-500 text-white hover:bg-red-600",disabled:T.isProcessing,children:"✕"})]},_))}),x.length<5&&e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("button",{onClick:A,className:"btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap",disabled:T.isProcessing,style:T.isProcessing?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-2xl flex-shrink-0",children:"📷"}),e.jsx("span",{className:"text-sm",children:"拍照识别"})]}),e.jsxs("button",{onClick:O,className:"btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap",disabled:T.isProcessing,style:T.isProcessing?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-2xl flex-shrink-0",children:"📁"}),e.jsx("span",{className:"text-sm",children:"从相册选择"})]})]}),e.jsx("input",{ref:B,type:"file",accept:"image/*",onChange:k=>{const _=k.target.files?.[0];_&&_ instanceof File?ne(_):_&&console.error("Invalid file type from input:",_)},className:"hidden"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-base font-medium text-gray-800",children:"补充描述（可选）"}),e.jsx("textarea",{value:y,onChange:k=>g(k.target.value),placeholder:"提供额外信息帮助AI更准确识别，如：这是一个中等大小的苹果，大约150g...",className:"textarea textarea-bordered w-full h-20 resize-none text-sm",disabled:T.isProcessing}),e.jsx("div",{className:"text-xs text-gray-500",children:"💡 例如：食物大小、品牌、特殊制作方式等"})]})]})}),T.error&&e.jsx("div",{className:"alert alert-error",children:e.jsx("span",{className:"text-white",children:T.error})}),T.isProcessing&&T.processingStep&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"loading loading-spinner loading-sm text-blue-600"}),e.jsx("span",{className:"text-sm text-blue-700 font-medium",children:T.processingStep})]})}),e.jsx("div",{className:"h-4"})]}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100",children:T.isProcessing?e.jsxs("button",{onClick:Ke,className:"btn btn-error text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap",children:[e.jsx("svg",{className:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),e.jsx("span",{children:"终止识别"})]}):u?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:de,className:"btn btn-outline flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap",children:"重新识别"}),e.jsxs("button",{onClick:G,className:"btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap",children:[e.jsx("svg",{className:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{children:"确认添加"})]})]}):e.jsxs("button",{onClick:U,disabled:!De()||T.isProcessing,className:"btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap",style:T.isProcessing?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("svg",{className:"w-4 h-4 mr-1 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),e.jsx("span",{children:"开始识别"})]})}),e.jsx("input",{ref:z,type:"file",accept:"image/*",capture:"environment",onChange:I,className:"hidden"}),E!==null&&e.jsx("div",{className:"fixed inset-0 bg-black/80 flex items-center justify-center z-50 pb-20",onClick:()=>R(null),children:e.jsxs("div",{className:"relative max-w-4xl max-h-4xl p-4",children:[e.jsx("img",{src:d[E],alt:`放大预览 ${E+1}`,className:"max-w-full max-h-full object-contain rounded-lg",onClick:k=>k.stopPropagation()}),e.jsx("button",{onClick:()=>R(null),className:"absolute top-2 right-2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200",children:"✕"}),d.length>1&&e.jsxs(e.Fragment,{children:[E>0&&e.jsx("button",{onClick:k=>{k.stopPropagation(),R(E-1)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200",children:"←"}),E<d.length-1&&e.jsx("button",{onClick:k=>{k.stopPropagation(),R(E+1)},className:"absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200",children:"→"})]}),e.jsxs("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm",children:[E+1," / ",d.length]})]})})]})}):null},Ee=({showAddButton:n=!0,onRecognitionComplete:t,currentDate:s,isAIProcessing:r=!1,onProcessingStateChange:a})=>{const i=Se(),l=mt(),[o,x]=b.useState(!1),[c,d]=b.useState("text"),p=m=>l.pathname===m,N=()=>{d("text"),x(!0),document.activeElement?.blur()},w=()=>{document.activeElement?.blur(),d("image"),x(!0)},f=m=>{t?.(m),x(!1)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"dock fixed bottom-0 left-0 right-0 bg-base-100 xl:bg-base-100/95 xl:backdrop-blur-sm",style:{position:"fixed",zIndex:1e3,transform:"none"},children:[e.jsxs("button",{onClick:()=>i("/dashboard"),className:`${p("/dashboard")?"dock-active":""} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`,"aria-label":"首页",tabIndex:0,disabled:r,style:r?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-lg xl:text-xl",children:"🏠"}),e.jsx("span",{className:"dock-label text-xs xl:text-sm",children:"首页"})]}),e.jsxs("button",{onClick:()=>i("/food-record"),className:`${p("/food-record")?"dock-active":""} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`,"aria-label":"开始记录食物",tabIndex:0,disabled:r,style:r?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-lg xl:text-xl",children:"🍽️"}),e.jsx("span",{className:"dock-label text-xs xl:text-sm",children:"记录"})]}),n&&e.jsxs("div",{className:`dropdown dropdown-top dropdown-center ${r?"pointer-events-none":""}`,children:[e.jsxs("button",{tabIndex:0,role:"button","aria-label":"添加食物",disabled:r,style:r?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-lg",children:"➕"}),e.jsx("span",{className:"dock-label",children:"添加"})]}),e.jsxs("ul",{tabIndex:0,className:"dropdown-content menu bg-white rounded-box z-[9999] w-32 p-1 shadow-lg border border-slate-200 mb-2",style:{opacity:1,backgroundColor:"white"},children:[e.jsx("li",{children:e.jsxs("button",{onClick:N,className:"flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap",disabled:r,children:[e.jsx("span",{className:"text-base flex-shrink-0",children:"📝"}),e.jsx("div",{className:"font-medium text-sm text-center",children:"文字识别"})]})}),e.jsx("li",{children:e.jsxs("button",{onClick:w,className:"flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap",disabled:r,children:[e.jsx("span",{className:"text-base flex-shrink-0",children:"📷"}),e.jsx("div",{className:"font-medium text-sm text-center",children:"图片识别"})]})})]})]}),!n&&e.jsxs("button",{disabled:!0,"aria-label":"添加食物",children:[e.jsx("span",{className:"text-lg opacity-30",children:"➕"}),e.jsx("span",{className:"dock-label opacity-30",children:"添加"})]}),e.jsxs("button",{onClick:()=>i("/calendar"),className:`${p("/calendar")?"dock-active":""} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`,"aria-label":"查看日历",tabIndex:0,disabled:r,style:r?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-lg xl:text-xl",children:"📅"}),e.jsx("span",{className:"dock-label text-xs xl:text-sm",children:"日历"})]}),e.jsxs("button",{onClick:()=>i("/profile"),className:`${p("/profile")?"dock-active":""} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`,"aria-label":"我的",tabIndex:0,disabled:r,style:r?{opacity:.5,pointerEvents:"none"}:{},children:[e.jsx("span",{className:"text-lg xl:text-xl",children:"👤"}),e.jsx("span",{className:"dock-label text-xs xl:text-sm",children:"我的"})]})]}),e.jsx(Xs,{isOpen:o,onClose:()=>x(!1),method:c,onRecognitionComplete:f,currentDate:s,onProcessingStateChange:a})]})},Zs=()=>{const n=Se(),{profile:t}=ge(),{getDailySummary:s,updateDailySummary:r,addDetailedFoodRecord:a}=Le(),[i,l]=b.useState(!1),[o,x]=b.useState(null),c=new Date,d=s(c);b.useEffect(()=>{ue(async()=>{const{createPageLoadAnimation:m}=await import("./animations-Cpx74AEx.js");return{createPageLoadAnimation:m}},__vite__mapDeps([0,1])).then(({createPageLoadAnimation:m})=>{m([".dashboard-card",".action-button",".hero-section",".nutrition-card",".profile-card"]),setTimeout(async()=>{const{createCountUpAnimation:g,createProgressAnimation:u}=await ue(async()=>{const{createCountUpAnimation:E,createProgressAnimation:R}=await import("./animations-Cpx74AEx.js");return{createCountUpAnimation:E,createProgressAnimation:R}},__vite__mapDeps([0,1]));document.querySelectorAll(".calorie-number").forEach(E=>{const R=E,L=parseInt(R.textContent||"0");L>0&&g(R,L,1200)}),document.querySelectorAll(".progress-bar").forEach(E=>{const R=E,L=parseFloat(R.style.width||"0");L>0&&u(R,L)});const C=document.querySelector(".dock");if(C){const E=window.getComputedStyle(C);console.log("🔍 Dock验证 - Anime.js动画后:",{position:E.position,bottom:E.bottom,zIndex:E.zIndex,transform:E.transform})}},500)})},[]),b.useEffect(()=>{if(t&&!d){const m=typeof t.dailyCalorieLimit=="string"?parseInt(t.dailyCalorieLimit):t.dailyCalorieLimit;r(c,{totalCalories:0,calorieLimit:m,remainingCalories:m,mealBreakdown:{breakfast:{mealType:"breakfast",calories:0,calorieLimit:Math.round(t.dailyCalorieLimit*t.mealRatios.breakfast),foodCount:0,percentage:0},lunch:{mealType:"lunch",calories:0,calorieLimit:Math.round(t.dailyCalorieLimit*t.mealRatios.lunch),foodCount:0,percentage:0},dinner:{mealType:"dinner",calories:0,calorieLimit:Math.round(t.dailyCalorieLimit*t.mealRatios.dinner),foodCount:0,percentage:0},snack:{mealType:"snack",calories:0,calorieLimit:ct(t.dailyCalorieLimit).totalSnackCalories,foodCount:0,percentage:0}},nutrition:{protein:0,fat:0,carbs:0,fiber:0,sugar:0,sodium:0},status:"under",percentage:0})}},[t,d,r,c]);const p=async()=>{if(!d||!t){console.log("AI分析失败：缺少必要数据",{todaySummary:d,profile:t});return}console.log("开始AI营养分析..."),l(!0);try{const m={totalCalories:d.totalCalories,calorieLimit:t.dailyCalorieLimit,protein:d.nutrition.protein,fat:d.nutrition.fat,carbs:d.nutrition.carbs,fiber:d.nutrition.fiber,sugar:d.nutrition.sugar,sodium:d.nutrition.sodium,userProfile:{age:t.age,gender:t.gender,weight:t.weight,height:t.height,targetWeight:t.targetWeight,activityLevel:t.activityLevel,bmr:t.bmr,tdee:t.tdee}},y=`
你是一位专业的营养师和减重顾问，请基于用户的个人信息和今日营养摄入数据，提供个性化的营养分析和减重建议。

用户档案：
年龄：${m.userProfile.age}岁
性别：${m.userProfile.gender}
当前体重：${rt(m.userProfile.weight,"kg")}
身高：${m.userProfile.height}cm
目标体重：${rt(m.userProfile.targetWeight,"kg")}
活动水平：${m.userProfile.activityLevel}
基础代谢率(BMR)：${nt(m.userProfile.bmr,"kcal/天")}
总日消耗(TDEE)：${nt(m.userProfile.tdee,"kcal/天")}

今日营养摄入：
卡路里摄入：${at(m.totalCalories)} / ${at(m.calorieLimit)}
蛋白质：${Y(m.protein,"g")}
脂肪：${Y(m.fat,"g")}
碳水化合物：${Y(m.carbs,"g")}
膳食纤维：${Y(m.fiber,"g")}
糖分：${Y(m.sugar,"g")}
钠：${Y(m.sodium,"mg")}

请提供专业分析，包含以下内容：
1. 营养状况评估：分析当前摄入是否均衡，是否有营养素过量或不足
2. 减重进度评价：基于卡路里摄入与TDEE的关系，评估减重效果
3. 改善建议：针对营养不均衡的具体改善方案
4. 健康提醒：基于钠、糖等指标的健康风险提示

重要格式要求：
- 请使用HTML格式进行排版，使用<br>换行，<strong>加粗重点，<p>分段
- 不要使用Markdown格式（如#、**、*等符号）
- 控制在200字以内，重点突出减重和健康改善建议
- 使用专业但易懂的中文表达

请按照以下HTML格式回复：
<p><strong>营养评估：</strong>[简要评估]</p>
<p><strong>减重进度：</strong>[进度分析]</p>
<p><strong>改善建议：</strong>[具体建议]</p>
<p><strong>健康提醒：</strong>[风险提示]</p>
      `;console.log("发送AI分析请求，提示词长度：",y.length);const g=await Be.analyzeNutritionAdvice(y);console.log("AI分析响应：",g),g&&g.advice&&g.advice.trim()?(console.log("AI建议内容：",g.advice),x(g.advice)):(console.log("AI响应为空或无效，使用默认建议"),x("AI分析完成，建议保持当前的营养摄入平衡。"))}catch(m){console.error("AI分析失败:",m),console.error("错误详情:",{message:m instanceof Error?m.message:"未知错误",stack:m instanceof Error?m.stack:void 0}),x(`AI分析失败：${m instanceof Error?m.message:"未知错误"}`)}finally{console.log("AI分析流程结束"),l(!1)}},N=()=>{l(!1),x("分析已终止")},w=m=>{m.currentTarget.classList.add("card-hover-target"),ue(async()=>{const{createCardHoverAnimation:g}=await import("./animations-Cpx74AEx.js");return{createCardHoverAnimation:g}},__vite__mapDeps([0,1])).then(({createCardHoverAnimation:g})=>{g(".card-hover-target",!0)})},f=m=>{const y=m.currentTarget;y.classList.add("card-leave-target"),ue(async()=>{const{createCardHoverAnimation:g}=await import("./animations-Cpx74AEx.js");return{createCardHoverAnimation:g}},__vite__mapDeps([0,1])).then(({createCardHoverAnimation:g})=>{g(".card-leave-target",!1)}),setTimeout(()=>{y.classList.remove("card-hover-target","card-leave-target")},350)};return t?e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50",style:{transform:"none",isolation:"auto",position:"relative",zIndex:"auto"},children:e.jsx("div",{className:"w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:d&&e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[t&&e.jsxs("div",{className:"dashboard-card relative overflow-hidden bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-indigo-200/50 shadow-xl",onMouseEnter:w,onMouseLeave:f,children:[e.jsx("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full -translate-y-16 translate-x-16"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/20 to-indigo-200/20 rounded-full translate-y-12 -translate-x-12"}),e.jsxs("div",{className:"relative z-10",children:[e.jsx("div",{className:"text-center mb-4",children:e.jsxs("div",{className:"inline-flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"🎯"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent",children:"目标倒计时"})]})}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50",children:[e.jsx("div",{className:"text-2xl font-bold text-indigo-600 mb-1",children:(()=>{const m=t.createdAt?new Date(t.createdAt):new Date,g=Math.floor((new Date().getTime()-m.getTime())/(1e3*60*60*24));return Math.max(0,t.targetDays-g)})()}),e.jsx("div",{className:"text-xs text-slate-600",children:"剩余天数"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-600 mb-1",children:F(t.weight-t.targetWeight,{precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"目标减重"}),e.jsx("div",{className:"text-xs text-purple-500",children:"kg"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50",children:[e.jsx("div",{className:"text-2xl font-bold text-pink-600 mb-1",children:F((t.weight-t.targetWeight)/t.targetDays*7,{precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"周减重"}),e.jsx("div",{className:"text-xs text-pink-500",children:"kg/周"})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[e.jsx("span",{children:"目标进度"}),e.jsxs("span",{className:"font-bold text-indigo-600",children:[(()=>{const m=t.createdAt?new Date(t.createdAt):new Date,g=Math.floor((new Date().getTime()-m.getTime())/(1e3*60*60*24)),u=Math.min(100,Math.max(0,g/t.targetDays*100));return F(u,{precision:"decimal"})})(),"% 完成"]})]}),e.jsx("div",{className:"w-full bg-white/60 rounded-full h-2 shadow-inner",children:e.jsx("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full shadow-sm transition-all duration-1000 ease-out",style:{width:`${(()=>{const m=t.createdAt?new Date(t.createdAt):new Date,g=Math.floor((new Date().getTime()-m.getTime())/(1e3*60*60*24)),u=Math.min(100,Math.max(0,g/t.targetDays*100));return F(u,{precision:"decimal"})})()}%`}})})]})]})]}),e.jsxs("div",{className:"dashboard-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl",onMouseEnter:w,onMouseLeave:f,children:[e.jsx("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full -translate-y-16 translate-x-16"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/20 to-orange-200/20 rounded-full translate-y-12 -translate-x-12"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsxs("div",{className:"inline-flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"🔥"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:"今日卡路里"})]}),e.jsx("div",{className:"w-full bg-white/60 rounded-full h-3 mb-3 shadow-inner",children:e.jsx("div",{className:"progress-bar bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full shadow-sm transition-all duration-1000 ease-out",style:{width:`${Math.min(d.totalCalories/d.calorieLimit*100,100)}%`}})}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[e.jsx("span",{children:d.totalCalories>=d.calorieLimit?e.jsxs("span",{className:"text-red-600 font-bold",children:["已超标 ",F(d.totalCalories-d.calorieLimit,{precision:"integer"})]}):e.jsxs(e.Fragment,{children:["还可摄入 ",e.jsx("span",{className:`font-bold ${d.calorieLimit-d.totalCalories<=200?"text-red-600":"text-emerald-600"}`,children:F(d.calorieLimit-d.totalCalories,{precision:"integer"})})]})}),e.jsx("span",{className:`font-bold ${d.totalCalories>=d.calorieLimit?"text-red-600":"text-orange-600"}`,children:F(d.totalCalories/d.calorieLimit*100,{precision:"integer",suffix:"%"})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50",children:[e.jsx("div",{className:"calorie-number text-2xl sm:text-3xl font-bold text-orange-600 mb-1",children:F(d.totalCalories,{precision:"integer"})}),e.jsx("div",{className:"text-xs text-slate-600 font-medium",children:"已摄入"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50",children:[e.jsx("div",{className:"calorie-number text-2xl sm:text-3xl font-bold text-red-600 mb-1",children:F(d.calorieLimit,{precision:"integer"})}),e.jsx("div",{className:"text-xs text-slate-600 font-medium",children:"目标"})]})]})]})]}),e.jsxs("div",{className:"dashboard-card relative overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-violet-200/50 shadow-xl",onMouseEnter:w,onMouseLeave:f,children:[e.jsx("div",{className:"absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-violet-200/20 to-purple-200/20 rounded-full -translate-y-18 translate-x-18"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-violet-200/20 rounded-full translate-y-16 -translate-x-16"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"🍴"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:"营养计划"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsxs("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 border border-amber-200/50 shadow-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-amber-400 to-orange-400 rounded-lg flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-sm",children:"🌅"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-amber-800 text-sm sm:text-base",children:"早餐"}),e.jsx("div",{className:"text-xs text-amber-600 whitespace-nowrap overflow-hidden text-ellipsis",children:"开启美好一天"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-lg sm:text-xl font-bold text-amber-700",children:[F(d.mealBreakdown.breakfast.calories,{precision:"integer"})," / ",F(t.dailyCalorieLimit*.3,{precision:"integer"})]}),e.jsx("div",{className:"text-xs text-amber-600",children:F(d.mealBreakdown.breakfast.calories/(t.dailyCalorieLimit*.3)*100,{precision:"integer",suffix:"%"})})]})]}),e.jsx("div",{className:"mt-3 w-full bg-amber-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full transition-all duration-1000 ease-out",style:{width:`${Math.min(d.mealBreakdown.breakfast.calories/(t.dailyCalorieLimit*.3)*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-gradient-to-r from-sky-50 to-blue-50 rounded-xl p-4 border border-sky-200/50 shadow-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-400 rounded-lg flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-sm",children:"☀️"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-sky-800 text-sm sm:text-base",children:"午餐"}),e.jsx("div",{className:"text-xs text-sky-600 whitespace-nowrap overflow-hidden text-ellipsis",children:"补充能量"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-lg sm:text-xl font-bold text-sky-700",children:[F(d.mealBreakdown.lunch.calories,{precision:"integer"})," / ",F(t.dailyCalorieLimit*.4,{precision:"integer"})]}),e.jsx("div",{className:"text-xs text-sky-600",children:F(d.mealBreakdown.lunch.calories/(t.dailyCalorieLimit*.4)*100,{precision:"integer",suffix:"%"})})]})]}),e.jsx("div",{className:"mt-3 w-full bg-sky-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-sky-400 to-sky-600 h-2 rounded-full transition-all duration-1000 ease-out",style:{width:`${Math.min(d.mealBreakdown.lunch.calories/(t.dailyCalorieLimit*.4)*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 border border-indigo-200/50 shadow-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-lg flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-sm",children:"🌙"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-indigo-800 text-sm sm:text-base",children:"晚餐"}),e.jsx("div",{className:"text-xs text-indigo-600 whitespace-nowrap overflow-hidden text-ellipsis",children:"营养收尾"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-lg sm:text-xl font-bold text-indigo-700",children:[F(d.mealBreakdown.dinner.calories,{precision:"integer"})," / ",F(t.dailyCalorieLimit*.3,{precision:"integer"})]}),e.jsx("div",{className:"text-xs text-indigo-600",children:F(d.mealBreakdown.dinner.calories/(t.dailyCalorieLimit*.3)*100,{precision:"integer",suffix:"%"})})]})]}),e.jsx("div",{className:"mt-3 w-full bg-indigo-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-indigo-400 to-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out",style:{width:`${Math.min(d.mealBreakdown.dinner.calories/(t.dailyCalorieLimit*.3)*100,100)}%`}})})]}),(()=>{const m=ct(t.dailyCalorieLimit);return e.jsxs("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200/50 shadow-lg",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-lg flex items-center justify-center shadow-md",children:e.jsx("span",{className:"text-sm",children:"🍎"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-emerald-800 text-sm sm:text-base",children:"加餐"}),e.jsx("div",{className:"text-xs text-emerald-600",children:"全天加餐总计"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-lg sm:text-xl font-bold text-emerald-700",children:[F(d.mealBreakdown.snack.calories,{precision:"integer"})," / ",m.totalSnackCalories]}),e.jsx("div",{className:"text-xs text-emerald-600",children:F(d.mealBreakdown.snack.calories/m.totalSnackCalories*100,{precision:"integer",suffix:"%"})})]})]}),e.jsx("div",{className:"mt-3 w-full bg-emerald-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full transition-all duration-1000 ease-out",style:{width:`${Math.min(d.mealBreakdown.snack.calories/m.totalSnackCalories*100,100)}%`}})})]})})()]})]})]}),e.jsxs("div",{className:"dashboard-card relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-emerald-200/50 shadow-xl",onMouseEnter:w,onMouseLeave:f,children:[e.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full -translate-y-20 -translate-x-20"}),e.jsx("div",{className:"absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-cyan-200/20 to-emerald-200/20 rounded-full translate-y-14 translate-x-14"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"📊"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent",children:"营养详情"})]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"蛋白质"}),e.jsx("span",{className:"text-xs text-emerald-600",children:"🥩"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-emerald-600 mb-1",children:Y(d.nutrition.protein,"g")}),e.jsx("div",{className:"w-full bg-emerald-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full",style:{width:`${Math.min(d.nutrition.protein/150*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"脂肪"}),e.jsx("span",{className:"text-xs text-amber-600",children:"🥑"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-amber-600 mb-1",children:Y(d.nutrition.fat,"g")}),e.jsx("div",{className:"w-full bg-amber-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full",style:{width:`${Math.min(d.nutrition.fat/80*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"碳水"}),e.jsx("span",{className:"text-xs text-blue-600",children:"🍞"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-blue-600 mb-1",children:Y(d.nutrition.carbs,"g")}),e.jsx("div",{className:"w-full bg-blue-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full",style:{width:`${Math.min(d.nutrition.carbs/300*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"纤维"}),e.jsx("span",{className:"text-xs text-green-600",children:"🥬"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-green-600 mb-1",children:Y(d.nutrition.fiber,"g")}),e.jsx("div",{className:"w-full bg-green-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full",style:{width:`${Math.min(d.nutrition.fiber/35*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"糖分"}),e.jsx("span",{className:"text-xs text-pink-600",children:"🍯"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-pink-600 mb-1",children:Y(d.nutrition.sugar||0,"g")}),e.jsx("div",{className:"w-full bg-pink-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-pink-400 to-pink-600 h-2 rounded-full",style:{width:`${Math.min((d.nutrition.sugar||0)/50*100,100)}%`}})})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"钠"}),e.jsx("span",{className:"text-xs text-purple-600",children:"🧂"})]}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-purple-600 mb-1",children:Y(d.nutrition.sodium||0,"mg")}),e.jsx("div",{className:"w-full bg-purple-100 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full",style:{width:`${Math.min((d.nutrition.sodium||0)/2300*100,100)}%`}})})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("button",{onClick:()=>i?N():p(),className:`w-full p-3 text-white rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl whitespace-nowrap ${i?"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700":"bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600"}`,children:e.jsx("div",{className:"flex items-center justify-center",children:i?e.jsx("span",{className:"font-medium",children:"终止分析"}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-lg mr-2 flex-shrink-0",children:"🤖"}),e.jsx("span",{className:"font-medium",children:"查看AI营养建议"})]})})}),o&&e.jsx("div",{className:"mt-3 p-3 bg-emerald-50 border border-emerald-200 rounded-xl",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-emerald-500 text-lg",children:"🤖"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-emerald-800 mb-1",children:"AI营养建议"}),e.jsx("div",{className:"text-sm text-emerald-700",dangerouslySetInnerHTML:{__html:o}})]})]})})]})]})]}),e.jsxs("div",{className:"dashboard-card relative overflow-hidden bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-rose-200/50 shadow-xl",onMouseEnter:w,onMouseLeave:f,children:[e.jsx("div",{className:"absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-rose-200/20 to-pink-200/20 rounded-full -translate-y-18 translate-x-18"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-red-200/20 to-rose-200/20 rounded-full translate-y-16 -translate-x-16"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-rose-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"⚡"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent",children:"代谢信息"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsx("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-slate-600 mb-1",children:"基础代谢率 (BMR)"}),e.jsx("div",{className:"text-xs text-slate-500",children:"静息状态下的能量消耗"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-2xl font-bold text-rose-600",children:F(t.bmr,{precision:"integer"})}),e.jsx("div",{className:"text-xs text-rose-500",children:"/天"})]})]})}),e.jsx("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-slate-600 mb-1",children:"总日消耗 (TDEE)"}),e.jsx("div",{className:"text-xs text-slate-500",children:"包含活动的总消耗"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-2xl font-bold text-pink-600",children:F(t.tdee,{precision:"integer"})}),e.jsx("div",{className:"text-xs text-pink-500",children:"/天"})]})]})}),e.jsx("div",{className:"bg-gradient-to-r from-rose-100/50 to-pink-100/50 rounded-lg p-3 border border-rose-200/30",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-rose-500",children:"🔥"}),e.jsxs("span",{className:"text-slate-600",children:["代谢效率：",e.jsx("span",{className:"font-semibold text-rose-600",children:F((t.tdee/t.bmr-1)*100,{precision:"integer",suffix:"%"})})," 活动加成"]})]})})]})]})]}),e.jsx("div",{className:"h-24 pb-safe"})]})})}),e.jsx(Ee,{showAddButton:!0,onRecognitionComplete:m=>{console.log("Dashboard收到识别结果:",m);const y=g=>{if(g.includes("breakfast"))return"breakfast";if(g.includes("lunch"))return"lunch";if(g.includes("dinner"))return"dinner";if(g.includes("snack"))return"snack";switch(g){case"morning-snack":return"snack";case"afternoon-snack":return"snack";case"evening-snack":return"snack";default:return"snack"}};if(m&&m.foods&&m.foods.length>0){const g=m.selectedDate||new Date;m.foods.forEach(u=>{if(!u.name||!u.calories||u.calories<=0){console.warn("跳过无效的食物记录:",u);return}const P={name:u.name,weight:u.weight||100,calories:u.calories,mealType:y(m.meal),recordedAt:g,nutrition:u.nutrition||{protein:Math.round(u.calories*.15/4),fat:Math.round(u.calories*.25/9),carbs:Math.round(u.calories*.6/4),fiber:Math.round(u.calories*.05/4),sugar:Math.round(u.calories*.1/4),sodium:Math.round(u.calories*.5)},aiRecognition:{confidence:u.confidence||.85,method:m.method,originalInput:m.content||"",dataSource:u.dataSource||(m.method==="text"?"text_analysis":"visual_estimation")},isEdited:!1};a(g,P)}),console.log("食物记录已保存到营养存储")}}})]}):e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center",children:e.jsx("span",{className:"text-2xl",children:"👤"})}),e.jsx("h2",{className:"text-xl font-bold text-slate-800 mb-2",children:"未找到用户档案"}),e.jsx("p",{className:"text-slate-600 mb-4",children:"请先完成个人档案设置"}),e.jsx("button",{className:"btn btn-primary btn-lg",onClick:()=>n("/setup"),children:"立即设置"})]})})},er=({onDateSelect:n,selectedDate:t,className:s})=>{const[r,a]=b.useState(new Date),{getDailySummary:i}=Le(),l=r.getFullYear(),o=r.getMonth()+1,x=ps(l,o),d=new Date(l,o-1,1).getDay(),p=[];for(let u=0;u<d;u++)p.push(null);x.forEach(u=>{p.push(u)});const N=()=>{const u=new Date(r);u.setMonth(u.getMonth()-1),a(u)},w=()=>{const u=new Date(r);u.setMonth(u.getMonth()+1),a(u)},f=u=>{const P=i(u);if(!P||P.totalCalories===0)return"no-data";const j=P.percentage;return j<90?"under":j<=110?"normal":j<=130?"over":"exceed"},m=(u,P,j)=>{const C="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200";if(P)return V(C,"bg-primary-600 text-white ring-2 ring-primary-200");if(j)switch(u){case"under":return V(C,"bg-blue-100 text-blue-800 ring-2 ring-blue-300");case"normal":return V(C,"bg-green-100 text-green-800 ring-2 ring-green-300");case"over":return V(C,"bg-yellow-100 text-yellow-800 ring-2 ring-yellow-300");case"exceed":return V(C,"bg-red-100 text-red-800 ring-2 ring-red-300");default:return V(C,"bg-gray-100 text-gray-800 ring-2 ring-gray-300")}switch(u){case"under":return V(C,"bg-blue-100 text-blue-800 hover:bg-blue-200");case"normal":return V(C,"bg-green-100 text-green-800 hover:bg-green-200");case"over":return V(C,"bg-yellow-100 text-yellow-800 hover:bg-yellow-200");case"exceed":return V(C,"bg-red-100 text-red-800 hover:bg-red-200");default:return V(C,"text-gray-600 hover:bg-gray-100")}},y=new Date,g=["日","一","二","三","四","五","六"];return e.jsxs("div",{className:V("bg-white rounded-lg shadow-sm border border-gray-200",s),children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[e.jsx("button",{onClick:N,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:e.jsx(Rt,{className:"h-5 w-5 text-gray-600"})}),e.jsxs("h3",{className:"text-lg font-semibold text-gray-900",children:[l,"年",o,"月"]}),e.jsx("button",{onClick:w,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:e.jsx(Tt,{className:"h-5 w-5 text-gray-600"})})]}),e.jsx("div",{className:"grid grid-cols-7 gap-1 p-4 pb-2",children:g.map(u=>e.jsx("div",{className:"text-center text-sm font-medium text-gray-500 py-2",children:u},u))}),e.jsx("div",{className:"grid grid-cols-7 gap-1 p-4 pt-0",children:p.map((u,P)=>{if(!u)return e.jsx("div",{className:"h-8"},P);const j=st(u,y,"day"),C=t&&st(u,t,"day"),E=f(u),R=u.getDate();return e.jsx("button",{onClick:()=>n?.(u),className:m(E,!!C,j),title:`${q(u,"yyyy年MM月dd日")} - ${tr(E)}`,children:R},P)})}),e.jsx("div",{className:"px-4 pb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-3 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-100"}),e.jsx("span",{className:"text-gray-600",children:"达标"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-100"}),e.jsx("span",{className:"text-gray-600",children:"接近"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-100"}),e.jsx("span",{className:"text-gray-600",children:"超标"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-100"}),e.jsx("span",{className:"text-gray-600",children:"未达标"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-100"}),e.jsx("span",{className:"text-gray-600",children:"无数据"})]})]})})]})},tr=n=>{switch(n){case"under":return"未达标";case"normal":return"达标";case"over":return"接近超标";case"exceed":return"超标";default:return"无数据"}};Yt.register(Xt,Zt,es,ts,ss,rs,as,ns,is,ls);const sr=({data:n,height:t=300})=>{const s={labels:n.trends.map(a=>q(a.date,"MM/dd")),datasets:[{label:"实际摄入",data:n.trends.map(a=>a.calories),borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)",fill:!0,tension:.4,pointRadius:4,pointHoverRadius:6},{label:"目标摄入",data:n.trends.map(()=>n.calories.target),borderColor:"#6b7280",backgroundColor:"transparent",borderDash:[5,5],fill:!1,pointRadius:0,pointHoverRadius:0}]},r={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20}},tooltip:{mode:"index",intersect:!1,callbacks:{label:a=>`${a.dataset.label}: ${a.parsed.y} kcal`}}},scales:{x:{grid:{display:!1}},y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{callback:a=>`${a} kcal`}}},interaction:{mode:"nearest",axis:"x",intersect:!1}};return e.jsx("div",{style:{height:t},children:e.jsx($t,{data:s,options:r})})},rr=({breakfast:n,lunch:t,dinner:s,snack:r=0,size:a=200})=>{const i=n+t+s+r;if(i===0)return e.jsx("div",{className:"flex items-center justify-center text-gray-500 text-sm",style:{width:a,height:a},children:"暂无数据"});const l={labels:r>0?["早餐","午餐","晚餐","零食"]:["早餐","午餐","晚餐"],datasets:[{data:r>0?[n,t,s,r]:[n,t,s],backgroundColor:["#fbbf24","#10b981","#3b82f6","#8b5cf6"],borderColor:["#f59e0b","#059669","#2563eb","#7c3aed"],borderWidth:2,hoverBorderWidth:3}]},o={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:15,usePointStyle:!0,font:{size:12}}},tooltip:{callbacks:{label:x=>{const c=Math.round(x.parsed/i*100);return`${x.label}: ${x.parsed} kcal (${c}%)`}}}},cutout:"50%"};return e.jsx("div",{style:{width:a,height:a},children:e.jsx(ut,{data:l,options:o})})},ar=({data:n,height:t=250})=>{const s={labels:n.map(a=>a.day),datasets:[{label:"实际摄入",data:n.map(a=>a.current),backgroundColor:"rgba(16, 185, 129, 0.8)",borderColor:"#10b981",borderWidth:1,borderRadius:4,borderSkipped:!1},{label:"目标摄入",data:n.map(a=>a.target),backgroundColor:"rgba(107, 114, 128, 0.3)",borderColor:"#6b7280",borderWidth:1,borderRadius:4,borderSkipped:!1}]},r={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20}},tooltip:{callbacks:{label:a=>`${a.dataset.label}: ${a.parsed.y} kcal`}}},scales:{x:{grid:{display:!1}},y:{beginAtZero:!0,grid:{color:"rgba(0, 0, 0, 0.1)"},ticks:{callback:a=>`${a} kcal`}}}};return e.jsx("div",{style:{height:t},children:e.jsx(Ot,{data:s,options:r})})},nr=({protein:n,fat:t,carbs:s,size:r=180})=>{const a=n+t+s;if(a===0)return e.jsx("div",{className:"flex items-center justify-center text-gray-500 text-sm",style:{width:r,height:r},children:"暂无数据"});const i={labels:["蛋白质","脂肪","碳水化合物"],datasets:[{data:[n,t,s],backgroundColor:["#ef4444","#f59e0b","#10b981"],borderColor:["#dc2626","#d97706","#059669"],borderWidth:2}]},l={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:10,usePointStyle:!0,font:{size:11}}},tooltip:{callbacks:{label:o=>{const x=Math.round(o.parsed/a*100);return`${o.label}: ${o.parsed}g (${x}%)`}}}},cutout:"60%"};return e.jsx("div",{style:{width:r,height:r},children:e.jsx(ut,{data:i,options:l})})},ir=({date:n,summary:t,onClose:s})=>{if(!t)return e.jsxs(fe,{children:[e.jsx(Ce,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(Me,{children:q(n,"yyyy年MM月dd日")}),s&&e.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:"✕"})]})}),e.jsx(ve,{children:e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx("p",{children:"这一天还没有记录任何食物"}),e.jsx("p",{className:"text-sm mt-1",children:"开始记录您的第一餐吧！"})]})})]});const r=i=>{switch(i){case"under":return e.jsx(be,{variant:"default",children:"未达标"});case"normal":return e.jsx(be,{variant:"success",children:"达标"});case"over":return e.jsx(be,{variant:"warning",children:"接近超标"});case"exceed":return e.jsx(be,{variant:"danger",children:"超标"});default:return e.jsx(be,{variant:"default",children:"无数据"})}},a={breakfast:"早餐",lunch:"午餐",dinner:"晚餐",snack:"零食"};return e.jsxs(fe,{children:[e.jsx(Ce,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(Me,{children:q(n,"yyyy年MM月dd日")}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[r(t.status),e.jsxs("span",{className:"text-sm text-gray-500",children:["完成度 ",Math.round(t.percentage),"%"]})]})]}),s&&e.jsx("button",{onClick:s,className:"text-gray-400 hover:text-gray-600 text-xl",children:"✕"})]})}),e.jsx(ve,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[e.jsx("div",{className:"text-lg font-semibold text-blue-700",children:e.jsx(he,{value:t.totalCalories})}),e.jsx("div",{className:"text-xs text-blue-600",children:"已摄入"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[e.jsx("div",{className:"text-lg font-semibold text-gray-700",children:e.jsx(he,{value:t.calorieLimit})}),e.jsx("div",{className:"text-xs text-gray-600",children:"目标"})]}),e.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[e.jsx("div",{className:"text-lg font-semibold text-green-700",children:e.jsx(he,{value:Math.max(0,t.remainingCalories)})}),e.jsx("div",{className:"text-xs text-green-600",children:"剩余"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"三餐分布"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(rr,{breakfast:t.mealBreakdown.breakfast.calories,lunch:t.mealBreakdown.lunch.calories,dinner:t.mealBreakdown.dinner.calories,snack:t.mealBreakdown.snack.calories,size:150}),e.jsx("div",{className:"flex-1 space-y-2",children:Object.entries(t.mealBreakdown).map(([i,l])=>i==="snack"&&l.calories===0?null:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:a[i]}),e.jsxs(be,{size:"sm",variant:"default",children:[l.foodCount,"项"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-medium",children:e.jsx(he,{value:l.calories})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["目标: ",e.jsx(he,{value:l.calorieLimit})]})]})]},i))})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"营养素分布"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(nr,{protein:t.nutrition.protein,fat:t.nutrition.fat,carbs:t.nutrition.carbs,size:130}),e.jsxs("div",{className:"flex-1 space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"蛋白质"}),e.jsx("span",{className:"font-medium",children:Y(t.nutrition.protein,"g")})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"脂肪"}),e.jsx("span",{className:"font-medium",children:Y(t.nutrition.fat,"g")})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"碳水化合物"}),e.jsx("span",{className:"font-medium",children:Y(t.nutrition.carbs,"g")})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"膳食纤维"}),e.jsx("span",{className:"font-medium",children:Y(t.nutrition.fiber,"g")})]})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"今日进度"}),e.jsxs("span",{className:"font-medium",children:[Math.round(t.percentage),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${t.percentage<90?"bg-blue-500":t.percentage<=110?"bg-green-500":t.percentage<=130?"bg-yellow-500":"bg-red-500"}`,style:{width:`${Math.min(t.percentage,100)}%`}})})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"flex-1 bg-primary-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors",children:"添加食物"}),e.jsx("button",{className:"flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors",children:"查看详情"})]})]})})]})},lr=()=>{const[n,t]=b.useState(null),{getDailySummary:s,getWeeklyAnalysis:r}=Le();Se();const a=new Date,i=n?s(n):null,l=r(a),o=l.trends.map((x,c)=>{const d=["周日","周一","周二","周三","周四","周五","周六"],p=x.date.getDay();return{day:d[p],current:x.calories,target:l.calories.target}});return e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"min-h-screen bg-gray-50 p-4",style:{transform:"none",isolation:"auto",position:"relative",zIndex:"auto"},children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-2",children:"营养日历"}),e.jsx("p",{className:"text-gray-600 lg:text-lg",children:"查看您的营养摄入历史和趋势分析"})]}),e.jsxs("div",{className:"space-y-6 lg:space-y-8",children:[e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8",children:[e.jsx("div",{className:"xl:col-span-1",children:e.jsx(er,{selectedDate:n||void 0,onDateSelect:t})}),e.jsx("div",{className:"xl:col-span-1",children:n?e.jsx(ir,{date:n,summary:i,onClose:()=>t(null)}):e.jsx(fe,{children:e.jsx(ve,{children:e.jsxs("div",{className:"text-center py-12 text-gray-500",children:[e.jsx("div",{className:"text-4xl mb-4",children:"📅"}),e.jsx("p",{className:"text-lg font-medium mb-2",children:"选择日期查看详情"}),e.jsx("p",{className:"text-sm",children:"点击日历上的任意日期查看当天的营养摄入详情"})]})})})})]}),e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8",children:[e.jsxs(fe,{children:[e.jsx(Ce,{children:e.jsx(Me,{children:"本周卡路里趋势"})}),e.jsx(ve,{children:e.jsx(sr,{data:l,height:250})})]}),e.jsxs(fe,{children:[e.jsx(Ce,{children:e.jsx(Me,{children:"本周每日对比"})}),e.jsx(ve,{children:e.jsx(ar,{data:o,height:250})})]})]}),e.jsxs(fe,{children:[e.jsx(Ce,{children:e.jsx(Me,{children:"本周统计"})}),e.jsx(ve,{children:e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-700",children:Math.round(l.calories.average)}),e.jsx("div",{className:"text-sm text-blue-600",children:"平均每日卡路里"})]}),e.jsxs("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-green-700",children:Math.round(l.calories.total)}),e.jsx("div",{className:"text-sm text-green-600",children:"本周总卡路里"})]}),e.jsxs("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[e.jsxs("div",{className:"text-2xl font-bold text-yellow-700",children:[Math.round(l.calories.adherenceRate*100),"%"]}),e.jsx("div",{className:"text-sm text-yellow-600",children:"目标达成率"})]}),e.jsxs("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-purple-700",children:l.trends.filter(x=>x.calories>0).length}),e.jsx("div",{className:"text-sm text-purple-600",children:"记录天数"})]})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[e.jsx(qe,{variant:"primary",onClick:()=>t(a),children:"查看今天"}),e.jsx(qe,{variant:"secondary",onClick:()=>alert("导出功能即将推出"),children:"导出数据"})]})]}),e.jsx("div",{className:"h-24 pb-safe"})]})}),e.jsx(Ee,{showAddButton:!0,onRecognitionComplete:x=>{console.log("Calendar收到识别结果:",x)}})]})},or=()=>{const[n,t]=b.useState({}),[s,r]=b.useState(0),[a,i]=b.useState([]),[l,o]=b.useState(!1),[x,c]=b.useState(null),d=(w,f,m)=>{const y={type:w,message:f,solution:m,timestamp:Date.now()};i(g=>[y,...g.slice(0,19)])},p=(w,f)=>{const m=f.bottom,y=window.innerHeight,g=Math.abs(m-y)<5;return x===null?(c(m),g):!(Math.abs(m-x)>5)&&g};b.useEffect(()=>{const w=()=>{const m=document.querySelector(".bottom-navigation")||document.querySelector('nav[style*="2147483647"]'),y=document.body;if(i([]),!m){d("error","底部导航栏元素不存在","检查BottomNavigation组件是否正确渲染，确认选择器.bottom-navigation是否正确"),t({exists:!1});return}const g=window.getComputedStyle(m),u=m.getBoundingClientRect(),P=p(m,u);o(P),g.position!=="fixed"&&d("error",`position属性错误: ${g.position}`,"设置 position: fixed !important"),g.bottom!=="0px"&&d("error",`bottom属性错误: ${g.bottom}`,"设置 bottom: 0 !important");const j=parseInt(g.zIndex);(isNaN(j)||j<1e3)&&d("warning",`z-index过低: ${g.zIndex}`,"设置更高的z-index值，建议使用2147483647"),g.display==="none"&&d("error","display为none，导航栏不可见","设置 display: block !important"),g.visibility==="hidden"&&d("error","visibility为hidden，导航栏不可见","设置 visibility: visible !important"),parseFloat(g.opacity)<.1&&d("warning",`opacity过低: ${g.opacity}`,"设置 opacity: 1 !important");const C=u.bottom>=0&&u.top<=window.innerHeight;C||d("error","导航栏不在视口内","检查top/bottom值，确保导航栏在屏幕可见区域");const E=document.elementFromPoint(window.innerWidth/2,window.innerHeight-10);E&&!m.contains(E)&&d("warning",`导航栏被遮挡，底部元素: ${E.tagName}`,"检查z-index或其他元素的层级");let R=m.parentElement;for(;R&&R!==document.body;){if(window.getComputedStyle(R).transform!=="none"){d("error",`父元素${R.tagName}有transform属性，创建了新的层叠上下文`,"将导航栏移到根级别，脱离transform影响");break}R=R.parentElement}window.scrollY>100&&!P&&d("error","导航栏随页面滚动移动，未真正固定","检查position:fixed是否生效，排查层叠上下文问题"),u.height<50&&d("warning",`导航栏高度过小: ${u.height}px`,"设置合适的最小高度，建议72px以上"),t({exists:!0,tagName:m.tagName,className:m.className,position:g.position,top:g.top,bottom:g.bottom,left:g.left,right:g.right,zIndex:g.zIndex,display:g.display,visibility:g.visibility,opacity:g.opacity,width:g.width,height:g.height,minHeight:g.minHeight,rectTop:u.top,rectBottom:u.bottom,rectLeft:u.left,rectRight:u.right,rectWidth:u.width,rectHeight:u.height,viewportHeight:window.innerHeight,viewportWidth:window.innerWidth,scrollY:window.scrollY,bodyScrollHeight:y.scrollHeight,bodyClientHeight:y.clientHeight,transform:g.transform,isolation:g.isolation,parentTagName:m.parentElement?.tagName,parentClassName:m.parentElement?.className,parentPosition:m.parentElement?window.getComputedStyle(m.parentElement).position:"none",parentTransform:m.parentElement?window.getComputedStyle(m.parentElement).transform:"none",inlineStyle:m.style.cssText,inViewport:C,elementAtBottom:E?.tagName,isFixed:P})},f=()=>{r(window.scrollY),w()};return w(),window.addEventListener("scroll",f),window.addEventListener("resize",w),()=>{window.removeEventListener("scroll",f),window.removeEventListener("resize",w)}},[]);const N=()=>{alert("调试模式 - 记录食物")};return e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"min-h-screen bg-gray-100 p-4",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("h1",{className:"text-2xl font-bold mb-6 text-center",children:["底部导航栏固定性调试工具",e.jsxs("div",{className:`text-sm mt-2 ${l?"text-green-600":"text-red-600"}`,children:["状态: ",l?"✅ 正常固定":"❌ 未正确固定"]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[e.jsxs("h2",{className:"text-lg font-semibold mb-4",children:["自动错误检测",e.jsxs("span",{className:"text-sm text-gray-500",children:["(",a.length," 个问题)"]})]}),a.length===0?e.jsx("div",{className:"text-green-600 text-center py-4",children:"✅ 未检测到问题，导航栏应该正常固定"}):e.jsx("div",{className:"space-y-3",children:a.map((w,f)=>e.jsxs("div",{className:`p-4 rounded-lg border-l-4 ${w.type==="error"?"bg-red-50 border-red-500":w.type==="warning"?"bg-yellow-50 border-yellow-500":"bg-blue-50 border-blue-500"}`,children:[e.jsxs("div",{className:`font-medium ${w.type==="error"?"text-red-800":w.type==="warning"?"text-yellow-800":"text-blue-800"}`,children:[w.type==="error"?"🚨":w.type==="warning"?"⚠️":"ℹ️"," ",w.message]}),e.jsxs("div",{className:"text-sm text-gray-600 mt-1",children:[e.jsx("strong",{children:"解决方案:"})," ",w.solution]})]},f))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"详细技术信息"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:Object.entries(n).map(([w,f])=>e.jsxs("div",{className:"flex justify-between border-b pb-1",children:[e.jsxs("span",{className:"font-medium text-gray-600",children:[w,":"]}),e.jsx("span",{className:`font-mono ${w==="exists"&&!f||w==="position"&&f!=="fixed"?"text-red-600":w==="zIndex"&&f!=="2147483647"?"text-orange-600":w==="inViewport"&&!f||w==="isFixed"&&!f?"text-red-600":w==="isFixed"&&f?"text-green-600":"text-gray-900"}`,children:typeof f=="boolean"?f?"✅":"❌":String(f)})]},w))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[e.jsxs("h2",{className:"text-lg font-semibold mb-4",children:["固定性测试区域",e.jsxs("span",{className:"text-sm text-gray-500",children:["(滚动位置: ",s,"px)"]})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[e.jsx("h3",{className:"font-medium text-blue-800 mb-2",children:"测试说明:"}),e.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[e.jsx("li",{children:"• 滚动页面时，底部导航栏应该始终固定在屏幕底部"}),e.jsx("li",{children:"• 如果导航栏随页面滚动移动，说明position:fixed未生效"}),e.jsx("li",{children:"• 观察上方的错误报告，查看具体问题和解决方案"})]})]}),e.jsx("div",{className:"space-y-4",children:Array.from({length:30},(w,f)=>e.jsxs("div",{className:"bg-gray-50 p-4 rounded border",children:[e.jsxs("h3",{className:"font-medium",children:["测试内容块 ",f+1]}),e.jsx("p",{className:"text-gray-600",children:"这是用于测试滚动的内容块。请滚动页面并观察底部导航栏是否保持固定在屏幕底部。 如果导航栏随页面移动，说明存在层叠上下文或定位问题。"}),f===10&&e.jsx("div",{className:"mt-2 p-2 bg-yellow-100 rounded text-yellow-800 text-sm",children:"⚠️ 中间检查点：此时应该已经滚动了一定距离，导航栏应该仍然固定在底部"}),f===20&&e.jsx("div",{className:"mt-2 p-2 bg-orange-100 rounded text-orange-800 text-sm",children:"🔍 深度测试点：大量滚动后，导航栏的固定性是关键测试指标"})]},f))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4",children:"手动测试工具"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx("button",{className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},children:"📍 滚动到顶部"}),e.jsx("button",{className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors",onClick:()=>{window.scrollTo({top:window.innerHeight,behavior:"smooth"})},children:"📍 滚动一屏"}),e.jsx("button",{className:"bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 transition-colors",onClick:()=>{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})},children:"📍 滚动到底部"}),e.jsx("button",{className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors",onClick:()=>{window.dispatchEvent(new Event("scroll"))},children:"🔄 重新检测"})]}),e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded text-sm text-gray-600",children:"💡 使用这些按钮测试不同滚动位置下导航栏的固定性。正常情况下，无论滚动到哪里，导航栏都应该保持在屏幕底部。"})]}),e.jsx("div",{className:"h-32"})]})}),e.jsx(Ee,{onRecognitionComplete:N})]})};class dr{dbName="KcalTrackerDB";dbVersion=1;db=null;async initialize(){try{if(!this.isSupported())throw new Error("浏览器不支持IndexedDB");this.requestPersistentStorage().catch(t=>{console.warn("持久化存储权限申请失败，但不影响应用运行:",t)}),this.db=await this.openDatabase(),console.log("IndexedDB初始化成功")}catch(t){throw console.error("IndexedDB初始化失败:",t),t}}isSupported(){return"indexedDB"in window&&"navigator"in window&&"storage"in navigator}async requestPersistentStorage(){try{if(!("storage"in navigator)||!("persist"in navigator.storage))return console.info("浏览器不支持持久化存储API，使用默认存储机制"),!1;if(await navigator.storage.persisted())return console.log("持久化存储: 已启用（浏览器自动授予）"),!0;const s=await navigator.storage.persist();return s?console.log("持久化存储: 申请成功"):(console.info("持久化存储: 未获得权限，但应用仍可正常使用"),console.info("注意: Chrome浏览器通常会在适当时机自动授予持久化存储权限")),s}catch(t){return console.warn("检查持久化存储权限时发生错误:",t),!1}}async getStorageStatus(){try{const t=this.isSupported();let s=!1,r={used:0,total:0,percentage:0,remaining:0,persistent:0,temporary:0,persistentPercentage:0,temporaryPercentage:0};if(t&&"storage"in navigator&&("persisted"in navigator.storage&&(s=await navigator.storage.persisted()),"estimate"in navigator.storage)){const a=await navigator.storage.estimate(),i=a.usage||0,l=a.quota||0,o=l>0?Math.round(i/l*100):0,x=l-i;let c=0,d=i;s&&(c=Math.round(i*.8),d=i-c);const p=l>0?Math.round(c/l*100):0,N=l>0?Math.round(d/l*100):0;r={used:i,total:l,percentage:o,remaining:x,persistent:c,temporary:d,persistentPercentage:p,temporaryPercentage:N}}return{isPersistent:s,quota:r,isSupported:t}}catch(t){return console.error("获取存储状态失败:",t),{isPersistent:!1,quota:{used:0,total:0,percentage:0,remaining:0,persistent:0,temporary:0,persistentPercentage:0,temporaryPercentage:0},isSupported:!1}}}openDatabase(){return new Promise((t,s)=>{const r=indexedDB.open(this.dbName,this.dbVersion);r.onerror=()=>{s(new Error("无法打开数据库"))},r.onsuccess=()=>{t(r.result)},r.onupgradeneeded=a=>{const i=a.target.result;this.createObjectStores(i)}})}createObjectStores(t){if(t.objectStoreNames.contains("profiles")||t.createObjectStore("profiles",{keyPath:"id"}).createIndex("userId","userId",{unique:!1}),!t.objectStoreNames.contains("nutritionRecords")){const s=t.createObjectStore("nutritionRecords",{keyPath:"id"});s.createIndex("date","date",{unique:!1}),s.createIndex("mealType","mealType",{unique:!1})}t.objectStoreNames.contains("settings")||t.createObjectStore("settings",{keyPath:"key"}),console.log("数据库表结构创建完成")}async setItem(t,s){if(!this.db)throw new Error("数据库未初始化");return new Promise((r,a)=>{const o=this.db.transaction([t],"readwrite").objectStore(t).put(s);o.onsuccess=()=>r(),o.onerror=()=>a(new Error("数据存储失败"))})}async getItem(t,s){if(!this.db)throw new Error("数据库未初始化");return new Promise((r,a)=>{const o=this.db.transaction([t],"readonly").objectStore(t).get(s);o.onsuccess=()=>{r(o.result||null)},o.onerror=()=>a(new Error("数据获取失败"))})}async getAllItems(t){if(!this.db)throw new Error("数据库未初始化");return new Promise((s,r)=>{const l=this.db.transaction([t],"readonly").objectStore(t).getAll();l.onsuccess=()=>{s(l.result||[])},l.onerror=()=>r(new Error("数据获取失败"))})}async removeItem(t,s){if(!this.db)throw new Error("数据库未初始化");return new Promise((r,a)=>{const o=this.db.transaction([t],"readwrite").objectStore(t).delete(s);o.onsuccess=()=>r(),o.onerror=()=>a(new Error("数据删除失败"))})}async clear(t){if(!this.db)throw new Error("数据库未初始化");return new Promise((s,r)=>{const l=this.db.transaction([t],"readwrite").objectStore(t).clear();l.onsuccess=()=>s(),l.onerror=()=>r(new Error("数据清空失败"))})}async checkStorageUsage(){try{return(await this.getStorageStatus()).quota.percentage>=85?(console.warn("存储空间使用率达到85%，建议清理数据"),!0):!1}catch(t){return console.error("检查存储使用率失败:",t),!1}}close(){this.db&&(this.db.close(),this.db=null)}}const Ge=new dr,cr=()=>{const n=Se(),{profile:t,clearProfile:s}=ge(),{dailySummaries:r,clearData:a,addDetailedFoodRecord:i}=Le(),[l,o]=b.useState(!1),[x,c]=b.useState(null),[d,p]=b.useState(!1),{models:N,getActiveModel:w,fixModelVisionSupport:f}=je(),m=b.useRef(null),[y,g]=b.useState(null),[u,P]=b.useState(!1),[j,C]=b.useState(!1),[E,R]=b.useState(!1),[L,D]=b.useState(!1);b.useEffect(()=>{(()=>{const U=window.matchMedia("(display-mode: standalone)").matches,G=navigator.standalone===!0,de=document.referrer.includes("android-app://"),oe=U||G||de;o(oe),p(!oe)})();const I=U=>{U.preventDefault(),c(U),p(!0)},O=()=>{o(!0),p(!1),c(null)};return window.addEventListener("beforeinstallprompt",I),window.addEventListener("appinstalled",O),()=>{window.removeEventListener("beforeinstallprompt",I),window.removeEventListener("appinstalled",O)}},[]);const B=async()=>{if(x)try{x.prompt();const{outcome:A}=await x.userChoice;console.log(A==="accepted"?"用户接受了PWA安装":"用户拒绝了PWA安装"),c(null)}catch(A){console.error("PWA安装失败:",A)}},z=async()=>{try{const A=await Ge.getStorageStatus();g(A),A.quota.percentage>=85&&P(!0)}catch(A){console.error("获取存储状态失败:",A)}},T=A=>{if(A===0)return"0 B";const I=1024,O=["B","KB","MB","GB"],U=Math.floor(Math.log(A)/Math.log(I));return Math.round(A/Math.pow(I,U)*100)/100+" "+O[U]},Q=()=>{try{const A={version:"1.0",timestamp:new Date().toISOString(),profile:t,nutritionRecords:Object.values(r),settings:{}},I=JSON.stringify(A,null,2),O=new Blob([I],{type:"application/json"}),U=URL.createObjectURL(O),G=document.createElement("a");G.href=U,G.download=`kcal-tracker-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(G),G.click(),document.body.removeChild(G),URL.revokeObjectURL(U),alert("数据导出成功！")}catch(A){console.error("数据导出失败:",A),alert("数据导出失败，请重试。")}},h=()=>{m.current?.click()},S=async A=>{const I=A.target.files?.[0];if(I){try{const O=await I.text(),U=JSON.parse(O);if(!U.version||!U.profile||!U.nutritionRecords)throw new Error("数据格式不正确");if(!confirm("导入数据将替换当前所有数据，是否继续？"))return;alert("数据导入成功！"),window.location.reload()}catch(O){console.error("数据导入失败:",O),alert("数据导入失败，请检查文件格式。")}m.current&&(m.current.value="")}};b.useEffect(()=>{f(),z(),ue(async()=>{const{createPageLoadAnimation:A}=await import("./animations-Cpx74AEx.js");return{createPageLoadAnimation:A}},__vite__mapDeps([0,1])).then(({createPageLoadAnimation:A})=>{A([".profile-card",".action-button",".settings-card",".stats-card"]),setTimeout(async()=>{const{createCountUpAnimation:O}=await ue(async()=>{const{createCountUpAnimation:G}=await import("./animations-Cpx74AEx.js");return{createCountUpAnimation:G}},__vite__mapDeps([0,1]));document.querySelectorAll(".profile-number").forEach(G=>{const de=G,oe=parseFloat(de.textContent||"0");oe>0&&O(de,oe,1200)})},500)})},[]);const $=A=>{ue(async()=>{const{createButtonAnimation:I}=await import("./animations-Cpx74AEx.js");return{createButtonAnimation:I}},__vite__mapDeps([0,1])).then(({createButtonAnimation:I})=>{I(A.currentTarget)})},K=A=>{A.currentTarget.classList.add("card-hover-target"),ue(async()=>{const{createCardHoverAnimation:O}=await import("./animations-Cpx74AEx.js");return{createCardHoverAnimation:O}},__vite__mapDeps([0,1])).then(({createCardHoverAnimation:O})=>{O(".card-hover-target",!0)})},J=A=>{const I=A.currentTarget;I.classList.add("card-leave-target"),ue(async()=>{const{createCardHoverAnimation:O}=await import("./animations-Cpx74AEx.js");return{createCardHoverAnimation:O}},__vite__mapDeps([0,1])).then(({createCardHoverAnimation:O})=>{O(".card-leave-target",!1)}),setTimeout(()=>{I.classList.remove("card-hover-target","card-leave-target")},350)};if(!t)return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center",children:e.jsx("span",{className:"text-2xl",children:"👤"})}),e.jsx("h2",{className:"text-xl font-bold text-slate-800 mb-2",children:"未找到用户档案"}),e.jsx("p",{className:"text-slate-600 mb-4",children:"请先完成个人档案设置"}),e.jsx("button",{className:"btn btn-primary btn-lg",onClick:()=>n("/setup"),children:"立即设置"})]})});const ee=t.weight-t.targetWeight,re=ee/t.targetDays*7,ne=t.weight/Math.pow(t.height/100,2),le=A=>{const I=new Date;return I.setDate(I.getDate()+A),I.toLocaleDateString("zh-CN",{month:"numeric",day:"numeric"}).replace("/",".")};return e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50",style:{transform:"none",isolation:"auto",position:"relative",zIndex:"auto"},children:e.jsx("div",{className:"w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"text-3xl sm:text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2",children:"我的"}),e.jsx("p",{className:"text-slate-600",children:"个人信息与设置"})]}),e.jsxs("div",{className:"profile-card relative overflow-hidden bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-sky-200/50 shadow-xl",onMouseEnter:K,onMouseLeave:J,children:[e.jsx("div",{className:"absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-sky-200/20 to-blue-200/20 rounded-full -translate-y-18 translate-x-18"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-sky-200/20 rounded-full translate-y-16 -translate-x-16"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-sky-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"👤"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-sky-600 to-blue-600 bg-clip-text text-transparent",children:"个人档案"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-sky-600 mb-1",children:e.jsx(te,{value:t.weight,precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"当前体重"}),e.jsx("div",{className:"text-xs text-sky-500",children:"kg"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-blue-600 mb-1",children:e.jsx(te,{value:t.targetWeight,precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"目标体重"}),e.jsx("div",{className:"text-xs text-blue-500",children:"kg"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50 cursor-pointer hover:bg-white/90 transition-all duration-200 hover:shadow-lg relative",onClick:()=>D(!L),children:[e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:`w-4 h-4 text-slate-400 transition-transform duration-300 ${L?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"})})}),e.jsx("div",{className:"profile-number text-2xl font-bold text-indigo-600 mb-1 transition-all duration-300 min-h-[32px] flex items-center justify-center",children:L?e.jsx("span",{className:"text-2xl leading-tight",children:le(t.targetDays)}):e.jsx($s,{value:t.targetDays,className:""})}),e.jsx("div",{className:"text-xs text-slate-600",children:L?"截止日期":"目标天数"}),!L&&e.jsx("div",{className:"text-xs text-indigo-500",children:"天"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-cyan-600 mb-1",children:e.jsx(te,{value:ee,precision:"auto",className:""})}),e.jsx("div",{className:"text-xs text-slate-600",children:"需减重量"}),e.jsx("div",{className:"text-xs text-cyan-500",children:"kg"})]})]})]})]}),e.jsxs("div",{className:"stats-card relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-emerald-200/50 shadow-xl",onMouseEnter:K,onMouseLeave:J,children:[e.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full -translate-y-20 -translate-x-20"}),e.jsx("div",{className:"absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-cyan-200/20 to-emerald-200/20 rounded-full translate-y-14 translate-x-14"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"📊"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent",children:"健康数据"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-emerald-600 mb-1",children:e.jsx(te,{value:ne,precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"BMI指数"}),e.jsx("div",{className:"text-xs text-emerald-500",children:ne<18.5?"偏瘦":ne<24?"正常":ne<28?"偏胖":"肥胖"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-teal-600 mb-1",children:e.jsx(te,{value:t.height,precision:"auto"})}),e.jsx("div",{className:"text-xs text-slate-600",children:"身高"}),e.jsx("div",{className:"text-xs text-teal-500",children:"cm"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-cyan-600 mb-1",children:e.jsx(te,{value:re,precision:"auto",className:""})}),e.jsx("div",{className:"text-xs text-slate-600",children:"每周减重"}),e.jsx("div",{className:"text-xs text-cyan-500",children:"kg/周"})]}),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-md border border-white/50",children:[e.jsx("div",{className:"profile-number text-2xl font-bold text-blue-600 mb-1",children:e.jsx(te,{value:t.age,precision:"auto",className:""})}),e.jsx("div",{className:"text-xs text-slate-600",children:"年龄"}),e.jsx("div",{className:"text-xs text-purple-500",children:"岁"})]})]})]})]}),y&&e.jsxs("div",{className:"storage-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl",onMouseEnter:K,onMouseLeave:J,children:[e.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-orange-200/20 to-amber-200/20 rounded-full -translate-y-20 -translate-x-20"}),e.jsx("div",{className:"absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-yellow-200/20 to-orange-200/20 rounded-full translate-y-14 translate-x-14"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"💾"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent",children:"存储状态"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-slate-600",children:"存储使用率"}),e.jsxs("span",{className:`text-sm font-bold ${y.quota.percentage>=85?"text-red-600":y.quota.percentage>=70?"text-amber-600":"text-green-600"}`,children:[y.quota.percentage,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:e.jsx("div",{className:`h-3 rounded-full transition-all duration-300 ${y.quota.percentage>=85?"bg-gradient-to-r from-red-400 to-red-600":y.quota.percentage>=70?"bg-gradient-to-r from-amber-400 to-amber-600":"bg-gradient-to-r from-green-400 to-green-600"}`,style:{width:`${Math.min(y.quota.percentage,100)}%`}})}),e.jsxs("div",{className:"flex justify-between text-xs text-slate-500",children:[e.jsxs("span",{children:["已用: ",T(y.quota.used)]}),e.jsxs("span",{children:["总计: ",T(y.quota.total)]})]})]}),u&&e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("span",{className:"text-red-600",children:"⚠️"}),e.jsx("span",{className:"text-sm font-medium text-red-800",children:"存储空间警告"})]}),e.jsx("p",{className:"text-xs text-red-700 mb-3",children:"存储空间使用率已达到85%，建议导出数据以释放空间。"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:Q,className:"flex-1 bg-red-600 text-white text-xs py-2 px-3 rounded-lg hover:bg-red-700 transition-colors",children:"立即导出"}),e.jsx("button",{onClick:()=>P(!1),className:"flex-1 bg-gray-300 text-gray-700 text-xs py-2 px-3 rounded-lg hover:bg-gray-400 transition-colors",children:"稍后处理"})]})]}),e.jsx("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`text-lg ${y.isPersistent?"text-green-600":"text-amber-600"}`,children:y.isPersistent?"✅":"📱"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"text-sm font-medium text-slate-800",children:["当前存储模式：",y.isPersistent?"持久化存储":"临时存储"]}),e.jsx("div",{className:"text-xs text-slate-600",children:y.isPersistent?"数据受到保护，不会在浏览器清理时丢失":"浏览器会根据使用情况自动管理存储优先级"})]})]})})]})]})]}),e.jsxs("div",{className:"settings-card relative overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-violet-200/50 shadow-xl",onMouseEnter:K,onMouseLeave:J,children:[e.jsx("div",{className:"absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-violet-200/20 to-purple-200/20 rounded-full -translate-y-18 translate-x-18"}),e.jsx("div",{className:"absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-violet-200/20 rounded-full translate-y-16 -translate-x-16"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx("span",{className:"text-xl",children:"⚙️"})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent",children:"设置选项"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),R(!0)},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"✏️"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"档案管理"}),e.jsx("div",{className:"text-xs text-slate-600",children:"编辑或重新设置档案"})]})]}),e.jsx("span",{className:"text-slate-400",children:"›"})]})}),d&&e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),B()},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"📱"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"安装本地应用"}),e.jsx("div",{className:"text-xs text-slate-600",children:"安装到桌面，离线使用"})]})]}),e.jsx("span",{className:"text-slate-400",children:"›"})]})}),l&&e.jsx("div",{className:"w-full bg-green-50 border border-green-200 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"✅"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-green-800",children:"应用已安装"}),e.jsx("div",{className:"text-xs text-green-600",children:"正在以PWA模式运行"})]})]})}),e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),Q()},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-orange-400 to-red-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"📤"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"数据导出"}),e.jsx("div",{className:"text-xs text-slate-600",children:"导出健康数据"})]})]}),e.jsx("span",{className:"text-slate-400",children:"›"})]})}),e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),h()},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-teal-400 to-cyan-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"📥"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"数据导入"}),e.jsx("div",{className:"text-xs text-slate-600",children:"导入健康数据"})]})]}),e.jsx("span",{className:"text-slate-400",children:"›"})]})}),e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),C(!0)},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"🤖"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"AI模型管理"}),e.jsx("div",{className:"text-xs text-slate-600",children:N.length>0?`已配置 ${N.length} 个模型`:"配置AI模型"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[w()&&e.jsx("span",{className:`text-xs px-2 py-1 rounded-full flex items-center gap-1 ${w()?.supportsVision===!0?"bg-emerald-100 text-emerald-800":"bg-yellow-100 text-yellow-800"}`,children:w()?.supportsVision===!0?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),"支持图像"]}):e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"❓"}),"无法判断"]})}),e.jsx("span",{className:"text-slate-400",children:"›"})]})]})}),e.jsx("button",{className:"action-button w-full bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50 hover:bg-white/90 transition-all duration-200",onClick:A=>{$(A),alert("关于我们功能即将推出")},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-slate-400 to-gray-400 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-sm",children:"ℹ️"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold text-sm text-slate-800",children:"关于我们"}),e.jsx("div",{className:"text-xs text-slate-600",children:"应用信息与帮助"})]})]}),e.jsx("span",{className:"text-slate-400",children:"›"})]})})]})]})]}),e.jsx("div",{className:"h-24 pb-safe"})]})})}),e.jsx("input",{ref:m,type:"file",accept:".json",onChange:S,className:"hidden"}),e.jsx(Ee,{showAddButton:!0,onRecognitionComplete:A=>{console.log("Profile收到识别结果:",A),A&&A.foods&&A.foods.length>0&&(A.foods.forEach(I=>{if(!I.name||!I.calories||I.calories<=0){console.warn("跳过无效的食物记录:",I);return}const O={name:I.name,weight:I.weight||100,calories:I.calories,mealType:A.meal,recordedAt:new Date,nutrition:I.nutrition||{protein:Math.round(I.calories*.15/4),fat:Math.round(I.calories*.25/9),carbs:Math.round(I.calories*.6/4),fiber:Math.round(I.calories*.05/4),sugar:Math.round(I.calories*.1/4),sodium:Math.round(I.calories*.5)},aiRecognition:{confidence:I.confidence||.85,method:A.method,originalInput:A.content||""},isEdited:!1};i(new Date,O)}),console.log("食物记录已保存到营养存储"))}}),e.jsx(vt,{isOpen:j,onClose:()=>C(!1)}),E&&e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsx("div",{className:"bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-slate-800",children:"档案管理"}),e.jsx("button",{onClick:()=>R(!1),className:"w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors",children:e.jsx("span",{className:"text-gray-600",children:"✕"})})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{R(!1),n("/setup?mode=edit")},className:"w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-xl p-4 hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-xl flex-shrink-0",children:"✏️"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold",children:"编辑档案"}),e.jsx("div",{className:"text-sm opacity-90",children:"修改个人信息和目标"})]})]})}),e.jsx("button",{onClick:()=>{confirm("确定要重新设置档案吗？这将清除所有个人信息，需要重新填写。")&&(s(),R(!1),n("/setup"))},className:"w-full bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl p-4 hover:from-orange-600 hover:to-red-600 transition-all duration-200 shadow-lg",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-xl",children:"🔄"}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-bold",children:"重新设置档案"}),e.jsx("div",{className:"text-sm opacity-90",children:"清除所有信息，重新开始"})]})]})})]}),e.jsx("div",{className:"mt-6 pt-4 border-t border-gray-200",children:e.jsx("button",{onClick:()=>R(!1),className:"w-full bg-gray-100 text-gray-700 rounded-xl p-3 hover:bg-gray-200 transition-colors",children:"取消"})})]})})})]})},mr=({isOpen:n,onClose:t,foodRecord:s,onSave:r,currentDate:a,initialEditMode:i=!1})=>{const[l,o]=b.useState(i),[x,c]=b.useState(null),[d,p]=b.useState(a||new Date);if(se.useEffect(()=>{if(s){c({...s});const u=new Date(s.recordedAt);p(a||u),o(i)}},[s,a,i]),!n||!s||!x)return null;const N=()=>{o(!0)},w=()=>{if(x){const u={...x,isEdited:!0,editedAt:new Date},P=new Date(s.recordedAt),j=d.toDateString()!==P.toDateString();r(u,j?d:void 0),o(!1),t()}},f=(u,P)=>{if(x)if(u.startsWith("nutrition.")){const j=u.split(".")[1];c({...x,nutrition:{...x.nutrition,[j]:typeof P=="string"?parseFloat(P)||0:P}})}else c({...x,[u]:typeof P=="string"&&(u==="calories"||u==="weight")?parseFloat(P)||0:P})},m=u=>(typeof u=="string"?new Date(u):u).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),y=u=>u>=.8?"text-green-600":u>=.6?"text-yellow-600":"text-red-600",g=u=>u>=.8?"高准确度":u>=.6?"中等准确度":"低准确度";return e.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50 p-4 pb-20",style:{backgroundColor:"rgba(0, 0, 0, 0.4)",backdropFilter:"blur(8px)"},onClick:()=>{l||t()},children:e.jsxs("div",{className:"relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md xl:max-w-2xl 2xl:max-w-3xl max-h-[80vh] sm:max-h-[75vh] xl:max-h-[85vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)"},onClick:u=>u.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between p-6 xl:p-8 pb-4 xl:pb-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl xl:text-2xl font-bold text-gray-900 mb-1",children:"营养详情"}),e.jsx("p",{className:"text-sm xl:text-base text-gray-500",children:"查看和编辑食物营养信息"})]}),e.jsx("button",{onClick:t,className:"btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500",tabIndex:0,children:e.jsx("svg",{className:"w-4 h-4 xl:w-5 xl:h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6 xl:p-8 pt-2 space-y-6 xl:space-y-8 pb-16 xl:pb-20",children:[e.jsxs("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 xl:p-6 border border-blue-100 shadow-sm",children:[e.jsxs("h3",{className:"text-lg xl:text-xl font-semibold text-gray-800 mb-4 xl:mb-6 flex items-center",children:[e.jsx("div",{className:"w-2 h-2 xl:w-3 xl:h-3 bg-blue-500 rounded-full mr-3"}),"基础信息"]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"食物名称"}),l?e.jsx("input",{type:"text",value:x.name,onChange:u=>f("name",u.target.value),className:"input input-bordered input-sm w-full"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:s.name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"记录日期"}),l?e.jsx("input",{type:"date",value:q(d,"yyyy-MM-dd"),onChange:u=>p(new Date(u.target.value)),className:"input input-bordered input-sm w-full"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:q(d,"yyyy年MM月dd日")})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"重量 (g)"}),l?e.jsx("input",{type:"number",value:x.weight,onChange:u=>f("weight",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(Ye,{value:s.weight})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"卡路里"}),l?e.jsx("input",{type:"number",value:x.calories,onChange:u=>f("calories",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(he,{value:s.calories})})]})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-xl p-4 border border-green-100 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-3"}),"营养成分"]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"蛋白质 (g)"}),l?e.jsx("input",{type:"number",value:x.nutrition.protein,onChange:u=>f("nutrition.protein",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(te,{value:s.nutrition.protein,unit:"g"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"脂肪 (g)"}),l?e.jsx("input",{type:"number",value:x.nutrition.fat,onChange:u=>f("nutrition.fat",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(te,{value:s.nutrition.fat,unit:"g"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"碳水化合物 (g)"}),l?e.jsx("input",{type:"number",value:x.nutrition.carbs,onChange:u=>f("nutrition.carbs",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(te,{value:s.nutrition.carbs,unit:"g"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"纤维 (g)"}),l?e.jsx("input",{type:"number",value:x.nutrition.fiber,onChange:u=>f("nutrition.fiber",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.jsx(te,{value:s.nutrition.fiber,unit:"g"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"糖分 (g)"}),l?e.jsx("input",{type:"number",value:x.nutrition.sugar,onChange:u=>f("nutrition.sugar",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"0.1"}):e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[s.nutrition.sugar,"g"]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"钠 (mg)"}),l?e.jsx("input",{type:"number",value:x.nutrition.sodium||0,onChange:u=>f("nutrition.sodium",u.target.value),className:"input input-bordered input-sm w-full",min:"0",step:"1"}):e.jsxs("div",{className:"text-sm font-medium text-gray-900",children:[s.nutrition.sodium||0,"mg"]})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50 rounded-xl p-4 border border-gray-100 shadow-sm",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-4 flex items-center",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-500 rounded-full mr-3"}),"记录信息"]}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"添加时间："}),e.jsx("span",{className:"font-medium",children:m(s.recordedAt)})]}),s.aiRecognition&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"识别方法："}),e.jsx("span",{className:"font-medium",children:s.aiRecognition.method==="text"?"文本识别":"视觉识别"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"准确度："}),e.jsxs("span",{className:`font-medium ${y(s.aiRecognition.confidence)}`,children:[Math.round(s.aiRecognition.confidence*100),"% (",g(s.aiRecognition.confidence),")"]})]})]}),s.isEdited&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"最后编辑："}),e.jsx("span",{className:"font-medium",children:m(s.updatedAt)})]}),s.isEdited&&e.jsx("div",{className:"text-xs text-orange-600 bg-orange-50 rounded p-1 mt-1",children:"⚠️ 此记录已被手动编辑"})]})]}),e.jsx("div",{className:"h-4"})]}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100",children:l?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:w,className:"btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"保存"]}),e.jsx("button",{onClick:t,className:"btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]",children:"关闭"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:N,className:"btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"编辑"]}),e.jsx("button",{onClick:t,className:"btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]",children:"关闭"})]})})]})})},xr=(n=6e4)=>{const[t,s]=b.useState(()=>{const a=_e();return{snackInfo:a,timeUntilNext:a.timeUntilNext,isSnackTime:a.isSnackTime,currentPeriodName:a.currentPeriod?.name||"",nextPeriodName:a.nextPeriod?.name||""}}),r=b.useCallback(()=>{const a=_e();s({snackInfo:a,timeUntilNext:a.timeUntilNext,isSnackTime:a.isSnackTime,currentPeriodName:a.currentPeriod?.name||"",nextPeriodName:a.nextPeriod?.name||""})},[]);return b.useEffect(()=>{r();const a=setInterval(r,n);return()=>{clearInterval(a)}},[r,n]),t},ur=n=>n.isSnackTime?`现在是${n.currentPeriod?.name}时间，适合补充营养`:`${n.nextPeriod?.name}将在${n.timeUntilNext}开始`,Oe=n=>n?{bgColor:"bg-green-50",borderColor:"border-green-200",textColor:"text-green-700",icon:"✅"}:{bgColor:"bg-amber-50",borderColor:"border-amber-200",textColor:"text-amber-700",icon:"⏰"},hr={breakfast:{label:"早餐",icon:"🌅",timeRange:"0:00-10:00",color:"text-orange-600 bg-orange-50 border-orange-200"},lunch:{label:"午餐",icon:"☀️",timeRange:"11:00-14:00",color:"text-green-600 bg-green-50 border-green-200"},dinner:{label:"晚餐",icon:"🌙",timeRange:"17:00-21:00",color:"text-blue-600 bg-blue-50 border-blue-200"},snack:{label:"加餐",icon:"🍎",timeRange:"随时",color:"text-emerald-600 bg-emerald-50 border-emerald-200"}},gr=n=>{const t=xe.find(a=>a.id===n.replace("-snack","")),r=_e().currentPeriod?.id===t?.id;return t?{label:t.name,icon:t.emoji,timeRange:t.timeRange,color:"text-emerald-600 bg-emerald-50 border-emerald-200",description:r?`现在是${t.name}时间`:t.description}:{label:"加餐",icon:"🍎",timeRange:"随时",color:"text-emerald-600 bg-emerald-50 border-emerald-200",description:""}},pr=()=>{const n=Se(),{profile:t}=ge(),{getDailyFoodRecords:s,addDetailedFoodRecord:r,updateFoodRecord:a,deleteFoodRecord:i}=Le(),[l,o]=b.useState(new Date),[x,c]=b.useState(!1),[d,p]=b.useState(!1),[N,w]=b.useState(null),[f,m]=b.useState(!1),y=xr(),g=s(l);b.useEffect(()=>{},[g]);const u=L=>{window.confirm("确定要删除这条食物记录吗？")&&i(l,L)},P=L=>{w(L),m(!0),p(!0)},j=()=>{const L=["breakfast","lunch","dinner","morning-snack","afternoon-snack","evening-snack"];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"dropdown dropdown-bottom dropdown-center w-full mx-auto",children:[e.jsx("div",{className:"flex items-center justify-center bg-white rounded-xl p-3 xl:p-4 shadow-sm border border-slate-200 mb-1 hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:D=>{D.stopPropagation(),o(new Date(l.getTime()-24*60*60*1e3))},className:"btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 hover:bg-slate-100 transition-colors",tabIndex:-1,children:"←"}),e.jsxs("div",{tabIndex:0,role:"button",className:"text-center min-w-[100px] cursor-pointer hover:bg-slate-50 rounded-lg p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyDown:D=>{(D.key==="Enter"||D.key===" ")&&D.preventDefault()},children:[e.jsx("div",{className:"font-bold text-slate-800 text-base xl:text-lg",children:q(l,"MM月dd日")}),e.jsx("div",{className:"text-xs text-slate-500 -mt-1",children:q(l,"EEEE")})]}),e.jsx("button",{onClick:D=>{D.stopPropagation(),o(new Date(l.getTime()+24*60*60*1e3))},className:"btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 hover:bg-slate-100 transition-colors",disabled:q(l,"yyyy-MM-dd")>=q(new Date,"yyyy-MM-dd"),tabIndex:-1,children:"→"})]})}),e.jsxs("div",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-4 shadow-lg border border-base-300",children:[e.jsx("div",{className:"text-center mb-4",children:e.jsx("h3",{className:"font-semibold text-base-content",children:"选择日期"})}),e.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-4",children:[e.jsx("button",{onClick:()=>{o(new Date),document.activeElement?.blur()},className:"btn btn-outline btn-sm",children:"今天"}),e.jsx("button",{onClick:()=>{const D=new Date;D.setDate(D.getDate()-1),o(D),document.activeElement?.blur()},className:"btn btn-outline btn-sm",children:"昨天"}),e.jsx("button",{onClick:()=>{const D=new Date;D.setDate(D.getDate()-3),o(D),document.activeElement?.blur()},className:"btn btn-outline btn-sm",children:"3天前"})]}),e.jsxs("div",{className:"form-control mb-4",children:[e.jsx("label",{className:"label",children:e.jsx("span",{className:"label-text",children:"选择具体日期"})}),e.jsx("input",{type:"date",value:q(l,"yyyy-MM-dd"),onChange:D=>{D.target.value&&(o(new Date(D.target.value)),document.activeElement?.blur())},max:q(new Date,"yyyy-MM-dd"),className:"input input-bordered w-full xl:input-lg bg-white"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>{document.activeElement?.blur()},className:"btn btn-ghost btn-sm",children:"关闭"})})]})]}),e.jsx("div",{className:"space-y-4",children:L.map((D,B)=>{let z=[],T;if(typeof D=="string"&&D.includes("-snack")){const h=g?.mealRecords.snack||[],S=xe.find($=>$.id===D.replace("-snack",""));z=h.filter($=>{if(!S)return!1;const J=new Date($.recordedAt).getHours();return J>=S.startHour&&J<S.endHour}),T=gr(D)}else z=g?.mealRecords[D]||[],T=hr[D];const Q=z.reduce((h,S)=>h+S.calories,0);return e.jsxs("div",{className:`food-timeline-item ${B===0?"mt-4":""}`,children:[e.jsxs("div",{className:"flex items-center gap-3 xl:gap-4 mb-3 xl:mb-4",children:[e.jsx("div",{className:`w-10 h-10 xl:w-12 xl:h-12 rounded-full flex items-center justify-center ${T.color} border-2 flex-shrink-0`,children:e.jsx("span",{className:"text-lg xl:text-xl",children:T.icon})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"font-bold text-slate-800 text-lg xl:text-xl",children:T.label}),e.jsx("div",{className:`badge ${T.color} font-medium text-xs xl:text-sm`,children:T.timeRange})]}),e.jsxs("div",{className:"text-sm xl:text-base text-slate-500 font-medium",children:[Q," kcal"]})]}),D==="snack"&&e.jsx("div",{className:`text-xs mt-1 p-2 rounded-lg ${Oe(y.isSnackTime).bgColor} ${Oe(y.isSnackTime).borderColor} border`,children:e.jsxs("span",{className:Oe(y.isSnackTime).textColor,children:[Oe(y.isSnackTime).icon," ",ur(y.snackInfo)]})})]})]}),e.jsx("div",{className:"mb-6",children:z.length===0?e.jsxs("div",{className:"bg-white rounded-xl p-6 xl:p-8 text-center text-slate-400 shadow-sm border border-slate-200 hover:shadow-md transition-shadow",children:[e.jsx("div",{className:"text-3xl xl:text-4xl mb-2",children:"🍽️"}),e.jsx("div",{className:"text-sm xl:text-base",children:"暂无记录"})]}):e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-3 xl:gap-4",children:z.map(h=>e.jsx("div",{"data-record-id":h.id,className:"bg-white rounded-lg border border-slate-200 p-4 xl:p-5 shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[280px] xl:min-w-[320px]",onClick:()=>C(h),tabIndex:0,onKeyDown:S=>{(S.key==="Enter"||S.key===" ")&&(S.preventDefault(),C(h))},children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[" ",e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"font-medium text-slate-800 text-base xl:text-lg truncate flex-1",title:h.name,children:[" ",h.name]}),h.aiRecognition&&e.jsxs(e.Fragment,{children:[h.aiRecognition.dataSource==="text_analysis"&&e.jsx("span",{className:"text-xs xl:text-sm bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full",children:"📝 文本识别"}),h.aiRecognition.dataSource==="visual_estimation"&&e.jsx("span",{className:"text-xs xl:text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full",children:"👁️ 视觉识别"}),h.aiRecognition.dataSource==="nutrition_label"&&e.jsx("span",{className:"text-xs xl:text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full",children:"📝 营养标签"}),!h.aiRecognition.dataSource&&h.aiRecognition.method==="text"&&e.jsx("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full",children:"📝 文本识别"}),!h.aiRecognition.dataSource&&h.aiRecognition.method==="image"&&e.jsx("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full",children:"👁️ 视觉识别"})]})]}),e.jsxs("div",{className:"text-sm xl:text-base text-slate-500 mt-1",children:[e.jsx(Ye,{value:h.weight})," • ",e.jsx(he,{value:h.calories}),h.isEdited&&e.jsx("span",{className:"ml-2 badge badge-xs xl:badge-sm badge-warning",children:"已编辑"})]}),e.jsxs("div",{className:"text-xs xl:text-sm text-slate-400 mt-1",children:["添加于 ",new Date(h.recordedAt).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),h.isEdited&&e.jsx("span",{className:"ml-2",children:"• 已编辑"})]})]}),e.jsxs("div",{className:"flex gap-1 xl:gap-2 ml-2",children:[e.jsx("button",{onClick:S=>{S.stopPropagation(),P(h)},className:"btn btn-ghost btn-sm xl:btn-md w-8 h-8 xl:w-10 xl:h-10 min-h-8 xl:min-h-10 p-0 hover:bg-blue-50 hover:scale-105 transition-all duration-200",title:"编辑",tabIndex:0,children:"✏️"}),e.jsx("button",{onClick:S=>{S.stopPropagation(),u(h.id)},className:"btn btn-ghost btn-sm xl:btn-md w-8 h-8 xl:w-10 xl:h-10 min-h-8 xl:min-h-10 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 hover:scale-105 transition-all duration-200",title:"删除",tabIndex:0,children:"🗑️"})]})]})},h.id))})})]},D)})})]})};if(!t)return e.jsx("div",{className:"min-h-screen bg-base-200 flex items-center justify-center p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"👤"}),e.jsx("h2",{className:"text-xl font-bold mb-2",children:"未找到用户档案"}),e.jsx("p",{className:"text-slate-600 mb-4",children:"请先完成个人档案设置"}),e.jsx("button",{className:"btn btn-primary",onClick:()=>n("/setup"),children:"立即设置"})]})});const C=L=>{w(L),m(!1),p(!0)},E=(L,D)=>{if(D&&D.toDateString()!==l.toDateString()){i(l,L.id);const B={...L,recordedAt:D,isEdited:!0};r(D,B),o(D)}else a(l,L.id,{name:L.name,weight:L.weight,calories:L.calories,nutrition:L.nutrition,isEdited:!0});p(!1),w(null)},R=L=>{if(console.log("识别完成:",L),L&&L.foods&&L.foods.length>0){const D=L.selectedDate||l;L.foods.forEach(B=>{if(!B||typeof B!="object"){console.warn("无效的食物数据，跳过:",B);return}const z=Number(B.calories)||0,T=Number(B.weight)||100;if(z<=0||isNaN(z)){console.warn("无效的卡路里数据，跳过食物记录:",{food:B,calories:z});return}const Q={name:B.name||"未知食物",weight:T,calories:z,mealType:L.meal,recordedAt:D,nutrition:B.nutrition||{protein:Math.round(z*.15/4),fat:Math.round(z*.25/9),carbs:Math.round(z*.6/4),fiber:Math.round(z*.05/4),sugar:Math.round(z*.1/4),sodium:Math.round(z*.5)},aiRecognition:{confidence:Number(B.confidence)||.85,method:L.method,originalInput:L.content||"",dataSource:B.dataSource==="nutrition_facts"?"nutrition_label":B.dataSource||(L.method==="text"?"text_analysis":"visual_estimation")},isEdited:!1};r(D,Q)}),console.log("食物记录已保存到营养存储")}};return e.jsxs("div",{className:"min-h-screen bg-base-200 pb-16",children:[e.jsx("div",{className:"max-w-lg xl:max-w-5xl 2xl:max-w-7xl mx-auto px-4 xl:px-6 pt-4 pb-4",children:j()}),e.jsx(Ee,{showAddButton:!0,onRecognitionComplete:R,currentDate:l,isAIProcessing:x,onProcessingStateChange:c}),e.jsx(mr,{isOpen:d,onClose:()=>{p(!1),w(null),m(!1)},foodRecord:N,onSave:E,currentDate:l,initialEditMode:f})]})},Ie=({children:n})=>{const{isProfileComplete:t}=ge();return t?e.jsx(e.Fragment,{children:n}):e.jsx(Fe,{to:"/setup",replace:!0})},br=({children:n})=>{const{isProfileComplete:t}=ge(),[s]=xt(),r=s.get("mode")==="edit";return t&&!r?e.jsx(Fe,{to:"/dashboard",replace:!0}):e.jsx(e.Fragment,{children:n})},fr=()=>{const{isProfileComplete:n}=ge();return e.jsx(Fe,{to:n?"/dashboard":"/setup",replace:!0})},vr=Bt([{path:"/",element:e.jsx(fr,{})},{path:"/setup",element:e.jsx(br,{children:e.jsx(ye,{children:e.jsx(_s,{})})})},{path:"/dashboard",element:e.jsx(Ie,{children:e.jsx(ye,{children:e.jsx(Zs,{})})})},{path:"/calendar",element:e.jsx(Ie,{children:e.jsx(ye,{children:e.jsx(lr,{})})})},{path:"/profile",element:e.jsx(Ie,{children:e.jsx(ye,{children:e.jsx(cr,{})})})},{path:"/food-record",element:e.jsx(Ie,{children:e.jsx(ye,{children:e.jsx(pr,{})})})},{path:"/debug-nav",element:e.jsx(Ie,{children:e.jsx(ye,{children:e.jsx(or,{})})})},{path:"*",element:e.jsx(Fe,{to:"/",replace:!0})}]),jr=()=>{const[n,t]=b.useState(null),[s,r]=b.useState(!1),[a,i]=b.useState(!1),[l,o]=b.useState(!1),[x,c]=b.useState(!1),d="pwa-install-dismissed";b.useEffect(()=>{const m=it.get(d,!1);if(m){c(!0);return}const y=()=>window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone===!0?(i(!0),!0):!1,g=y(),u=E=>{console.log("beforeinstallprompt事件触发"),E.preventDefault(),t(E),o(!0),!g&&!m&&setTimeout(()=>{r(!0)},5e3)},P=()=>{console.log("PWA已安装"),i(!0),r(!1),o(!1),t(null)},j=()=>{y()};window.addEventListener("beforeinstallprompt",u),window.addEventListener("appinstalled",P);const C=window.matchMedia("(display-mode: standalone)");return C.addEventListener("change",j),()=>{window.removeEventListener("beforeinstallprompt",u),window.removeEventListener("appinstalled",P),C.removeEventListener("change",j)}},[]);const p=async()=>{if(!n){N();return}try{console.log("触发PWA安装提示"),await n.prompt();const{outcome:m}=await n.userChoice;console.log("用户选择结果:",m),m==="accepted"?(console.log("用户接受了安装提示"),i(!0)):console.log("用户拒绝了安装提示"),t(null),o(!1),r(!1)}catch(m){console.error("安装提示失败:",m),N()}},N=()=>{const m=/iPad|iPhone|iPod/.test(navigator.userAgent),y=/Android/.test(navigator.userAgent);let g=`请手动安装应用：

`;m?g+=`1. 点击浏览器底部的分享按钮
2. 选择"添加到主屏幕"
3. 点击"添加"`:y?g+=`1. 点击浏览器菜单（三个点）
2. 选择"添加到主屏幕"或"安装应用"
3. 点击"安装"`:g+=`1. 点击地址栏右侧的安装图标
2. 或在浏览器菜单中选择"安装应用"`,alert(g)},w=()=>{r(!1),setTimeout(()=>{!a&&l&&!x&&r(!0)},60*60*1e3)},f=()=>{r(!1),c(!0),it.set(d,!0)};return a||x||!s||!n&&!l?null:e.jsx("div",{className:"fixed bottom-20 left-4 right-4 z-50 md:left-auto md:right-4 md:w-80",children:e.jsx("div",{className:"bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-200/50 p-4 animate-slide-up",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg",children:e.jsx("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-bold text-gray-900 text-sm mb-1",children:"安装KCal Tracker"}),e.jsxs("p",{className:"text-xs text-gray-600 mb-3",children:["添加到主屏幕，享受原生应用体验",n?"":"（需要手动操作）"]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:p,className:"flex-1 bg-emerald-600 text-white text-xs py-3 px-2 rounded-xl hover:bg-emerald-700 transition-colors font-medium shadow-lg min-h-[44px] flex items-center justify-center whitespace-nowrap",children:n?"立即安装":"安装指导"}),e.jsx("button",{onClick:w,className:"flex-1 bg-gray-100 text-gray-700 text-xs py-3 px-2 rounded-xl hover:bg-gray-200 transition-colors min-h-[44px] flex items-center justify-center whitespace-nowrap",children:"稍后提醒"})]})]}),e.jsx("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 transition-colors p-2 min-h-[44px] min-w-[44px] flex items-center justify-center",title:"不再提示",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})})},yr=()=>{const[n,t]=b.useState(navigator.onLine),[s,r]=b.useState(!1),[a,i]=b.useState(!1);return b.useEffect(()=>{const l=()=>{t(!0),r(!1),i(!0),setTimeout(()=>{i(!1)},3e3)},o=()=>{t(!1),i(!1),r(!0)};return window.addEventListener("online",l),window.addEventListener("offline",o),navigator.onLine||r(!0),()=>{window.removeEventListener("online",l),window.removeEventListener("offline",o)}},[]),s?e.jsx("div",{className:"fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-80",children:e.jsx("div",{className:"bg-amber-50 border border-amber-200 rounded-xl p-3 shadow-lg animate-slide-down",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-amber-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-amber-800 text-sm",children:"离线模式"}),e.jsx("p",{className:"text-xs text-amber-700",children:"网络连接已断开，部分功能可能受限"})]}),e.jsx("button",{onClick:()=>r(!1),className:"text-amber-600 hover:text-amber-800 transition-colors p-1",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})}):a?e.jsx("div",{className:"fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-80",children:e.jsx("div",{className:"bg-green-50 border border-green-200 rounded-xl p-3 shadow-lg animate-slide-down",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0",children:e.jsx("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-green-800 text-sm",children:"已重新连接"}),e.jsx("p",{className:"text-xs text-green-700",children:"网络连接已恢复，所有功能正常"})]})]})})}):null};function wr(){return b.useEffect(()=>((async()=>{try{await Ge.initialize(),console.log("应用存储初始化完成")}catch(t){console.error("应用存储初始化失败:",t)}})(),()=>{Ge.close()}),[]),e.jsxs(Us,{children:[e.jsx(zt,{router:vr}),e.jsx(jr,{}),e.jsx(yr,{})]})}Wt.createRoot(document.getElementById("root")).render(e.jsx(b.StrictMode,{children:e.jsx(wr,{})}));
