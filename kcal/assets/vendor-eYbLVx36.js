import{c as b0}from"./utils-DXIKCEYF.js";import{C as dy,B as S0,D as E0,L as R0}from"./charts-DTefc40_.js";function T0(n,r){for(var o=0;o<r.length;o++){const c=r[o];if(typeof c!="string"&&!Array.isArray(c)){for(const f in c)if(f!=="default"&&!(f in n)){const d=Object.getOwnPropertyDescriptor(c,f);d&&Object.defineProperty(n,f,d.get?d:{enumerable:!0,get:()=>c[f]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}function x0(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Pc={exports:{}},ku={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Em;function w0(){if(Em)return ku;Em=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(c,f,d){var y=null;if(d!==void 0&&(y=""+d),f.key!==void 0&&(y=""+f.key),"key"in f){d={};for(var g in f)g!=="key"&&(d[g]=f[g])}else d=f;return f=d.ref,{$$typeof:n,type:c,key:y,ref:f!==void 0?f:null,props:d}}return ku.Fragment=r,ku.jsx=o,ku.jsxs=o,ku}var Rm;function M0(){return Rm||(Rm=1,Pc.exports=w0()),Pc.exports}var zS=M0(),Ic={exports:{}},Re={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tm;function A0(){if(Tm)return Re;Tm=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),y=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),A=Symbol.iterator;function x(E){return E===null||typeof E!="object"?null:(E=A&&E[A]||E["@@iterator"],typeof E=="function"?E:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,j={};function k(E,q,W){this.props=E,this.context=q,this.refs=j,this.updater=W||C}k.prototype.isReactComponent={},k.prototype.setState=function(E,q){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,q,"setState")},k.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function B(){}B.prototype=k.prototype;function ne(E,q,W){this.props=E,this.context=q,this.refs=j,this.updater=W||C}var K=ne.prototype=new B;K.constructor=ne,w(K,k.prototype),K.isPureReactComponent=!0;var ue=Array.isArray,P={H:null,A:null,T:null,S:null,V:null},U=Object.prototype.hasOwnProperty;function oe(E,q,W,V,I,ve){return W=ve.ref,{$$typeof:n,type:E,key:q,ref:W!==void 0?W:null,props:ve}}function F(E,q){return oe(E.type,q,void 0,void 0,void 0,E.props)}function ce(E){return typeof E=="object"&&E!==null&&E.$$typeof===n}function Me(E){var q={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(W){return q[W]})}var Qe=/\/+/g;function Ye(E,q){return typeof E=="object"&&E!==null&&E.key!=null?Me(""+E.key):q.toString(36)}function Te(){}function xe(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(Te,Te):(E.status="pending",E.then(function(q){E.status==="pending"&&(E.status="fulfilled",E.value=q)},function(q){E.status==="pending"&&(E.status="rejected",E.reason=q)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function we(E,q,W,V,I){var ve=typeof E;(ve==="undefined"||ve==="boolean")&&(E=null);var se=!1;if(E===null)se=!0;else switch(ve){case"bigint":case"string":case"number":se=!0;break;case"object":switch(E.$$typeof){case n:case r:se=!0;break;case p:return se=E._init,we(se(E._payload),q,W,V,I)}}if(se)return I=I(E),se=V===""?"."+Ye(E,0):V,ue(I)?(W="",se!=null&&(W=se.replace(Qe,"$&/")+"/"),we(I,q,W,"",function(it){return it})):I!=null&&(ce(I)&&(I=F(I,W+(I.key==null||E&&E.key===I.key?"":(""+I.key).replace(Qe,"$&/")+"/")+se)),q.push(I)),1;se=0;var Oe=V===""?".":V+":";if(ue(E))for(var He=0;He<E.length;He++)V=E[He],ve=Oe+Ye(V,He),se+=we(V,q,W,ve,I);else if(He=x(E),typeof He=="function")for(E=He.call(E),He=0;!(V=E.next()).done;)V=V.value,ve=Oe+Ye(V,He++),se+=we(V,q,W,ve,I);else if(ve==="object"){if(typeof E.then=="function")return we(xe(E),q,W,V,I);throw q=String(E),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return se}function D(E,q,W){if(E==null)return E;var V=[],I=0;return we(E,V,"","",function(ve){return q.call(W,ve,I++)}),V}function J(E){if(E._status===-1){var q=E._result;q=q(),q.then(function(W){(E._status===0||E._status===-1)&&(E._status=1,E._result=W)},function(W){(E._status===0||E._status===-1)&&(E._status=2,E._result=W)}),E._status===-1&&(E._status=0,E._result=q)}if(E._status===1)return E._result.default;throw E._result}var X=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function Se(){}return Re.Children={map:D,forEach:function(E,q,W){D(E,function(){q.apply(this,arguments)},W)},count:function(E){var q=0;return D(E,function(){q++}),q},toArray:function(E){return D(E,function(q){return q})||[]},only:function(E){if(!ce(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},Re.Component=k,Re.Fragment=o,Re.Profiler=f,Re.PureComponent=ne,Re.StrictMode=c,Re.Suspense=h,Re.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=P,Re.__COMPILER_RUNTIME={__proto__:null,c:function(E){return P.H.useMemoCache(E)}},Re.cache=function(E){return function(){return E.apply(null,arguments)}},Re.cloneElement=function(E,q,W){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var V=w({},E.props),I=E.key,ve=void 0;if(q!=null)for(se in q.ref!==void 0&&(ve=void 0),q.key!==void 0&&(I=""+q.key),q)!U.call(q,se)||se==="key"||se==="__self"||se==="__source"||se==="ref"&&q.ref===void 0||(V[se]=q[se]);var se=arguments.length-2;if(se===1)V.children=W;else if(1<se){for(var Oe=Array(se),He=0;He<se;He++)Oe[He]=arguments[He+2];V.children=Oe}return oe(E.type,I,void 0,void 0,ve,V)},Re.createContext=function(E){return E={$$typeof:y,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:d,_context:E},E},Re.createElement=function(E,q,W){var V,I={},ve=null;if(q!=null)for(V in q.key!==void 0&&(ve=""+q.key),q)U.call(q,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(I[V]=q[V]);var se=arguments.length-2;if(se===1)I.children=W;else if(1<se){for(var Oe=Array(se),He=0;He<se;He++)Oe[He]=arguments[He+2];I.children=Oe}if(E&&E.defaultProps)for(V in se=E.defaultProps,se)I[V]===void 0&&(I[V]=se[V]);return oe(E,ve,void 0,void 0,null,I)},Re.createRef=function(){return{current:null}},Re.forwardRef=function(E){return{$$typeof:g,render:E}},Re.isValidElement=ce,Re.lazy=function(E){return{$$typeof:p,_payload:{_status:-1,_result:E},_init:J}},Re.memo=function(E,q){return{$$typeof:v,type:E,compare:q===void 0?null:q}},Re.startTransition=function(E){var q=P.T,W={};P.T=W;try{var V=E(),I=P.S;I!==null&&I(W,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(Se,X)}catch(ve){X(ve)}finally{P.T=q}},Re.unstable_useCacheRefresh=function(){return P.H.useCacheRefresh()},Re.use=function(E){return P.H.use(E)},Re.useActionState=function(E,q,W){return P.H.useActionState(E,q,W)},Re.useCallback=function(E,q){return P.H.useCallback(E,q)},Re.useContext=function(E){return P.H.useContext(E)},Re.useDebugValue=function(){},Re.useDeferredValue=function(E,q){return P.H.useDeferredValue(E,q)},Re.useEffect=function(E,q,W){var V=P.H;if(typeof W=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(E,q)},Re.useId=function(){return P.H.useId()},Re.useImperativeHandle=function(E,q,W){return P.H.useImperativeHandle(E,q,W)},Re.useInsertionEffect=function(E,q){return P.H.useInsertionEffect(E,q)},Re.useLayoutEffect=function(E,q){return P.H.useLayoutEffect(E,q)},Re.useMemo=function(E,q){return P.H.useMemo(E,q)},Re.useOptimistic=function(E,q){return P.H.useOptimistic(E,q)},Re.useReducer=function(E,q,W){return P.H.useReducer(E,q,W)},Re.useRef=function(E){return P.H.useRef(E)},Re.useState=function(E){return P.H.useState(E)},Re.useSyncExternalStore=function(E,q,W){return P.H.useSyncExternalStore(E,q,W)},Re.useTransition=function(){return P.H.useTransition()},Re.version="19.1.0",Re}var xm;function Af(){return xm||(xm=1,Ic.exports=A0()),Ic.exports}var S=Af();const pt=x0(S),wm=T0({__proto__:null,default:pt},[S]);var ef={exports:{}},Vu={},tf={exports:{}},lf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mm;function z0(){return Mm||(Mm=1,function(n){function r(D,J){var X=D.length;D.push(J);e:for(;0<X;){var Se=X-1>>>1,E=D[Se];if(0<f(E,J))D[Se]=J,D[X]=E,X=Se;else break e}}function o(D){return D.length===0?null:D[0]}function c(D){if(D.length===0)return null;var J=D[0],X=D.pop();if(X!==J){D[0]=X;e:for(var Se=0,E=D.length,q=E>>>1;Se<q;){var W=2*(Se+1)-1,V=D[W],I=W+1,ve=D[I];if(0>f(V,X))I<E&&0>f(ve,V)?(D[Se]=ve,D[I]=X,Se=I):(D[Se]=V,D[W]=X,Se=W);else if(I<E&&0>f(ve,X))D[Se]=ve,D[I]=X,Se=I;else break e}}return J}function f(D,J){var X=D.sortIndex-J.sortIndex;return X!==0?X:D.id-J.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;n.unstable_now=function(){return d.now()}}else{var y=Date,g=y.now();n.unstable_now=function(){return y.now()-g}}var h=[],v=[],p=1,A=null,x=3,C=!1,w=!1,j=!1,k=!1,B=typeof setTimeout=="function"?setTimeout:null,ne=typeof clearTimeout=="function"?clearTimeout:null,K=typeof setImmediate<"u"?setImmediate:null;function ue(D){for(var J=o(v);J!==null;){if(J.callback===null)c(v);else if(J.startTime<=D)c(v),J.sortIndex=J.expirationTime,r(h,J);else break;J=o(v)}}function P(D){if(j=!1,ue(D),!w)if(o(h)!==null)w=!0,U||(U=!0,Ye());else{var J=o(v);J!==null&&we(P,J.startTime-D)}}var U=!1,oe=-1,F=5,ce=-1;function Me(){return k?!0:!(n.unstable_now()-ce<F)}function Qe(){if(k=!1,U){var D=n.unstable_now();ce=D;var J=!0;try{e:{w=!1,j&&(j=!1,ne(oe),oe=-1),C=!0;var X=x;try{t:{for(ue(D),A=o(h);A!==null&&!(A.expirationTime>D&&Me());){var Se=A.callback;if(typeof Se=="function"){A.callback=null,x=A.priorityLevel;var E=Se(A.expirationTime<=D);if(D=n.unstable_now(),typeof E=="function"){A.callback=E,ue(D),J=!0;break t}A===o(h)&&c(h),ue(D)}else c(h);A=o(h)}if(A!==null)J=!0;else{var q=o(v);q!==null&&we(P,q.startTime-D),J=!1}}break e}finally{A=null,x=X,C=!1}J=void 0}}finally{J?Ye():U=!1}}}var Ye;if(typeof K=="function")Ye=function(){K(Qe)};else if(typeof MessageChannel<"u"){var Te=new MessageChannel,xe=Te.port2;Te.port1.onmessage=Qe,Ye=function(){xe.postMessage(null)}}else Ye=function(){B(Qe,0)};function we(D,J){oe=B(function(){D(n.unstable_now())},J)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(D){D.callback=null},n.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<D?Math.floor(1e3/D):5},n.unstable_getCurrentPriorityLevel=function(){return x},n.unstable_next=function(D){switch(x){case 1:case 2:case 3:var J=3;break;default:J=x}var X=x;x=J;try{return D()}finally{x=X}},n.unstable_requestPaint=function(){k=!0},n.unstable_runWithPriority=function(D,J){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var X=x;x=D;try{return J()}finally{x=X}},n.unstable_scheduleCallback=function(D,J,X){var Se=n.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?Se+X:Se):X=Se,D){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=X+E,D={id:p++,callback:J,priorityLevel:D,startTime:X,expirationTime:E,sortIndex:-1},X>Se?(D.sortIndex=X,r(v,D),o(h)===null&&D===o(v)&&(j?(ne(oe),oe=-1):j=!0,we(P,X-Se))):(D.sortIndex=E,r(h,D),w||C||(w=!0,U||(U=!0,Ye()))),D},n.unstable_shouldYield=Me,n.unstable_wrapCallback=function(D){var J=x;return function(){var X=x;x=J;try{return D.apply(this,arguments)}finally{x=X}}}}(lf)),lf}var Am;function O0(){return Am||(Am=1,tf.exports=z0()),tf.exports}var af={exports:{}},wt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zm;function D0(){if(zm)return wt;zm=1;var n=Af();function r(h){var v="https://react.dev/errors/"+h;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var p=2;p<arguments.length;p++)v+="&args[]="+encodeURIComponent(arguments[p])}return"Minified React error #"+h+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var c={d:{f:o,r:function(){throw Error(r(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},f=Symbol.for("react.portal");function d(h,v,p){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:A==null?null:""+A,children:h,containerInfo:v,implementation:p}}var y=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(h,v){if(h==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return wt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,wt.createPortal=function(h,v){var p=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(r(299));return d(h,v,null,p)},wt.flushSync=function(h){var v=y.T,p=c.p;try{if(y.T=null,c.p=2,h)return h()}finally{y.T=v,c.p=p,c.d.f()}},wt.preconnect=function(h,v){typeof h=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,c.d.C(h,v))},wt.prefetchDNS=function(h){typeof h=="string"&&c.d.D(h)},wt.preinit=function(h,v){if(typeof h=="string"&&v&&typeof v.as=="string"){var p=v.as,A=g(p,v.crossOrigin),x=typeof v.integrity=="string"?v.integrity:void 0,C=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;p==="style"?c.d.S(h,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:A,integrity:x,fetchPriority:C}):p==="script"&&c.d.X(h,{crossOrigin:A,integrity:x,fetchPriority:C,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},wt.preinitModule=function(h,v){if(typeof h=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var p=g(v.as,v.crossOrigin);c.d.M(h,{crossOrigin:p,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&c.d.M(h)},wt.preload=function(h,v){if(typeof h=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var p=v.as,A=g(p,v.crossOrigin);c.d.L(h,p,{crossOrigin:A,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},wt.preloadModule=function(h,v){if(typeof h=="string")if(v){var p=g(v.as,v.crossOrigin);c.d.m(h,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:p,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else c.d.m(h)},wt.requestFormReset=function(h){c.d.r(h)},wt.unstable_batchedUpdates=function(h,v){return h(v)},wt.useFormState=function(h,v,p){return y.H.useFormState(h,v,p)},wt.useFormStatus=function(){return y.H.useHostTransitionStatus()},wt.version="19.1.0",wt}var Om;function hy(){if(Om)return af.exports;Om=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),af.exports=D0(),af.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dm;function _0(){if(Dm)return Vu;Dm=1;var n=O0(),r=Af(),o=hy();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function y(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(d(e)!==e)throw Error(c(188))}function h(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(c(188));return t!==e?null:e}for(var l=e,a=t;;){var u=l.return;if(u===null)break;var i=u.alternate;if(i===null){if(a=u.return,a!==null){l=a;continue}break}if(u.child===i.child){for(i=u.child;i;){if(i===l)return g(u),e;if(i===a)return g(u),t;i=i.sibling}throw Error(c(188))}if(l.return!==a.return)l=u,a=i;else{for(var s=!1,m=u.child;m;){if(m===l){s=!0,l=u,a=i;break}if(m===a){s=!0,a=u,l=i;break}m=m.sibling}if(!s){for(m=i.child;m;){if(m===l){s=!0,l=i,a=u;break}if(m===a){s=!0,a=i,l=u;break}m=m.sibling}if(!s)throw Error(c(189))}}if(l.alternate!==a)throw Error(c(190))}if(l.tag!==3)throw Error(c(188));return l.stateNode.current===l?e:t}function v(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=v(e),t!==null)return t;e=e.sibling}return null}var p=Object.assign,A=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),B=Symbol.for("react.provider"),ne=Symbol.for("react.consumer"),K=Symbol.for("react.context"),ue=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),U=Symbol.for("react.suspense_list"),oe=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),ce=Symbol.for("react.activity"),Me=Symbol.for("react.memo_cache_sentinel"),Qe=Symbol.iterator;function Ye(e){return e===null||typeof e!="object"?null:(e=Qe&&e[Qe]||e["@@iterator"],typeof e=="function"?e:null)}var Te=Symbol.for("react.client.reference");function xe(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Te?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case w:return"Fragment";case k:return"Profiler";case j:return"StrictMode";case P:return"Suspense";case U:return"SuspenseList";case ce:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case C:return"Portal";case K:return(e.displayName||"Context")+".Provider";case ne:return(e._context.displayName||"Context")+".Consumer";case ue:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case oe:return t=e.displayName||null,t!==null?t:xe(e.type)||"Memo";case F:t=e._payload,e=e._init;try{return xe(e(t))}catch{}}return null}var we=Array.isArray,D=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,X={pending:!1,data:null,method:null,action:null},Se=[],E=-1;function q(e){return{current:e}}function W(e){0>E||(e.current=Se[E],Se[E]=null,E--)}function V(e,t){E++,Se[E]=e.current,e.current=t}var I=q(null),ve=q(null),se=q(null),Oe=q(null);function He(e,t){switch(V(se,t),V(ve,e),V(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?$h(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=$h(t),e=Fh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}W(I),V(I,e)}function it(){W(I),W(ve),W(se)}function Pe(e){e.memoizedState!==null&&V(Oe,e);var t=I.current,l=Fh(t,e.type);t!==l&&(V(ve,e),V(I,l))}function Mt(e){ve.current===e&&(W(I),W(ve)),Oe.current===e&&(W(Oe),ju._currentValue=X)}var ul=Object.prototype.hasOwnProperty,Qn=n.unstable_scheduleCallback,rl=n.unstable_cancelCallback,Vi=n.unstable_shouldYield,Qi=n.unstable_requestPaint,Ht=n.unstable_now,Zi=n.unstable_getCurrentPriorityLevel,nr=n.unstable_ImmediatePriority,ur=n.unstable_UserBlockingPriority,tn=n.unstable_NormalPriority,Tl=n.unstable_LowPriority,Vl=n.unstable_IdlePriority,rr=n.log,Zn=n.unstable_setDisableYieldValue,Ot=null,tt=null;function il(e){if(typeof rr=="function"&&Zn(e),tt&&typeof tt.setStrictMode=="function")try{tt.setStrictMode(Ot,e)}catch{}}var bt=Math.clz32?Math.clz32:ir,Ki=Math.log,ml=Math.LN2;function ir(e){return e>>>=0,e===0?32:31-(Ki(e)/ml|0)|0}var Ma=256,Aa=4194304;function xl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function za(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var u=0,i=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var m=a&134217727;return m!==0?(a=m&~i,a!==0?u=xl(a):(s&=m,s!==0?u=xl(s):l||(l=m&~e,l!==0&&(u=xl(l))))):(m=a&~i,m!==0?u=xl(m):s!==0?u=xl(s):l||(l=a&~e,l!==0&&(u=xl(l)))),u===0?0:t!==0&&t!==u&&(t&i)===0&&(i=u&-u,l=t&-t,i>=l||i===32&&(l&4194048)!==0)?t:u}function yl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function or(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ln(){var e=Ma;return Ma<<=1,(Ma&4194048)===0&&(Ma=256),e}function cr(){var e=Aa;return Aa<<=1,(Aa&62914560)===0&&(Aa=4194304),e}function an(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Oa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function fr(e,t,l,a,u,i){var s=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var m=e.entanglements,b=e.expirationTimes,_=e.hiddenUpdates;for(l=s&~l;0<l;){var Y=31-bt(l),Q=1<<Y;m[Y]=0,b[Y]=-1;var N=_[Y];if(N!==null)for(_[Y]=null,Y=0;Y<N.length;Y++){var L=N[Y];L!==null&&(L.lane&=-536870913)}l&=~Q}a!==0&&Da(e,a,0),i!==0&&u===0&&e.tag!==0&&(e.suspendedLanes|=i&~(s&~t))}function Da(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-bt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function _a(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-bt(l),u=1<<a;u&t|e[a]&t&&(e[a]|=t),l&=~u}}function Kn(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Jn(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function R(){var e=J.p;return e!==0?e:(e=window.event,e===void 0?32:ym(e.type))}function z(e,t){var l=J.p;try{return J.p=e,t()}finally{J.p=l}}var H=Math.random().toString(36).slice(2),Z="__reactFiber$"+H,$="__reactProps$"+H,re="__reactContainer$"+H,he="__reactEvents$"+H,ee="__reactListeners$"+H,de="__reactHandles$"+H,ie="__reactResources$"+H,ge="__reactMarker$"+H;function pe(e){delete e[Z],delete e[$],delete e[he],delete e[ee],delete e[de]}function Ee(e){var t=e[Z];if(t)return t;for(var l=e.parentNode;l;){if(t=l[re]||l[Z]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=em(e);e!==null;){if(l=e[Z])return l;e=em(e)}return t}e=l,l=e.parentNode}return null}function je(e){if(e=e[Z]||e[re]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function We(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function lt(e){var t=e[ie];return t||(t=e[ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function qe(e){e[ge]=!0}var Le=new Set,Ql={};function At(e,t){Zt(e,t),Zt(e+"Capture",t)}function Zt(e,t){for(Ql[e]=t,e=0;e<t.length;e++)Le.add(t[e])}var Dt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Zl={},Kl={};function $n(e){return ul.call(Kl,e)?!0:ul.call(Zl,e)?!1:Dt.test(e)?Kl[e]=!0:(Zl[e]=!0,!1)}function Kt(e,t,l){if($n(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function wl(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function De(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var Tt,ol;function cl(e){if(Tt===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);Tt=t&&t[1]||"",ol=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Tt+e+ol}var Ca=!1;function Ie(e,t){if(!e||Ca)return"";Ca=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var Q=function(){throw Error()};if(Object.defineProperty(Q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Q,[])}catch(L){var N=L}Reflect.construct(e,[],Q)}else{try{Q.call()}catch(L){N=L}e.call(Q.prototype)}}else{try{throw Error()}catch(L){N=L}(Q=e())&&typeof Q.catch=="function"&&Q.catch(function(){})}}catch(L){if(L&&N&&typeof L.stack=="string")return[L.stack,N.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var u=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");u&&u.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),s=i[0],m=i[1];if(s&&m){var b=s.split(`
`),_=m.split(`
`);for(u=a=0;a<b.length&&!b[a].includes("DetermineComponentFrameRoot");)a++;for(;u<_.length&&!_[u].includes("DetermineComponentFrameRoot");)u++;if(a===b.length||u===_.length)for(a=b.length-1,u=_.length-1;1<=a&&0<=u&&b[a]!==_[u];)u--;for(;1<=a&&0<=u;a--,u--)if(b[a]!==_[u]){if(a!==1||u!==1)do if(a--,u--,0>u||b[a]!==_[u]){var Y=`
`+b[a].replace(" at new "," at ");return e.displayName&&Y.includes("<anonymous>")&&(Y=Y.replace("<anonymous>",e.displayName)),Y}while(1<=a&&0<=u);break}}}finally{Ca=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?cl(l):""}function Jl(e){switch(e.tag){case 26:case 27:case 5:return cl(e.type);case 16:return cl("Lazy");case 13:return cl("Suspense");case 19:return cl("SuspenseList");case 0:case 15:return Ie(e.type,!1);case 11:return Ie(e.type.render,!1);case 1:return Ie(e.type,!0);case 31:return cl("Activity");default:return""}}function Fn(e){try{var t="";do t+=Jl(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function _t(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Vf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function mv(e){var t=Vf(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var u=l.get,i=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(s){a=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(s){a=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function sr(e){e._valueTracker||(e._valueTracker=mv(e))}function Qf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Vf(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function dr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var yv=/[\n"\\]/g;function Jt(e){return e.replace(yv,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ji(e,t,l,a,u,i,s,m){e.name="",s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"?e.type=s:e.removeAttribute("type"),t!=null?s==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+_t(t)):e.value!==""+_t(t)&&(e.value=""+_t(t)):s!=="submit"&&s!=="reset"||e.removeAttribute("value"),t!=null?$i(e,s,_t(t)):l!=null?$i(e,s,_t(l)):a!=null&&e.removeAttribute("value"),u==null&&i!=null&&(e.defaultChecked=!!i),u!=null&&(e.checked=u&&typeof u!="function"&&typeof u!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+_t(m):e.removeAttribute("name")}function Zf(e,t,l,a,u,i,s,m){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;l=l!=null?""+_t(l):"",t=t!=null?""+_t(t):l,m||t===e.value||(e.value=t),e.defaultValue=t}a=a??u,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=m?e.checked:!!a,e.defaultChecked=!!a,s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.name=s)}function $i(e,t,l){t==="number"&&dr(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function nn(e,t,l,a){if(e=e.options,t){t={};for(var u=0;u<l.length;u++)t["$"+l[u]]=!0;for(l=0;l<e.length;l++)u=t.hasOwnProperty("$"+e[l].value),e[l].selected!==u&&(e[l].selected=u),u&&a&&(e[l].defaultSelected=!0)}else{for(l=""+_t(l),t=null,u=0;u<e.length;u++){if(e[u].value===l){e[u].selected=!0,a&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function Kf(e,t,l){if(t!=null&&(t=""+_t(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+_t(l):""}function Jf(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(c(92));if(we(a)){if(1<a.length)throw Error(c(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=_t(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function un(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var vv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function $f(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||vv.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function Ff(e,t,l){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var u in t)a=t[u],t.hasOwnProperty(u)&&l[u]!==a&&$f(e,u,a)}else for(var i in t)t.hasOwnProperty(i)&&$f(e,i,t[i])}function Fi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),pv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function hr(e){return pv.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Wi=null;function Pi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var rn=null,on=null;function Wf(e){var t=je(e);if(t&&(e=t.stateNode)){var l=e[$]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ji(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Jt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var u=a[$]||null;if(!u)throw Error(c(90));Ji(a,u.value,u.defaultValue,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&Qf(a)}break e;case"textarea":Kf(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&nn(e,!!l.multiple,t,!1)}}}var Ii=!1;function Pf(e,t,l){if(Ii)return e(t,l);Ii=!0;try{var a=e(t);return a}finally{if(Ii=!1,(rn!==null||on!==null)&&(Pr(),rn&&(t=rn,e=on,on=rn=null,Wf(t),e)))for(t=0;t<e.length;t++)Wf(e[t])}}function Wn(e,t){var l=e.stateNode;if(l===null)return null;var a=l[$]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(c(231,t,typeof l));return l}var Ml=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),eo=!1;if(Ml)try{var Pn={};Object.defineProperty(Pn,"passive",{get:function(){eo=!0}}),window.addEventListener("test",Pn,Pn),window.removeEventListener("test",Pn,Pn)}catch{eo=!1}var $l=null,to=null,mr=null;function If(){if(mr)return mr;var e,t=to,l=t.length,a,u="value"in $l?$l.value:$l.textContent,i=u.length;for(e=0;e<l&&t[e]===u[e];e++);var s=l-e;for(a=1;a<=s&&t[l-a]===u[i-a];a++);return mr=u.slice(e,1<a?1-a:void 0)}function yr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function vr(){return!0}function es(){return!1}function Ct(e){function t(l,a,u,i,s){this._reactName=l,this._targetInst=u,this.type=a,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(l=e[m],this[m]=l?l(i):i[m]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?vr:es,this.isPropagationStopped=es,this}return p(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=vr)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=vr)},persist:function(){},isPersistent:vr}),t}var Ua={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},gr=Ct(Ua),In=p({},Ua,{view:0,detail:0}),bv=Ct(In),lo,ao,eu,pr=p({},In,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:uo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==eu&&(eu&&e.type==="mousemove"?(lo=e.screenX-eu.screenX,ao=e.screenY-eu.screenY):ao=lo=0,eu=e),lo)},movementY:function(e){return"movementY"in e?e.movementY:ao}}),ts=Ct(pr),Sv=p({},pr,{dataTransfer:0}),Ev=Ct(Sv),Rv=p({},In,{relatedTarget:0}),no=Ct(Rv),Tv=p({},Ua,{animationName:0,elapsedTime:0,pseudoElement:0}),xv=Ct(Tv),wv=p({},Ua,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Mv=Ct(wv),Av=p({},Ua,{data:0}),ls=Ct(Av),zv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ov={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function _v(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dv[e])?!!t[e]:!1}function uo(){return _v}var Cv=p({},In,{key:function(e){if(e.key){var t=zv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=yr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ov[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:uo,charCode:function(e){return e.type==="keypress"?yr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?yr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Uv=Ct(Cv),Nv=p({},pr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),as=Ct(Nv),Lv=p({},In,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:uo}),Hv=Ct(Lv),Bv=p({},Ua,{propertyName:0,elapsedTime:0,pseudoElement:0}),jv=Ct(Bv),Yv=p({},pr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),qv=Ct(Yv),Gv=p({},Ua,{newState:0,oldState:0}),Xv=Ct(Gv),kv=[9,13,27,32],ro=Ml&&"CompositionEvent"in window,tu=null;Ml&&"documentMode"in document&&(tu=document.documentMode);var Vv=Ml&&"TextEvent"in window&&!tu,ns=Ml&&(!ro||tu&&8<tu&&11>=tu),us=" ",rs=!1;function is(e,t){switch(e){case"keyup":return kv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function os(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var cn=!1;function Qv(e,t){switch(e){case"compositionend":return os(t);case"keypress":return t.which!==32?null:(rs=!0,us);case"textInput":return e=t.data,e===us&&rs?null:e;default:return null}}function Zv(e,t){if(cn)return e==="compositionend"||!ro&&is(e,t)?(e=If(),mr=to=$l=null,cn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ns&&t.locale!=="ko"?null:t.data;default:return null}}var Kv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function cs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Kv[e.type]:t==="textarea"}function fs(e,t,l,a){rn?on?on.push(a):on=[a]:rn=a,t=ni(t,"onChange"),0<t.length&&(l=new gr("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var lu=null,au=null;function Jv(e){Vh(e,0)}function br(e){var t=We(e);if(Qf(t))return e}function ss(e,t){if(e==="change")return t}var ds=!1;if(Ml){var io;if(Ml){var oo="oninput"in document;if(!oo){var hs=document.createElement("div");hs.setAttribute("oninput","return;"),oo=typeof hs.oninput=="function"}io=oo}else io=!1;ds=io&&(!document.documentMode||9<document.documentMode)}function ms(){lu&&(lu.detachEvent("onpropertychange",ys),au=lu=null)}function ys(e){if(e.propertyName==="value"&&br(au)){var t=[];fs(t,au,e,Pi(e)),Pf(Jv,t)}}function $v(e,t,l){e==="focusin"?(ms(),lu=t,au=l,lu.attachEvent("onpropertychange",ys)):e==="focusout"&&ms()}function Fv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return br(au)}function Wv(e,t){if(e==="click")return br(t)}function Pv(e,t){if(e==="input"||e==="change")return br(t)}function Iv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Bt=typeof Object.is=="function"?Object.is:Iv;function nu(e,t){if(Bt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var u=l[a];if(!ul.call(t,u)||!Bt(e[u],t[u]))return!1}return!0}function vs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function gs(e,t){var l=vs(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=vs(l)}}function ps(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ps(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function bs(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=dr(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=dr(e.document)}return t}function co(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var eg=Ml&&"documentMode"in document&&11>=document.documentMode,fn=null,fo=null,uu=null,so=!1;function Ss(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;so||fn==null||fn!==dr(a)||(a=fn,"selectionStart"in a&&co(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),uu&&nu(uu,a)||(uu=a,a=ni(fo,"onSelect"),0<a.length&&(t=new gr("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=fn)))}function Na(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var sn={animationend:Na("Animation","AnimationEnd"),animationiteration:Na("Animation","AnimationIteration"),animationstart:Na("Animation","AnimationStart"),transitionrun:Na("Transition","TransitionRun"),transitionstart:Na("Transition","TransitionStart"),transitioncancel:Na("Transition","TransitionCancel"),transitionend:Na("Transition","TransitionEnd")},ho={},Es={};Ml&&(Es=document.createElement("div").style,"AnimationEvent"in window||(delete sn.animationend.animation,delete sn.animationiteration.animation,delete sn.animationstart.animation),"TransitionEvent"in window||delete sn.transitionend.transition);function La(e){if(ho[e])return ho[e];if(!sn[e])return e;var t=sn[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Es)return ho[e]=t[l];return e}var Rs=La("animationend"),Ts=La("animationiteration"),xs=La("animationstart"),tg=La("transitionrun"),lg=La("transitionstart"),ag=La("transitioncancel"),ws=La("transitionend"),Ms=new Map,mo="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");mo.push("scrollEnd");function fl(e,t){Ms.set(e,t),At(t,[e])}var As=new WeakMap;function $t(e,t){if(typeof e=="object"&&e!==null){var l=As.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Fn(t)},As.set(e,t),t)}return{value:e,source:t,stack:Fn(t)}}var Ft=[],dn=0,yo=0;function Sr(){for(var e=dn,t=yo=dn=0;t<e;){var l=Ft[t];Ft[t++]=null;var a=Ft[t];Ft[t++]=null;var u=Ft[t];Ft[t++]=null;var i=Ft[t];if(Ft[t++]=null,a!==null&&u!==null){var s=a.pending;s===null?u.next=u:(u.next=s.next,s.next=u),a.pending=u}i!==0&&zs(l,u,i)}}function Er(e,t,l,a){Ft[dn++]=e,Ft[dn++]=t,Ft[dn++]=l,Ft[dn++]=a,yo|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function vo(e,t,l,a){return Er(e,t,l,a),Rr(e)}function hn(e,t){return Er(e,null,null,t),Rr(e)}function zs(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var u=!1,i=e.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(u=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,u&&t!==null&&(u=31-bt(l),e=i.hiddenUpdates,a=e[u],a===null?e[u]=[t]:a.push(t),t.lane=l|536870912),i):null}function Rr(e){if(50<Du)throw Du=0,Rc=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var mn={};function ng(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function jt(e,t,l,a){return new ng(e,t,l,a)}function go(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Al(e,t){var l=e.alternate;return l===null?(l=jt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Os(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Tr(e,t,l,a,u,i){var s=0;if(a=e,typeof e=="function")go(e)&&(s=1);else if(typeof e=="string")s=r0(e,l,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ce:return e=jt(31,l,t,u),e.elementType=ce,e.lanes=i,e;case w:return Ha(l.children,u,i,t);case j:s=8,u|=24;break;case k:return e=jt(12,l,t,u|2),e.elementType=k,e.lanes=i,e;case P:return e=jt(13,l,t,u),e.elementType=P,e.lanes=i,e;case U:return e=jt(19,l,t,u),e.elementType=U,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case B:case K:s=10;break e;case ne:s=9;break e;case ue:s=11;break e;case oe:s=14;break e;case F:s=16,a=null;break e}s=29,l=Error(c(130,e===null?"null":typeof e,"")),a=null}return t=jt(s,l,t,u),t.elementType=e,t.type=a,t.lanes=i,t}function Ha(e,t,l,a){return e=jt(7,e,a,t),e.lanes=l,e}function po(e,t,l){return e=jt(6,e,null,t),e.lanes=l,e}function bo(e,t,l){return t=jt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var yn=[],vn=0,xr=null,wr=0,Wt=[],Pt=0,Ba=null,zl=1,Ol="";function ja(e,t){yn[vn++]=wr,yn[vn++]=xr,xr=e,wr=t}function Ds(e,t,l){Wt[Pt++]=zl,Wt[Pt++]=Ol,Wt[Pt++]=Ba,Ba=e;var a=zl;e=Ol;var u=32-bt(a)-1;a&=~(1<<u),l+=1;var i=32-bt(t)+u;if(30<i){var s=u-u%5;i=(a&(1<<s)-1).toString(32),a>>=s,u-=s,zl=1<<32-bt(t)+u|l<<u|a,Ol=i+e}else zl=1<<i|l<<u|a,Ol=e}function So(e){e.return!==null&&(ja(e,1),Ds(e,1,0))}function Eo(e){for(;e===xr;)xr=yn[--vn],yn[vn]=null,wr=yn[--vn],yn[vn]=null;for(;e===Ba;)Ba=Wt[--Pt],Wt[Pt]=null,Ol=Wt[--Pt],Wt[Pt]=null,zl=Wt[--Pt],Wt[Pt]=null}var zt=null,nt=null,Xe=!1,Ya=null,vl=!1,Ro=Error(c(519));function qa(e){var t=Error(c(418,""));throw ou($t(t,e)),Ro}function _s(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Z]=e,t[$]=a,l){case"dialog":Ne("cancel",t),Ne("close",t);break;case"iframe":case"object":case"embed":Ne("load",t);break;case"video":case"audio":for(l=0;l<Cu.length;l++)Ne(Cu[l],t);break;case"source":Ne("error",t);break;case"img":case"image":case"link":Ne("error",t),Ne("load",t);break;case"details":Ne("toggle",t);break;case"input":Ne("invalid",t),Zf(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),sr(t);break;case"select":Ne("invalid",t);break;case"textarea":Ne("invalid",t),Jf(t,a.value,a.defaultValue,a.children),sr(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Jh(t.textContent,l)?(a.popover!=null&&(Ne("beforetoggle",t),Ne("toggle",t)),a.onScroll!=null&&Ne("scroll",t),a.onScrollEnd!=null&&Ne("scrollend",t),a.onClick!=null&&(t.onclick=ui),t=!0):t=!1,t||qa(e)}function Cs(e){for(zt=e.return;zt;)switch(zt.tag){case 5:case 13:vl=!1;return;case 27:case 3:vl=!0;return;default:zt=zt.return}}function ru(e){if(e!==zt)return!1;if(!Xe)return Cs(e),Xe=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||jc(e.type,e.memoizedProps)),l=!l),l&&nt&&qa(e),Cs(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){nt=dl(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}nt=null}}else t===27?(t=nt,sa(e.type)?(e=Xc,Xc=null,nt=e):nt=t):nt=zt?dl(e.stateNode.nextSibling):null;return!0}function iu(){nt=zt=null,Xe=!1}function Us(){var e=Ya;return e!==null&&(Lt===null?Lt=e:Lt.push.apply(Lt,e),Ya=null),e}function ou(e){Ya===null?Ya=[e]:Ya.push(e)}var To=q(null),Ga=null,Dl=null;function Fl(e,t,l){V(To,t._currentValue),t._currentValue=l}function _l(e){e._currentValue=To.current,W(To)}function xo(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function wo(e,t,l,a){var u=e.child;for(u!==null&&(u.return=e);u!==null;){var i=u.dependencies;if(i!==null){var s=u.child;i=i.firstContext;e:for(;i!==null;){var m=i;i=u;for(var b=0;b<t.length;b++)if(m.context===t[b]){i.lanes|=l,m=i.alternate,m!==null&&(m.lanes|=l),xo(i.return,l,e),a||(s=null);break e}i=m.next}}else if(u.tag===18){if(s=u.return,s===null)throw Error(c(341));s.lanes|=l,i=s.alternate,i!==null&&(i.lanes|=l),xo(s,l,e),s=null}else s=u.child;if(s!==null)s.return=u;else for(s=u;s!==null;){if(s===e){s=null;break}if(u=s.sibling,u!==null){u.return=s.return,s=u;break}s=s.return}u=s}}function cu(e,t,l,a){e=null;for(var u=t,i=!1;u!==null;){if(!i){if((u.flags&524288)!==0)i=!0;else if((u.flags&262144)!==0)break}if(u.tag===10){var s=u.alternate;if(s===null)throw Error(c(387));if(s=s.memoizedProps,s!==null){var m=u.type;Bt(u.pendingProps.value,s.value)||(e!==null?e.push(m):e=[m])}}else if(u===Oe.current){if(s=u.alternate,s===null)throw Error(c(387));s.memoizedState.memoizedState!==u.memoizedState.memoizedState&&(e!==null?e.push(ju):e=[ju])}u=u.return}e!==null&&wo(t,e,l,a),t.flags|=262144}function Mr(e){for(e=e.firstContext;e!==null;){if(!Bt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Xa(e){Ga=e,Dl=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function xt(e){return Ns(Ga,e)}function Ar(e,t){return Ga===null&&Xa(e),Ns(e,t)}function Ns(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Dl===null){if(e===null)throw Error(c(308));Dl=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Dl=Dl.next=t;return l}var ug=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},rg=n.unstable_scheduleCallback,ig=n.unstable_NormalPriority,dt={$$typeof:K,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Mo(){return{controller:new ug,data:new Map,refCount:0}}function fu(e){e.refCount--,e.refCount===0&&rg(ig,function(){e.controller.abort()})}var su=null,Ao=0,gn=0,pn=null;function og(e,t){if(su===null){var l=su=[];Ao=0,gn=Oc(),pn={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Ao++,t.then(Ls,Ls),t}function Ls(){if(--Ao===0&&su!==null){pn!==null&&(pn.status="fulfilled");var e=su;su=null,gn=0,pn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function cg(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(u){l.push(u)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var u=0;u<l.length;u++)(0,l[u])(t)},function(u){for(a.status="rejected",a.reason=u,u=0;u<l.length;u++)(0,l[u])(void 0)}),a}var Hs=D.S;D.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&og(e,t),Hs!==null&&Hs(e,t)};var ka=q(null);function zo(){var e=ka.current;return e!==null?e:Fe.pooledCache}function zr(e,t){t===null?V(ka,ka.current):V(ka,t.pool)}function Bs(){var e=zo();return e===null?null:{parent:dt._currentValue,pool:e}}var du=Error(c(460)),js=Error(c(474)),Or=Error(c(542)),Oo={then:function(){}};function Ys(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Dr(){}function qs(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Dr,Dr),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xs(e),e;default:if(typeof t.status=="string")t.then(Dr,Dr);else{if(e=Fe,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var u=t;u.status="fulfilled",u.value=a}},function(a){if(t.status==="pending"){var u=t;u.status="rejected",u.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Xs(e),e}throw hu=t,du}}var hu=null;function Gs(){if(hu===null)throw Error(c(459));var e=hu;return hu=null,e}function Xs(e){if(e===du||e===Or)throw Error(c(483))}var Wl=!1;function Do(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function _o(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Pl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Il(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(ke&2)!==0){var u=a.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),a.pending=t,t=Rr(e),zs(e,null,l),t}return Er(e,a,t,l),Rr(e)}function mu(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,_a(e,l)}}function Co(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var u=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var s={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?u=i=s:i=i.next=s,l=l.next}while(l!==null);i===null?u=i=t:i=i.next=t}else u=i=t;l={baseState:a.baseState,firstBaseUpdate:u,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Uo=!1;function yu(){if(Uo){var e=pn;if(e!==null)throw e}}function vu(e,t,l,a){Uo=!1;var u=e.updateQueue;Wl=!1;var i=u.firstBaseUpdate,s=u.lastBaseUpdate,m=u.shared.pending;if(m!==null){u.shared.pending=null;var b=m,_=b.next;b.next=null,s===null?i=_:s.next=_,s=b;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,m=Y.lastBaseUpdate,m!==s&&(m===null?Y.firstBaseUpdate=_:m.next=_,Y.lastBaseUpdate=b))}if(i!==null){var Q=u.baseState;s=0,Y=_=b=null,m=i;do{var N=m.lane&-536870913,L=N!==m.lane;if(L?(Be&N)===N:(a&N)===N){N!==0&&N===gn&&(Uo=!0),Y!==null&&(Y=Y.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var be=e,me=m;N=t;var Je=l;switch(me.tag){case 1:if(be=me.payload,typeof be=="function"){Q=be.call(Je,Q,N);break e}Q=be;break e;case 3:be.flags=be.flags&-65537|128;case 0:if(be=me.payload,N=typeof be=="function"?be.call(Je,Q,N):be,N==null)break e;Q=p({},Q,N);break e;case 2:Wl=!0}}N=m.callback,N!==null&&(e.flags|=64,L&&(e.flags|=8192),L=u.callbacks,L===null?u.callbacks=[N]:L.push(N))}else L={lane:N,tag:m.tag,payload:m.payload,callback:m.callback,next:null},Y===null?(_=Y=L,b=Q):Y=Y.next=L,s|=N;if(m=m.next,m===null){if(m=u.shared.pending,m===null)break;L=m,m=L.next,L.next=null,u.lastBaseUpdate=L,u.shared.pending=null}}while(!0);Y===null&&(b=Q),u.baseState=b,u.firstBaseUpdate=_,u.lastBaseUpdate=Y,i===null&&(u.shared.lanes=0),ia|=s,e.lanes=s,e.memoizedState=Q}}function ks(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function Vs(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)ks(l[e],t)}var bn=q(null),_r=q(0);function Qs(e,t){e=jl,V(_r,e),V(bn,t),jl=e|t.baseLanes}function No(){V(_r,jl),V(bn,bn.current)}function Lo(){jl=_r.current,W(bn),W(_r)}var ea=0,Ae=null,Ze=null,ft=null,Cr=!1,Sn=!1,Va=!1,Ur=0,gu=0,En=null,fg=0;function ot(){throw Error(c(321))}function Ho(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!Bt(e[l],t[l]))return!1;return!0}function Bo(e,t,l,a,u,i){return ea=i,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,D.H=e===null||e.memoizedState===null?zd:Od,Va=!1,i=l(a,u),Va=!1,Sn&&(i=Ks(t,l,a,u)),Zs(e),i}function Zs(e){D.H=Yr;var t=Ze!==null&&Ze.next!==null;if(ea=0,ft=Ze=Ae=null,Cr=!1,gu=0,En=null,t)throw Error(c(300));e===null||yt||(e=e.dependencies,e!==null&&Mr(e)&&(yt=!0))}function Ks(e,t,l,a){Ae=e;var u=0;do{if(Sn&&(En=null),gu=0,Sn=!1,25<=u)throw Error(c(301));if(u+=1,ft=Ze=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}D.H=gg,i=t(l,a)}while(Sn);return i}function sg(){var e=D.H,t=e.useState()[0];return t=typeof t.then=="function"?pu(t):t,e=e.useState()[0],(Ze!==null?Ze.memoizedState:null)!==e&&(Ae.flags|=1024),t}function jo(){var e=Ur!==0;return Ur=0,e}function Yo(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function qo(e){if(Cr){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Cr=!1}ea=0,ft=Ze=Ae=null,Sn=!1,gu=Ur=0,En=null}function Ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ft===null?Ae.memoizedState=ft=e:ft=ft.next=e,ft}function st(){if(Ze===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=Ze.next;var t=ft===null?Ae.memoizedState:ft.next;if(t!==null)ft=t,Ze=e;else{if(e===null)throw Ae.alternate===null?Error(c(467)):Error(c(310));Ze=e,e={memoizedState:Ze.memoizedState,baseState:Ze.baseState,baseQueue:Ze.baseQueue,queue:Ze.queue,next:null},ft===null?Ae.memoizedState=ft=e:ft=ft.next=e}return ft}function Go(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function pu(e){var t=gu;return gu+=1,En===null&&(En=[]),e=qs(En,e,t),t=Ae,(ft===null?t.memoizedState:ft.next)===null&&(t=t.alternate,D.H=t===null||t.memoizedState===null?zd:Od),e}function Nr(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return pu(e);if(e.$$typeof===K)return xt(e)}throw Error(c(438,String(e)))}function Xo(e){var t=null,l=Ae.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=Ae.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(u){return u.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=Go(),Ae.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Me;return t.index++,l}function Cl(e,t){return typeof t=="function"?t(e):t}function Lr(e){var t=st();return ko(t,Ze,e)}function ko(e,t,l){var a=e.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=l;var u=e.baseQueue,i=a.pending;if(i!==null){if(u!==null){var s=u.next;u.next=i.next,i.next=s}t.baseQueue=u=i,a.pending=null}if(i=e.baseState,u===null)e.memoizedState=i;else{t=u.next;var m=s=null,b=null,_=t,Y=!1;do{var Q=_.lane&-536870913;if(Q!==_.lane?(Be&Q)===Q:(ea&Q)===Q){var N=_.revertLane;if(N===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),Q===gn&&(Y=!0);else if((ea&N)===N){_=_.next,N===gn&&(Y=!0);continue}else Q={lane:0,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},b===null?(m=b=Q,s=i):b=b.next=Q,Ae.lanes|=N,ia|=N;Q=_.action,Va&&l(i,Q),i=_.hasEagerState?_.eagerState:l(i,Q)}else N={lane:Q,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},b===null?(m=b=N,s=i):b=b.next=N,Ae.lanes|=Q,ia|=Q;_=_.next}while(_!==null&&_!==t);if(b===null?s=i:b.next=m,!Bt(i,e.memoizedState)&&(yt=!0,Y&&(l=pn,l!==null)))throw l;e.memoizedState=i,e.baseState=s,e.baseQueue=b,a.lastRenderedState=i}return u===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Vo(e){var t=st(),l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=e;var a=l.dispatch,u=l.pending,i=t.memoizedState;if(u!==null){l.pending=null;var s=u=u.next;do i=e(i,s.action),s=s.next;while(s!==u);Bt(i,t.memoizedState)||(yt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),l.lastRenderedState=i}return[i,a]}function Js(e,t,l){var a=Ae,u=st(),i=Xe;if(i){if(l===void 0)throw Error(c(407));l=l()}else l=t();var s=!Bt((Ze||u).memoizedState,l);s&&(u.memoizedState=l,yt=!0),u=u.queue;var m=Ws.bind(null,a,u,e);if(bu(2048,8,m,[e]),u.getSnapshot!==t||s||ft!==null&&ft.memoizedState.tag&1){if(a.flags|=2048,Rn(9,Hr(),Fs.bind(null,a,u,l,t),null),Fe===null)throw Error(c(349));i||(ea&124)!==0||$s(a,t,l)}return l}function $s(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=Ae.updateQueue,t===null?(t=Go(),Ae.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function Fs(e,t,l,a){t.value=l,t.getSnapshot=a,Ps(t)&&Is(e)}function Ws(e,t,l){return l(function(){Ps(t)&&Is(e)})}function Ps(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!Bt(e,l)}catch{return!0}}function Is(e){var t=hn(e,2);t!==null&&kt(t,e,2)}function Qo(e){var t=Ut();if(typeof e=="function"){var l=e;if(e=l(),Va){il(!0);try{l()}finally{il(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cl,lastRenderedState:e},t}function ed(e,t,l,a){return e.baseState=l,ko(e,Ze,typeof a=="function"?a:Cl)}function dg(e,t,l,a,u){if(jr(e))throw Error(c(485));if(e=t.action,e!==null){var i={payload:u,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(s){i.listeners.push(s)}};D.T!==null?l(!0):i.isTransition=!1,a(i),l=t.pending,l===null?(i.next=t.pending=i,td(t,i)):(i.next=l.next,t.pending=l.next=i)}}function td(e,t){var l=t.action,a=t.payload,u=e.state;if(t.isTransition){var i=D.T,s={};D.T=s;try{var m=l(u,a),b=D.S;b!==null&&b(s,m),ld(e,t,m)}catch(_){Zo(e,t,_)}finally{D.T=i}}else try{i=l(u,a),ld(e,t,i)}catch(_){Zo(e,t,_)}}function ld(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){ad(e,t,a)},function(a){return Zo(e,t,a)}):ad(e,t,l)}function ad(e,t,l){t.status="fulfilled",t.value=l,nd(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,td(e,l)))}function Zo(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,nd(t),t=t.next;while(t!==a)}e.action=null}function nd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function ud(e,t){return t}function rd(e,t){if(Xe){var l=Fe.formState;if(l!==null){e:{var a=Ae;if(Xe){if(nt){t:{for(var u=nt,i=vl;u.nodeType!==8;){if(!i){u=null;break t}if(u=dl(u.nextSibling),u===null){u=null;break t}}i=u.data,u=i==="F!"||i==="F"?u:null}if(u){nt=dl(u.nextSibling),a=u.data==="F!";break e}}qa(a)}a=!1}a&&(t=l[0])}}return l=Ut(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ud,lastRenderedState:t},l.queue=a,l=wd.bind(null,Ae,a),a.dispatch=l,a=Qo(!1),i=Wo.bind(null,Ae,!1,a.queue),a=Ut(),u={state:t,dispatch:null,action:e,pending:null},a.queue=u,l=dg.bind(null,Ae,u,i,l),u.dispatch=l,a.memoizedState=e,[t,l,!1]}function id(e){var t=st();return od(t,Ze,e)}function od(e,t,l){if(t=ko(e,t,ud)[0],e=Lr(Cl)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=pu(t)}catch(s){throw s===du?Or:s}else a=t;t=st();var u=t.queue,i=u.dispatch;return l!==t.memoizedState&&(Ae.flags|=2048,Rn(9,Hr(),hg.bind(null,u,l),null)),[a,i,e]}function hg(e,t){e.action=t}function cd(e){var t=st(),l=Ze;if(l!==null)return od(t,l,e);st(),t=t.memoizedState,l=st();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Rn(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=Ae.updateQueue,t===null&&(t=Go(),Ae.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Hr(){return{destroy:void 0,resource:void 0}}function fd(){return st().memoizedState}function Br(e,t,l,a){var u=Ut();a=a===void 0?null:a,Ae.flags|=e,u.memoizedState=Rn(1|t,Hr(),l,a)}function bu(e,t,l,a){var u=st();a=a===void 0?null:a;var i=u.memoizedState.inst;Ze!==null&&a!==null&&Ho(a,Ze.memoizedState.deps)?u.memoizedState=Rn(t,i,l,a):(Ae.flags|=e,u.memoizedState=Rn(1|t,i,l,a))}function sd(e,t){Br(8390656,8,e,t)}function dd(e,t){bu(2048,8,e,t)}function hd(e,t){return bu(4,2,e,t)}function md(e,t){return bu(4,4,e,t)}function yd(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function vd(e,t,l){l=l!=null?l.concat([e]):null,bu(4,4,yd.bind(null,t,e),l)}function Ko(){}function gd(e,t){var l=st();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Ho(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function pd(e,t){var l=st();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Ho(t,a[1]))return a[0];if(a=e(),Va){il(!0);try{e()}finally{il(!1)}}return l.memoizedState=[a,t],a}function Jo(e,t,l){return l===void 0||(ea&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Eh(),Ae.lanes|=e,ia|=e,l)}function bd(e,t,l,a){return Bt(l,t)?l:bn.current!==null?(e=Jo(e,l,a),Bt(e,t)||(yt=!0),e):(ea&42)===0?(yt=!0,e.memoizedState=l):(e=Eh(),Ae.lanes|=e,ia|=e,t)}function Sd(e,t,l,a,u){var i=J.p;J.p=i!==0&&8>i?i:8;var s=D.T,m={};D.T=m,Wo(e,!1,t,l);try{var b=u(),_=D.S;if(_!==null&&_(m,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var Y=cg(b,a);Su(e,t,Y,Xt(e))}else Su(e,t,a,Xt(e))}catch(Q){Su(e,t,{then:function(){},status:"rejected",reason:Q},Xt())}finally{J.p=i,D.T=s}}function mg(){}function $o(e,t,l,a){if(e.tag!==5)throw Error(c(476));var u=Ed(e).queue;Sd(e,u,t,X,l===null?mg:function(){return Rd(e),l(a)})}function Ed(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:X,baseState:X,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cl,lastRenderedState:X},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Cl,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Rd(e){var t=Ed(e).next.queue;Su(e,t,{},Xt())}function Fo(){return xt(ju)}function Td(){return st().memoizedState}function xd(){return st().memoizedState}function yg(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=Xt();e=Pl(l);var a=Il(t,e,l);a!==null&&(kt(a,t,l),mu(a,t,l)),t={cache:Mo()},e.payload=t;return}t=t.return}}function vg(e,t,l){var a=Xt();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},jr(e)?Md(t,l):(l=vo(e,t,l,a),l!==null&&(kt(l,e,a),Ad(l,t,a)))}function wd(e,t,l){var a=Xt();Su(e,t,l,a)}function Su(e,t,l,a){var u={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(jr(e))Md(t,u);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,m=i(s,l);if(u.hasEagerState=!0,u.eagerState=m,Bt(m,s))return Er(e,t,u,0),Fe===null&&Sr(),!1}catch{}finally{}if(l=vo(e,t,u,a),l!==null)return kt(l,e,a),Ad(l,t,a),!0}return!1}function Wo(e,t,l,a){if(a={lane:2,revertLane:Oc(),action:a,hasEagerState:!1,eagerState:null,next:null},jr(e)){if(t)throw Error(c(479))}else t=vo(e,l,a,2),t!==null&&kt(t,e,2)}function jr(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function Md(e,t){Sn=Cr=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function Ad(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,_a(e,l)}}var Yr={readContext:xt,use:Nr,useCallback:ot,useContext:ot,useEffect:ot,useImperativeHandle:ot,useLayoutEffect:ot,useInsertionEffect:ot,useMemo:ot,useReducer:ot,useRef:ot,useState:ot,useDebugValue:ot,useDeferredValue:ot,useTransition:ot,useSyncExternalStore:ot,useId:ot,useHostTransitionStatus:ot,useFormState:ot,useActionState:ot,useOptimistic:ot,useMemoCache:ot,useCacheRefresh:ot},zd={readContext:xt,use:Nr,useCallback:function(e,t){return Ut().memoizedState=[e,t===void 0?null:t],e},useContext:xt,useEffect:sd,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,Br(4194308,4,yd.bind(null,t,e),l)},useLayoutEffect:function(e,t){return Br(4194308,4,e,t)},useInsertionEffect:function(e,t){Br(4,2,e,t)},useMemo:function(e,t){var l=Ut();t=t===void 0?null:t;var a=e();if(Va){il(!0);try{e()}finally{il(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=Ut();if(l!==void 0){var u=l(t);if(Va){il(!0);try{l(t)}finally{il(!1)}}}else u=t;return a.memoizedState=a.baseState=u,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:u},a.queue=e,e=e.dispatch=vg.bind(null,Ae,e),[a.memoizedState,e]},useRef:function(e){var t=Ut();return e={current:e},t.memoizedState=e},useState:function(e){e=Qo(e);var t=e.queue,l=wd.bind(null,Ae,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:Ko,useDeferredValue:function(e,t){var l=Ut();return Jo(l,e,t)},useTransition:function(){var e=Qo(!1);return e=Sd.bind(null,Ae,e.queue,!0,!1),Ut().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=Ae,u=Ut();if(Xe){if(l===void 0)throw Error(c(407));l=l()}else{if(l=t(),Fe===null)throw Error(c(349));(Be&124)!==0||$s(a,t,l)}u.memoizedState=l;var i={value:l,getSnapshot:t};return u.queue=i,sd(Ws.bind(null,a,i,e),[e]),a.flags|=2048,Rn(9,Hr(),Fs.bind(null,a,i,l,t),null),l},useId:function(){var e=Ut(),t=Fe.identifierPrefix;if(Xe){var l=Ol,a=zl;l=(a&~(1<<32-bt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=Ur++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=fg++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Fo,useFormState:rd,useActionState:rd,useOptimistic:function(e){var t=Ut();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=Wo.bind(null,Ae,!0,l),l.dispatch=t,[e,t]},useMemoCache:Xo,useCacheRefresh:function(){return Ut().memoizedState=yg.bind(null,Ae)}},Od={readContext:xt,use:Nr,useCallback:gd,useContext:xt,useEffect:dd,useImperativeHandle:vd,useInsertionEffect:hd,useLayoutEffect:md,useMemo:pd,useReducer:Lr,useRef:fd,useState:function(){return Lr(Cl)},useDebugValue:Ko,useDeferredValue:function(e,t){var l=st();return bd(l,Ze.memoizedState,e,t)},useTransition:function(){var e=Lr(Cl)[0],t=st().memoizedState;return[typeof e=="boolean"?e:pu(e),t]},useSyncExternalStore:Js,useId:Td,useHostTransitionStatus:Fo,useFormState:id,useActionState:id,useOptimistic:function(e,t){var l=st();return ed(l,Ze,e,t)},useMemoCache:Xo,useCacheRefresh:xd},gg={readContext:xt,use:Nr,useCallback:gd,useContext:xt,useEffect:dd,useImperativeHandle:vd,useInsertionEffect:hd,useLayoutEffect:md,useMemo:pd,useReducer:Vo,useRef:fd,useState:function(){return Vo(Cl)},useDebugValue:Ko,useDeferredValue:function(e,t){var l=st();return Ze===null?Jo(l,e,t):bd(l,Ze.memoizedState,e,t)},useTransition:function(){var e=Vo(Cl)[0],t=st().memoizedState;return[typeof e=="boolean"?e:pu(e),t]},useSyncExternalStore:Js,useId:Td,useHostTransitionStatus:Fo,useFormState:cd,useActionState:cd,useOptimistic:function(e,t){var l=st();return Ze!==null?ed(l,Ze,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:Xo,useCacheRefresh:xd},Tn=null,Eu=0;function qr(e){var t=Eu;return Eu+=1,Tn===null&&(Tn=[]),qs(Tn,e,t)}function Ru(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Gr(e,t){throw t.$$typeof===A?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Dd(e){var t=e._init;return t(e._payload)}function _d(e){function t(M,T){if(e){var O=M.deletions;O===null?(M.deletions=[T],M.flags|=16):O.push(T)}}function l(M,T){if(!e)return null;for(;T!==null;)t(M,T),T=T.sibling;return null}function a(M){for(var T=new Map;M!==null;)M.key!==null?T.set(M.key,M):T.set(M.index,M),M=M.sibling;return T}function u(M,T){return M=Al(M,T),M.index=0,M.sibling=null,M}function i(M,T,O){return M.index=O,e?(O=M.alternate,O!==null?(O=O.index,O<T?(M.flags|=67108866,T):O):(M.flags|=67108866,T)):(M.flags|=1048576,T)}function s(M){return e&&M.alternate===null&&(M.flags|=67108866),M}function m(M,T,O,G){return T===null||T.tag!==6?(T=po(O,M.mode,G),T.return=M,T):(T=u(T,O),T.return=M,T)}function b(M,T,O,G){var te=O.type;return te===w?Y(M,T,O.props.children,G,O.key):T!==null&&(T.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===F&&Dd(te)===T.type)?(T=u(T,O.props),Ru(T,O),T.return=M,T):(T=Tr(O.type,O.key,O.props,null,M.mode,G),Ru(T,O),T.return=M,T)}function _(M,T,O,G){return T===null||T.tag!==4||T.stateNode.containerInfo!==O.containerInfo||T.stateNode.implementation!==O.implementation?(T=bo(O,M.mode,G),T.return=M,T):(T=u(T,O.children||[]),T.return=M,T)}function Y(M,T,O,G,te){return T===null||T.tag!==7?(T=Ha(O,M.mode,G,te),T.return=M,T):(T=u(T,O),T.return=M,T)}function Q(M,T,O){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=po(""+T,M.mode,O),T.return=M,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case x:return O=Tr(T.type,T.key,T.props,null,M.mode,O),Ru(O,T),O.return=M,O;case C:return T=bo(T,M.mode,O),T.return=M,T;case F:var G=T._init;return T=G(T._payload),Q(M,T,O)}if(we(T)||Ye(T))return T=Ha(T,M.mode,O,null),T.return=M,T;if(typeof T.then=="function")return Q(M,qr(T),O);if(T.$$typeof===K)return Q(M,Ar(M,T),O);Gr(M,T)}return null}function N(M,T,O,G){var te=T!==null?T.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return te!==null?null:m(M,T,""+O,G);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case x:return O.key===te?b(M,T,O,G):null;case C:return O.key===te?_(M,T,O,G):null;case F:return te=O._init,O=te(O._payload),N(M,T,O,G)}if(we(O)||Ye(O))return te!==null?null:Y(M,T,O,G,null);if(typeof O.then=="function")return N(M,T,qr(O),G);if(O.$$typeof===K)return N(M,T,Ar(M,O),G);Gr(M,O)}return null}function L(M,T,O,G,te){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return M=M.get(O)||null,m(T,M,""+G,te);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case x:return M=M.get(G.key===null?O:G.key)||null,b(T,M,G,te);case C:return M=M.get(G.key===null?O:G.key)||null,_(T,M,G,te);case F:var _e=G._init;return G=_e(G._payload),L(M,T,O,G,te)}if(we(G)||Ye(G))return M=M.get(O)||null,Y(T,M,G,te,null);if(typeof G.then=="function")return L(M,T,O,qr(G),te);if(G.$$typeof===K)return L(M,T,O,Ar(T,G),te);Gr(T,G)}return null}function be(M,T,O,G){for(var te=null,_e=null,fe=T,ye=T=0,gt=null;fe!==null&&ye<O.length;ye++){fe.index>ye?(gt=fe,fe=null):gt=fe.sibling;var Ge=N(M,fe,O[ye],G);if(Ge===null){fe===null&&(fe=gt);break}e&&fe&&Ge.alternate===null&&t(M,fe),T=i(Ge,T,ye),_e===null?te=Ge:_e.sibling=Ge,_e=Ge,fe=gt}if(ye===O.length)return l(M,fe),Xe&&ja(M,ye),te;if(fe===null){for(;ye<O.length;ye++)fe=Q(M,O[ye],G),fe!==null&&(T=i(fe,T,ye),_e===null?te=fe:_e.sibling=fe,_e=fe);return Xe&&ja(M,ye),te}for(fe=a(fe);ye<O.length;ye++)gt=L(fe,M,ye,O[ye],G),gt!==null&&(e&&gt.alternate!==null&&fe.delete(gt.key===null?ye:gt.key),T=i(gt,T,ye),_e===null?te=gt:_e.sibling=gt,_e=gt);return e&&fe.forEach(function(va){return t(M,va)}),Xe&&ja(M,ye),te}function me(M,T,O,G){if(O==null)throw Error(c(151));for(var te=null,_e=null,fe=T,ye=T=0,gt=null,Ge=O.next();fe!==null&&!Ge.done;ye++,Ge=O.next()){fe.index>ye?(gt=fe,fe=null):gt=fe.sibling;var va=N(M,fe,Ge.value,G);if(va===null){fe===null&&(fe=gt);break}e&&fe&&va.alternate===null&&t(M,fe),T=i(va,T,ye),_e===null?te=va:_e.sibling=va,_e=va,fe=gt}if(Ge.done)return l(M,fe),Xe&&ja(M,ye),te;if(fe===null){for(;!Ge.done;ye++,Ge=O.next())Ge=Q(M,Ge.value,G),Ge!==null&&(T=i(Ge,T,ye),_e===null?te=Ge:_e.sibling=Ge,_e=Ge);return Xe&&ja(M,ye),te}for(fe=a(fe);!Ge.done;ye++,Ge=O.next())Ge=L(fe,M,ye,Ge.value,G),Ge!==null&&(e&&Ge.alternate!==null&&fe.delete(Ge.key===null?ye:Ge.key),T=i(Ge,T,ye),_e===null?te=Ge:_e.sibling=Ge,_e=Ge);return e&&fe.forEach(function(p0){return t(M,p0)}),Xe&&ja(M,ye),te}function Je(M,T,O,G){if(typeof O=="object"&&O!==null&&O.type===w&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case x:e:{for(var te=O.key;T!==null;){if(T.key===te){if(te=O.type,te===w){if(T.tag===7){l(M,T.sibling),G=u(T,O.props.children),G.return=M,M=G;break e}}else if(T.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===F&&Dd(te)===T.type){l(M,T.sibling),G=u(T,O.props),Ru(G,O),G.return=M,M=G;break e}l(M,T);break}else t(M,T);T=T.sibling}O.type===w?(G=Ha(O.props.children,M.mode,G,O.key),G.return=M,M=G):(G=Tr(O.type,O.key,O.props,null,M.mode,G),Ru(G,O),G.return=M,M=G)}return s(M);case C:e:{for(te=O.key;T!==null;){if(T.key===te)if(T.tag===4&&T.stateNode.containerInfo===O.containerInfo&&T.stateNode.implementation===O.implementation){l(M,T.sibling),G=u(T,O.children||[]),G.return=M,M=G;break e}else{l(M,T);break}else t(M,T);T=T.sibling}G=bo(O,M.mode,G),G.return=M,M=G}return s(M);case F:return te=O._init,O=te(O._payload),Je(M,T,O,G)}if(we(O))return be(M,T,O,G);if(Ye(O)){if(te=Ye(O),typeof te!="function")throw Error(c(150));return O=te.call(O),me(M,T,O,G)}if(typeof O.then=="function")return Je(M,T,qr(O),G);if(O.$$typeof===K)return Je(M,T,Ar(M,O),G);Gr(M,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,T!==null&&T.tag===6?(l(M,T.sibling),G=u(T,O),G.return=M,M=G):(l(M,T),G=po(O,M.mode,G),G.return=M,M=G),s(M)):l(M,T)}return function(M,T,O,G){try{Eu=0;var te=Je(M,T,O,G);return Tn=null,te}catch(fe){if(fe===du||fe===Or)throw fe;var _e=jt(29,fe,null,M.mode);return _e.lanes=G,_e.return=M,_e}finally{}}}var xn=_d(!0),Cd=_d(!1),It=q(null),gl=null;function ta(e){var t=e.alternate;V(ht,ht.current&1),V(It,e),gl===null&&(t===null||bn.current!==null||t.memoizedState!==null)&&(gl=e)}function Ud(e){if(e.tag===22){if(V(ht,ht.current),V(It,e),gl===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(gl=e)}}else la()}function la(){V(ht,ht.current),V(It,It.current)}function Ul(e){W(It),gl===e&&(gl=null),W(ht)}var ht=q(0);function Xr(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Gc(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Po(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:p({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var Io={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=Xt(),u=Pl(a);u.payload=t,l!=null&&(u.callback=l),t=Il(e,u,a),t!==null&&(kt(t,e,a),mu(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=Xt(),u=Pl(a);u.tag=1,u.payload=t,l!=null&&(u.callback=l),t=Il(e,u,a),t!==null&&(kt(t,e,a),mu(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=Xt(),a=Pl(l);a.tag=2,t!=null&&(a.callback=t),t=Il(e,a,l),t!==null&&(kt(t,e,l),mu(t,e,l))}};function Nd(e,t,l,a,u,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,i,s):t.prototype&&t.prototype.isPureReactComponent?!nu(l,a)||!nu(u,i):!0}function Ld(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&Io.enqueueReplaceState(t,t.state,null)}function Qa(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=p({},l));for(var u in e)l[u]===void 0&&(l[u]=e[u])}return l}var kr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Hd(e){kr(e)}function Bd(e){console.error(e)}function jd(e){kr(e)}function Vr(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Yd(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(u){setTimeout(function(){throw u})}}function ec(e,t,l){return l=Pl(l),l.tag=3,l.payload={element:null},l.callback=function(){Vr(e,t)},l}function qd(e){return e=Pl(e),e.tag=3,e}function Gd(e,t,l,a){var u=l.type.getDerivedStateFromError;if(typeof u=="function"){var i=a.value;e.payload=function(){return u(i)},e.callback=function(){Yd(t,l,a)}}var s=l.stateNode;s!==null&&typeof s.componentDidCatch=="function"&&(e.callback=function(){Yd(t,l,a),typeof u!="function"&&(oa===null?oa=new Set([this]):oa.add(this));var m=a.stack;this.componentDidCatch(a.value,{componentStack:m!==null?m:""})})}function pg(e,t,l,a,u){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&cu(t,l,u,!0),l=It.current,l!==null){switch(l.tag){case 13:return gl===null?xc():l.alternate===null&&ut===0&&(ut=3),l.flags&=-257,l.flags|=65536,l.lanes=u,a===Oo?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Mc(e,a,u)),!1;case 22:return l.flags|=65536,a===Oo?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Mc(e,a,u)),!1}throw Error(c(435,l.tag))}return Mc(e,a,u),xc(),!1}if(Xe)return t=It.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=u,a!==Ro&&(e=Error(c(422),{cause:a}),ou($t(e,l)))):(a!==Ro&&(t=Error(c(423),{cause:a}),ou($t(t,l))),e=e.current.alternate,e.flags|=65536,u&=-u,e.lanes|=u,a=$t(a,l),u=ec(e.stateNode,a,u),Co(e,u),ut!==4&&(ut=2)),!1;var i=Error(c(520),{cause:a});if(i=$t(i,l),Ou===null?Ou=[i]:Ou.push(i),ut!==4&&(ut=2),t===null)return!0;a=$t(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=u&-u,l.lanes|=e,e=ec(l.stateNode,a,e),Co(l,e),!1;case 1:if(t=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(oa===null||!oa.has(i))))return l.flags|=65536,u&=-u,l.lanes|=u,u=qd(u),Gd(u,e,l,a),Co(l,u),!1}l=l.return}while(l!==null);return!1}var Xd=Error(c(461)),yt=!1;function St(e,t,l,a){t.child=e===null?Cd(t,null,l,a):xn(t,e.child,l,a)}function kd(e,t,l,a,u){l=l.render;var i=t.ref;if("ref"in a){var s={};for(var m in a)m!=="ref"&&(s[m]=a[m])}else s=a;return Xa(t),a=Bo(e,t,l,s,i,u),m=jo(),e!==null&&!yt?(Yo(e,t,u),Nl(e,t,u)):(Xe&&m&&So(t),t.flags|=1,St(e,t,a,u),t.child)}function Vd(e,t,l,a,u){if(e===null){var i=l.type;return typeof i=="function"&&!go(i)&&i.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=i,Qd(e,t,i,a,u)):(e=Tr(l.type,null,a,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!oc(e,u)){var s=i.memoizedProps;if(l=l.compare,l=l!==null?l:nu,l(s,a)&&e.ref===t.ref)return Nl(e,t,u)}return t.flags|=1,e=Al(i,a),e.ref=t.ref,e.return=t,t.child=e}function Qd(e,t,l,a,u){if(e!==null){var i=e.memoizedProps;if(nu(i,a)&&e.ref===t.ref)if(yt=!1,t.pendingProps=a=i,oc(e,u))(e.flags&131072)!==0&&(yt=!0);else return t.lanes=e.lanes,Nl(e,t,u)}return tc(e,t,l,a,u)}function Zd(e,t,l){var a=t.pendingProps,u=a.children,i=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,e!==null){for(u=t.child=e.child,i=0;u!==null;)i=i|u.lanes|u.childLanes,u=u.sibling;t.childLanes=i&~a}else t.childLanes=0,t.child=null;return Kd(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&zr(t,i!==null?i.cachePool:null),i!==null?Qs(t,i):No(),Ud(t);else return t.lanes=t.childLanes=536870912,Kd(e,t,i!==null?i.baseLanes|l:l,l)}else i!==null?(zr(t,i.cachePool),Qs(t,i),la(),t.memoizedState=null):(e!==null&&zr(t,null),No(),la());return St(e,t,u,l),t.child}function Kd(e,t,l,a){var u=zo();return u=u===null?null:{parent:dt._currentValue,pool:u},t.memoizedState={baseLanes:l,cachePool:u},e!==null&&zr(t,null),No(),Ud(t),e!==null&&cu(e,t,a,!0),null}function Qr(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(c(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function tc(e,t,l,a,u){return Xa(t),l=Bo(e,t,l,a,void 0,u),a=jo(),e!==null&&!yt?(Yo(e,t,u),Nl(e,t,u)):(Xe&&a&&So(t),t.flags|=1,St(e,t,l,u),t.child)}function Jd(e,t,l,a,u,i){return Xa(t),t.updateQueue=null,l=Ks(t,a,l,u),Zs(e),a=jo(),e!==null&&!yt?(Yo(e,t,i),Nl(e,t,i)):(Xe&&a&&So(t),t.flags|=1,St(e,t,l,i),t.child)}function $d(e,t,l,a,u){if(Xa(t),t.stateNode===null){var i=mn,s=l.contextType;typeof s=="object"&&s!==null&&(i=xt(s)),i=new l(a,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Io,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=a,i.state=t.memoizedState,i.refs={},Do(t),s=l.contextType,i.context=typeof s=="object"&&s!==null?xt(s):mn,i.state=t.memoizedState,s=l.getDerivedStateFromProps,typeof s=="function"&&(Po(t,l,s,a),i.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(s=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),s!==i.state&&Io.enqueueReplaceState(i,i.state,null),vu(t,a,i,u),yu(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){i=t.stateNode;var m=t.memoizedProps,b=Qa(l,m);i.props=b;var _=i.context,Y=l.contextType;s=mn,typeof Y=="object"&&Y!==null&&(s=xt(Y));var Q=l.getDerivedStateFromProps;Y=typeof Q=="function"||typeof i.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,Y||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(m||_!==s)&&Ld(t,i,a,s),Wl=!1;var N=t.memoizedState;i.state=N,vu(t,a,i,u),yu(),_=t.memoizedState,m||N!==_||Wl?(typeof Q=="function"&&(Po(t,l,Q,a),_=t.memoizedState),(b=Wl||Nd(t,l,b,a,N,_,s))?(Y||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=_),i.props=a,i.state=_,i.context=s,a=b):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{i=t.stateNode,_o(e,t),s=t.memoizedProps,Y=Qa(l,s),i.props=Y,Q=t.pendingProps,N=i.context,_=l.contextType,b=mn,typeof _=="object"&&_!==null&&(b=xt(_)),m=l.getDerivedStateFromProps,(_=typeof m=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==Q||N!==b)&&Ld(t,i,a,b),Wl=!1,N=t.memoizedState,i.state=N,vu(t,a,i,u),yu();var L=t.memoizedState;s!==Q||N!==L||Wl||e!==null&&e.dependencies!==null&&Mr(e.dependencies)?(typeof m=="function"&&(Po(t,l,m,a),L=t.memoizedState),(Y=Wl||Nd(t,l,Y,a,N,L,b)||e!==null&&e.dependencies!==null&&Mr(e.dependencies))?(_||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,L,b),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,L,b)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=L),i.props=a,i.state=L,i.context=b,a=Y):(typeof i.componentDidUpdate!="function"||s===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),a=!1)}return i=a,Qr(e,t),a=(t.flags&128)!==0,i||a?(i=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&a?(t.child=xn(t,e.child,null,u),t.child=xn(t,null,l,u)):St(e,t,l,u),t.memoizedState=i.state,e=t.child):e=Nl(e,t,u),e}function Fd(e,t,l,a){return iu(),t.flags|=256,St(e,t,l,a),t.child}var lc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ac(e){return{baseLanes:e,cachePool:Bs()}}function nc(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=el),e}function Wd(e,t,l){var a=t.pendingProps,u=!1,i=(t.flags&128)!==0,s;if((s=i)||(s=e!==null&&e.memoizedState===null?!1:(ht.current&2)!==0),s&&(u=!0,t.flags&=-129),s=(t.flags&32)!==0,t.flags&=-33,e===null){if(Xe){if(u?ta(t):la(),Xe){var m=nt,b;if(b=m){e:{for(b=m,m=vl;b.nodeType!==8;){if(!m){m=null;break e}if(b=dl(b.nextSibling),b===null){m=null;break e}}m=b}m!==null?(t.memoizedState={dehydrated:m,treeContext:Ba!==null?{id:zl,overflow:Ol}:null,retryLane:536870912,hydrationErrors:null},b=jt(18,null,null,0),b.stateNode=m,b.return=t,t.child=b,zt=t,nt=null,b=!0):b=!1}b||qa(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Gc(m)?t.lanes=32:t.lanes=536870912,null;Ul(t)}return m=a.children,a=a.fallback,u?(la(),u=t.mode,m=Zr({mode:"hidden",children:m},u),a=Ha(a,u,l,null),m.return=t,a.return=t,m.sibling=a,t.child=m,u=t.child,u.memoizedState=ac(l),u.childLanes=nc(e,s,l),t.memoizedState=lc,a):(ta(t),uc(t,m))}if(b=e.memoizedState,b!==null&&(m=b.dehydrated,m!==null)){if(i)t.flags&256?(ta(t),t.flags&=-257,t=rc(e,t,l)):t.memoizedState!==null?(la(),t.child=e.child,t.flags|=128,t=null):(la(),u=a.fallback,m=t.mode,a=Zr({mode:"visible",children:a.children},m),u=Ha(u,m,l,null),u.flags|=2,a.return=t,u.return=t,a.sibling=u,t.child=a,xn(t,e.child,null,l),a=t.child,a.memoizedState=ac(l),a.childLanes=nc(e,s,l),t.memoizedState=lc,t=u);else if(ta(t),Gc(m)){if(s=m.nextSibling&&m.nextSibling.dataset,s)var _=s.dgst;s=_,a=Error(c(419)),a.stack="",a.digest=s,ou({value:a,source:null,stack:null}),t=rc(e,t,l)}else if(yt||cu(e,t,l,!1),s=(l&e.childLanes)!==0,yt||s){if(s=Fe,s!==null&&(a=l&-l,a=(a&42)!==0?1:Kn(a),a=(a&(s.suspendedLanes|l))!==0?0:a,a!==0&&a!==b.retryLane))throw b.retryLane=a,hn(e,a),kt(s,e,a),Xd;m.data==="$?"||xc(),t=rc(e,t,l)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,nt=dl(m.nextSibling),zt=t,Xe=!0,Ya=null,vl=!1,e!==null&&(Wt[Pt++]=zl,Wt[Pt++]=Ol,Wt[Pt++]=Ba,zl=e.id,Ol=e.overflow,Ba=t),t=uc(t,a.children),t.flags|=4096);return t}return u?(la(),u=a.fallback,m=t.mode,b=e.child,_=b.sibling,a=Al(b,{mode:"hidden",children:a.children}),a.subtreeFlags=b.subtreeFlags&65011712,_!==null?u=Al(_,u):(u=Ha(u,m,l,null),u.flags|=2),u.return=t,a.return=t,a.sibling=u,t.child=a,a=u,u=t.child,m=e.child.memoizedState,m===null?m=ac(l):(b=m.cachePool,b!==null?(_=dt._currentValue,b=b.parent!==_?{parent:_,pool:_}:b):b=Bs(),m={baseLanes:m.baseLanes|l,cachePool:b}),u.memoizedState=m,u.childLanes=nc(e,s,l),t.memoizedState=lc,a):(ta(t),l=e.child,e=l.sibling,l=Al(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(s=t.deletions,s===null?(t.deletions=[e],t.flags|=16):s.push(e)),t.child=l,t.memoizedState=null,l)}function uc(e,t){return t=Zr({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Zr(e,t){return e=jt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function rc(e,t,l){return xn(t,e.child,null,l),e=uc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Pd(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),xo(e.return,t,l)}function ic(e,t,l,a,u){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:u}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=u)}function Id(e,t,l){var a=t.pendingProps,u=a.revealOrder,i=a.tail;if(St(e,t,a.children,l),a=ht.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Pd(e,l,t);else if(e.tag===19)Pd(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(V(ht,a),u){case"forwards":for(l=t.child,u=null;l!==null;)e=l.alternate,e!==null&&Xr(e)===null&&(u=l),l=l.sibling;l=u,l===null?(u=t.child,t.child=null):(u=l.sibling,l.sibling=null),ic(t,!1,u,l,i);break;case"backwards":for(l=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&Xr(e)===null){t.child=u;break}e=u.sibling,u.sibling=l,l=u,u=e}ic(t,!0,l,null,i);break;case"together":ic(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Nl(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),ia|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(cu(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,l=Al(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Al(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function oc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Mr(e)))}function bg(e,t,l){switch(t.tag){case 3:He(t,t.stateNode.containerInfo),Fl(t,dt,e.memoizedState.cache),iu();break;case 27:case 5:Pe(t);break;case 4:He(t,t.stateNode.containerInfo);break;case 10:Fl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(ta(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Wd(e,t,l):(ta(t),e=Nl(e,t,l),e!==null?e.sibling:null);ta(t);break;case 19:var u=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(cu(e,t,l,!1),a=(l&t.childLanes)!==0),u){if(a)return Id(e,t,l);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),V(ht,ht.current),a)break;return null;case 22:case 23:return t.lanes=0,Zd(e,t,l);case 24:Fl(t,dt,e.memoizedState.cache)}return Nl(e,t,l)}function eh(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)yt=!0;else{if(!oc(e,l)&&(t.flags&128)===0)return yt=!1,bg(e,t,l);yt=(e.flags&131072)!==0}else yt=!1,Xe&&(t.flags&1048576)!==0&&Ds(t,wr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,u=a._init;if(a=u(a._payload),t.type=a,typeof a=="function")go(a)?(e=Qa(a,e),t.tag=1,t=$d(null,t,a,e,l)):(t.tag=0,t=tc(null,t,a,e,l));else{if(a!=null){if(u=a.$$typeof,u===ue){t.tag=11,t=kd(null,t,a,e,l);break e}else if(u===oe){t.tag=14,t=Vd(null,t,a,e,l);break e}}throw t=xe(a)||a,Error(c(306,t,""))}}return t;case 0:return tc(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,u=Qa(a,t.pendingProps),$d(e,t,a,u,l);case 3:e:{if(He(t,t.stateNode.containerInfo),e===null)throw Error(c(387));a=t.pendingProps;var i=t.memoizedState;u=i.element,_o(e,t),vu(t,a,null,l);var s=t.memoizedState;if(a=s.cache,Fl(t,dt,a),a!==i.cache&&wo(t,[dt],l,!0),yu(),a=s.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:s.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=Fd(e,t,a,l);break e}else if(a!==u){u=$t(Error(c(424)),t),ou(u),t=Fd(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(nt=dl(e.firstChild),zt=t,Xe=!0,Ya=null,vl=!0,l=Cd(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(iu(),a===u){t=Nl(e,t,l);break e}St(e,t,a,l)}t=t.child}return t;case 26:return Qr(e,t),e===null?(l=nm(t.type,null,t.pendingProps,null))?t.memoizedState=l:Xe||(l=t.type,e=t.pendingProps,a=ri(se.current).createElement(l),a[Z]=t,a[$]=e,Rt(a,l,e),qe(a),t.stateNode=a):t.memoizedState=nm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Pe(t),e===null&&Xe&&(a=t.stateNode=tm(t.type,t.pendingProps,se.current),zt=t,vl=!0,u=nt,sa(t.type)?(Xc=u,nt=dl(a.firstChild)):nt=u),St(e,t,t.pendingProps.children,l),Qr(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Xe&&((u=a=nt)&&(a=Kg(a,t.type,t.pendingProps,vl),a!==null?(t.stateNode=a,zt=t,nt=dl(a.firstChild),vl=!1,u=!0):u=!1),u||qa(t)),Pe(t),u=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,a=i.children,jc(u,i)?a=null:s!==null&&jc(u,s)&&(t.flags|=32),t.memoizedState!==null&&(u=Bo(e,t,sg,null,null,l),ju._currentValue=u),Qr(e,t),St(e,t,a,l),t.child;case 6:return e===null&&Xe&&((e=l=nt)&&(l=Jg(l,t.pendingProps,vl),l!==null?(t.stateNode=l,zt=t,nt=null,e=!0):e=!1),e||qa(t)),null;case 13:return Wd(e,t,l);case 4:return He(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=xn(t,null,a,l):St(e,t,a,l),t.child;case 11:return kd(e,t,t.type,t.pendingProps,l);case 7:return St(e,t,t.pendingProps,l),t.child;case 8:return St(e,t,t.pendingProps.children,l),t.child;case 12:return St(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,Fl(t,t.type,a.value),St(e,t,a.children,l),t.child;case 9:return u=t.type._context,a=t.pendingProps.children,Xa(t),u=xt(u),a=a(u),t.flags|=1,St(e,t,a,l),t.child;case 14:return Vd(e,t,t.type,t.pendingProps,l);case 15:return Qd(e,t,t.type,t.pendingProps,l);case 19:return Id(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Zr(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Al(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Zd(e,t,l);case 24:return Xa(t),a=xt(dt),e===null?(u=zo(),u===null&&(u=Fe,i=Mo(),u.pooledCache=i,i.refCount++,i!==null&&(u.pooledCacheLanes|=l),u=i),t.memoizedState={parent:a,cache:u},Do(t),Fl(t,dt,u)):((e.lanes&l)!==0&&(_o(e,t),vu(t,null,null,l),yu()),u=e.memoizedState,i=t.memoizedState,u.parent!==a?(u={parent:a,cache:a},t.memoizedState=u,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=u),Fl(t,dt,a)):(a=i.cache,Fl(t,dt,a),a!==u.cache&&wo(t,[dt],l,!0))),St(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function Ll(e){e.flags|=4}function th(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!cm(t)){if(t=It.current,t!==null&&((Be&4194048)===Be?gl!==null:(Be&62914560)!==Be&&(Be&536870912)===0||t!==gl))throw hu=Oo,js;e.flags|=8192}}function Kr(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?cr():536870912,e.lanes|=t,zn|=t)}function Tu(e,t){if(!Xe)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function at(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var u=e.child;u!==null;)l|=u.lanes|u.childLanes,a|=u.subtreeFlags&65011712,a|=u.flags&65011712,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)l|=u.lanes|u.childLanes,a|=u.subtreeFlags,a|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function Sg(e,t,l){var a=t.pendingProps;switch(Eo(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return at(t),null;case 1:return at(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),_l(dt),it(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(ru(t)?Ll(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Us())),at(t),null;case 26:return l=t.memoizedState,e===null?(Ll(t),l!==null?(at(t),th(t,l)):(at(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Ll(t),at(t),th(t,l)):(at(t),t.flags&=-16777217):(e.memoizedProps!==a&&Ll(t),at(t),t.flags&=-16777217),null;case 27:Mt(t),l=se.current;var u=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Ll(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return at(t),null}e=I.current,ru(t)?_s(t):(e=tm(u,a,l),t.stateNode=e,Ll(t))}return at(t),null;case 5:if(Mt(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Ll(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return at(t),null}if(e=I.current,ru(t))_s(t);else{switch(u=ri(se.current),e){case 1:e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=u.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=u.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=u.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?u.createElement("select",{is:a.is}):u.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?u.createElement(l,{is:a.is}):u.createElement(l)}}e[Z]=t,e[$]=a;e:for(u=t.child;u!==null;){if(u.tag===5||u.tag===6)e.appendChild(u.stateNode);else if(u.tag!==4&&u.tag!==27&&u.child!==null){u.child.return=u,u=u.child;continue}if(u===t)break e;for(;u.sibling===null;){if(u.return===null||u.return===t)break e;u=u.return}u.sibling.return=u.return,u=u.sibling}t.stateNode=e;e:switch(Rt(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ll(t)}}return at(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Ll(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(c(166));if(e=se.current,ru(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,u=zt,u!==null)switch(u.tag){case 27:case 5:a=u.memoizedProps}e[Z]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Jh(e.nodeValue,l)),e||qa(t)}else e=ri(e).createTextNode(a),e[Z]=t,t.stateNode=e}return at(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(u=ru(t),a!==null&&a.dehydrated!==null){if(e===null){if(!u)throw Error(c(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(c(317));u[Z]=t}else iu(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;at(t),u=!1}else u=Us(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=u),u=!0;if(!u)return t.flags&256?(Ul(t),t):(Ul(t),null)}if(Ul(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,u=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(u=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==u&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Kr(t,t.updateQueue),at(t),null;case 4:return it(),e===null&&Uc(t.stateNode.containerInfo),at(t),null;case 10:return _l(t.type),at(t),null;case 19:if(W(ht),u=t.memoizedState,u===null)return at(t),null;if(a=(t.flags&128)!==0,i=u.rendering,i===null)if(a)Tu(u,!1);else{if(ut!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=Xr(e),i!==null){for(t.flags|=128,Tu(u,!1),e=i.updateQueue,t.updateQueue=e,Kr(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Os(l,e),l=l.sibling;return V(ht,ht.current&1|2),t.child}e=e.sibling}u.tail!==null&&Ht()>Fr&&(t.flags|=128,a=!0,Tu(u,!1),t.lanes=4194304)}else{if(!a)if(e=Xr(i),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Kr(t,e),Tu(u,!0),u.tail===null&&u.tailMode==="hidden"&&!i.alternate&&!Xe)return at(t),null}else 2*Ht()-u.renderingStartTime>Fr&&l!==536870912&&(t.flags|=128,a=!0,Tu(u,!1),t.lanes=4194304);u.isBackwards?(i.sibling=t.child,t.child=i):(e=u.last,e!==null?e.sibling=i:t.child=i,u.last=i)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Ht(),t.sibling=null,e=ht.current,V(ht,a?e&1|2:e&1),t):(at(t),null);case 22:case 23:return Ul(t),Lo(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(at(t),t.subtreeFlags&6&&(t.flags|=8192)):at(t),l=t.updateQueue,l!==null&&Kr(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&W(ka),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),_l(dt),at(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function Eg(e,t){switch(Eo(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return _l(dt),it(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Mt(t),null;case 13:if(Ul(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));iu()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(ht),null;case 4:return it(),null;case 10:return _l(t.type),null;case 22:case 23:return Ul(t),Lo(),e!==null&&W(ka),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return _l(dt),null;case 25:return null;default:return null}}function lh(e,t){switch(Eo(t),t.tag){case 3:_l(dt),it();break;case 26:case 27:case 5:Mt(t);break;case 4:it();break;case 13:Ul(t);break;case 19:W(ht);break;case 10:_l(t.type);break;case 22:case 23:Ul(t),Lo(),e!==null&&W(ka);break;case 24:_l(dt)}}function xu(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var u=a.next;l=u;do{if((l.tag&e)===e){a=void 0;var i=l.create,s=l.inst;a=i(),s.destroy=a}l=l.next}while(l!==u)}}catch(m){$e(t,t.return,m)}}function aa(e,t,l){try{var a=t.updateQueue,u=a!==null?a.lastEffect:null;if(u!==null){var i=u.next;a=i;do{if((a.tag&e)===e){var s=a.inst,m=s.destroy;if(m!==void 0){s.destroy=void 0,u=t;var b=l,_=m;try{_()}catch(Y){$e(u,b,Y)}}}a=a.next}while(a!==i)}}catch(Y){$e(t,t.return,Y)}}function ah(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Vs(t,l)}catch(a){$e(e,e.return,a)}}}function nh(e,t,l){l.props=Qa(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){$e(e,t,a)}}function wu(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(u){$e(e,t,u)}}function pl(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(u){$e(e,t,u)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(u){$e(e,t,u)}else l.current=null}function uh(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(u){$e(e,e.return,u)}}function cc(e,t,l){try{var a=e.stateNode;Xg(a,e.type,l,t),a[$]=t}catch(u){$e(e,e.return,u)}}function rh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&sa(e.type)||e.tag===4}function fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||rh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&sa(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function sc(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=ui));else if(a!==4&&(a===27&&sa(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(sc(e,t,l),e=e.sibling;e!==null;)sc(e,t,l),e=e.sibling}function Jr(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&sa(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(Jr(e,t,l),e=e.sibling;e!==null;)Jr(e,t,l),e=e.sibling}function ih(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,u=t.attributes;u.length;)t.removeAttributeNode(u[0]);Rt(t,a,l),t[Z]=e,t[$]=l}catch(i){$e(e,e.return,i)}}var Hl=!1,ct=!1,dc=!1,oh=typeof WeakSet=="function"?WeakSet:Set,vt=null;function Rg(e,t){if(e=e.containerInfo,Hc=di,e=bs(e),co(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var u=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break e}var s=0,m=-1,b=-1,_=0,Y=0,Q=e,N=null;t:for(;;){for(var L;Q!==l||u!==0&&Q.nodeType!==3||(m=s+u),Q!==i||a!==0&&Q.nodeType!==3||(b=s+a),Q.nodeType===3&&(s+=Q.nodeValue.length),(L=Q.firstChild)!==null;)N=Q,Q=L;for(;;){if(Q===e)break t;if(N===l&&++_===u&&(m=s),N===i&&++Y===a&&(b=s),(L=Q.nextSibling)!==null)break;Q=N,N=Q.parentNode}Q=L}l=m===-1||b===-1?null:{start:m,end:b}}else l=null}l=l||{start:0,end:0}}else l=null;for(Bc={focusedElem:e,selectionRange:l},di=!1,vt=t;vt!==null;)if(t=vt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,vt=e;else for(;vt!==null;){switch(t=vt,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,l=t,u=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var be=Qa(l.type,u,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(be,i),a.__reactInternalSnapshotBeforeUpdate=e}catch(me){$e(l,l.return,me)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)qc(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":qc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,vt=e;break}vt=t.return}}function ch(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:na(e,l),a&4&&xu(5,l);break;case 1:if(na(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(s){$e(l,l.return,s)}else{var u=Qa(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(u,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){$e(l,l.return,s)}}a&64&&ah(l),a&512&&wu(l,l.return);break;case 3:if(na(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{Vs(e,t)}catch(s){$e(l,l.return,s)}}break;case 27:t===null&&a&4&&ih(l);case 26:case 5:na(e,l),t===null&&a&4&&uh(l),a&512&&wu(l,l.return);break;case 12:na(e,l);break;case 13:na(e,l),a&4&&dh(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=_g.bind(null,l),$g(e,l))));break;case 22:if(a=l.memoizedState!==null||Hl,!a){t=t!==null&&t.memoizedState!==null||ct,u=Hl;var i=ct;Hl=a,(ct=t)&&!i?ua(e,l,(l.subtreeFlags&8772)!==0):na(e,l),Hl=u,ct=i}break;case 30:break;default:na(e,l)}}function fh(e){var t=e.alternate;t!==null&&(e.alternate=null,fh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&pe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var et=null,Nt=!1;function Bl(e,t,l){for(l=l.child;l!==null;)sh(e,t,l),l=l.sibling}function sh(e,t,l){if(tt&&typeof tt.onCommitFiberUnmount=="function")try{tt.onCommitFiberUnmount(Ot,l)}catch{}switch(l.tag){case 26:ct||pl(l,t),Bl(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:ct||pl(l,t);var a=et,u=Nt;sa(l.type)&&(et=l.stateNode,Nt=!1),Bl(e,t,l),Nu(l.stateNode),et=a,Nt=u;break;case 5:ct||pl(l,t);case 6:if(a=et,u=Nt,et=null,Bl(e,t,l),et=a,Nt=u,et!==null)if(Nt)try{(et.nodeType===9?et.body:et.nodeName==="HTML"?et.ownerDocument.body:et).removeChild(l.stateNode)}catch(i){$e(l,t,i)}else try{et.removeChild(l.stateNode)}catch(i){$e(l,t,i)}break;case 18:et!==null&&(Nt?(e=et,Ih(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Xu(e)):Ih(et,l.stateNode));break;case 4:a=et,u=Nt,et=l.stateNode.containerInfo,Nt=!0,Bl(e,t,l),et=a,Nt=u;break;case 0:case 11:case 14:case 15:ct||aa(2,l,t),ct||aa(4,l,t),Bl(e,t,l);break;case 1:ct||(pl(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&nh(l,t,a)),Bl(e,t,l);break;case 21:Bl(e,t,l);break;case 22:ct=(a=ct)||l.memoizedState!==null,Bl(e,t,l),ct=a;break;default:Bl(e,t,l)}}function dh(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Xu(e)}catch(l){$e(t,t.return,l)}}function Tg(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new oh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new oh),t;default:throw Error(c(435,e.tag))}}function hc(e,t){var l=Tg(e);t.forEach(function(a){var u=Cg.bind(null,e,a);l.has(a)||(l.add(a),a.then(u,u))})}function Yt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var u=l[a],i=e,s=t,m=s;e:for(;m!==null;){switch(m.tag){case 27:if(sa(m.type)){et=m.stateNode,Nt=!1;break e}break;case 5:et=m.stateNode,Nt=!1;break e;case 3:case 4:et=m.stateNode.containerInfo,Nt=!0;break e}m=m.return}if(et===null)throw Error(c(160));sh(i,s,u),et=null,Nt=!1,i=u.alternate,i!==null&&(i.return=null),u.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)hh(t,e),t=t.sibling}var sl=null;function hh(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Yt(t,e),qt(e),a&4&&(aa(3,e,e.return),xu(3,e),aa(5,e,e.return));break;case 1:Yt(t,e),qt(e),a&512&&(ct||l===null||pl(l,l.return)),a&64&&Hl&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var u=sl;if(Yt(t,e),qt(e),a&512&&(ct||l===null||pl(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,u=u.ownerDocument||u;t:switch(a){case"title":i=u.getElementsByTagName("title")[0],(!i||i[ge]||i[Z]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=u.createElement(a),u.head.insertBefore(i,u.querySelector("head > title"))),Rt(i,a,l),i[Z]=e,qe(i),a=i;break e;case"link":var s=im("link","href",u).get(a+(l.href||""));if(s){for(var m=0;m<s.length;m++)if(i=s[m],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){s.splice(m,1);break t}}i=u.createElement(a),Rt(i,a,l),u.head.appendChild(i);break;case"meta":if(s=im("meta","content",u).get(a+(l.content||""))){for(m=0;m<s.length;m++)if(i=s[m],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){s.splice(m,1);break t}}i=u.createElement(a),Rt(i,a,l),u.head.appendChild(i);break;default:throw Error(c(468,a))}i[Z]=e,qe(i),a=i}e.stateNode=a}else om(u,e.type,e.stateNode);else e.stateNode=rm(u,a,e.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?om(u,e.type,e.stateNode):rm(u,a,e.memoizedProps)):a===null&&e.stateNode!==null&&cc(e,e.memoizedProps,l.memoizedProps)}break;case 27:Yt(t,e),qt(e),a&512&&(ct||l===null||pl(l,l.return)),l!==null&&a&4&&cc(e,e.memoizedProps,l.memoizedProps);break;case 5:if(Yt(t,e),qt(e),a&512&&(ct||l===null||pl(l,l.return)),e.flags&32){u=e.stateNode;try{un(u,"")}catch(L){$e(e,e.return,L)}}a&4&&e.stateNode!=null&&(u=e.memoizedProps,cc(e,u,l!==null?l.memoizedProps:u)),a&1024&&(dc=!0);break;case 6:if(Yt(t,e),qt(e),a&4){if(e.stateNode===null)throw Error(c(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(L){$e(e,e.return,L)}}break;case 3:if(ci=null,u=sl,sl=ii(t.containerInfo),Yt(t,e),sl=u,qt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Xu(t.containerInfo)}catch(L){$e(e,e.return,L)}dc&&(dc=!1,mh(e));break;case 4:a=sl,sl=ii(e.stateNode.containerInfo),Yt(t,e),qt(e),sl=a;break;case 12:Yt(t,e),qt(e);break;case 13:Yt(t,e),qt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(bc=Ht()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 22:u=e.memoizedState!==null;var b=l!==null&&l.memoizedState!==null,_=Hl,Y=ct;if(Hl=_||u,ct=Y||b,Yt(t,e),ct=Y,Hl=_,qt(e),a&8192)e:for(t=e.stateNode,t._visibility=u?t._visibility&-2:t._visibility|1,u&&(l===null||b||Hl||ct||Za(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){b=l=t;try{if(i=b.stateNode,u)s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none";else{m=b.stateNode;var Q=b.memoizedProps.style,N=Q!=null&&Q.hasOwnProperty("display")?Q.display:null;m.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(L){$e(b,b.return,L)}}}else if(t.tag===6){if(l===null){b=t;try{b.stateNode.nodeValue=u?"":b.memoizedProps}catch(L){$e(b,b.return,L)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,hc(e,l))));break;case 19:Yt(t,e),qt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,hc(e,a)));break;case 30:break;case 21:break;default:Yt(t,e),qt(e)}}function qt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(rh(a)){l=a;break}a=a.return}if(l==null)throw Error(c(160));switch(l.tag){case 27:var u=l.stateNode,i=fc(e);Jr(e,i,u);break;case 5:var s=l.stateNode;l.flags&32&&(un(s,""),l.flags&=-33);var m=fc(e);Jr(e,m,s);break;case 3:case 4:var b=l.stateNode.containerInfo,_=fc(e);sc(e,_,b);break;default:throw Error(c(161))}}catch(Y){$e(e,e.return,Y)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function mh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;mh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function na(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)ch(e,t.alternate,t),t=t.sibling}function Za(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:aa(4,t,t.return),Za(t);break;case 1:pl(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&nh(t,t.return,l),Za(t);break;case 27:Nu(t.stateNode);case 26:case 5:pl(t,t.return),Za(t);break;case 22:t.memoizedState===null&&Za(t);break;case 30:Za(t);break;default:Za(t)}e=e.sibling}}function ua(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,u=e,i=t,s=i.flags;switch(i.tag){case 0:case 11:case 15:ua(u,i,l),xu(4,i);break;case 1:if(ua(u,i,l),a=i,u=a.stateNode,typeof u.componentDidMount=="function")try{u.componentDidMount()}catch(_){$e(a,a.return,_)}if(a=i,u=a.updateQueue,u!==null){var m=a.stateNode;try{var b=u.shared.hiddenCallbacks;if(b!==null)for(u.shared.hiddenCallbacks=null,u=0;u<b.length;u++)ks(b[u],m)}catch(_){$e(a,a.return,_)}}l&&s&64&&ah(i),wu(i,i.return);break;case 27:ih(i);case 26:case 5:ua(u,i,l),l&&a===null&&s&4&&uh(i),wu(i,i.return);break;case 12:ua(u,i,l);break;case 13:ua(u,i,l),l&&s&4&&dh(u,i);break;case 22:i.memoizedState===null&&ua(u,i,l),wu(i,i.return);break;case 30:break;default:ua(u,i,l)}t=t.sibling}}function mc(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&fu(l))}function yc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&fu(e))}function bl(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)yh(e,t,l,a),t=t.sibling}function yh(e,t,l,a){var u=t.flags;switch(t.tag){case 0:case 11:case 15:bl(e,t,l,a),u&2048&&xu(9,t);break;case 1:bl(e,t,l,a);break;case 3:bl(e,t,l,a),u&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&fu(e)));break;case 12:if(u&2048){bl(e,t,l,a),e=t.stateNode;try{var i=t.memoizedProps,s=i.id,m=i.onPostCommit;typeof m=="function"&&m(s,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){$e(t,t.return,b)}}else bl(e,t,l,a);break;case 13:bl(e,t,l,a);break;case 23:break;case 22:i=t.stateNode,s=t.alternate,t.memoizedState!==null?i._visibility&2?bl(e,t,l,a):Mu(e,t):i._visibility&2?bl(e,t,l,a):(i._visibility|=2,wn(e,t,l,a,(t.subtreeFlags&10256)!==0)),u&2048&&mc(s,t);break;case 24:bl(e,t,l,a),u&2048&&yc(t.alternate,t);break;default:bl(e,t,l,a)}}function wn(e,t,l,a,u){for(u=u&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,s=t,m=l,b=a,_=s.flags;switch(s.tag){case 0:case 11:case 15:wn(i,s,m,b,u),xu(8,s);break;case 23:break;case 22:var Y=s.stateNode;s.memoizedState!==null?Y._visibility&2?wn(i,s,m,b,u):Mu(i,s):(Y._visibility|=2,wn(i,s,m,b,u)),u&&_&2048&&mc(s.alternate,s);break;case 24:wn(i,s,m,b,u),u&&_&2048&&yc(s.alternate,s);break;default:wn(i,s,m,b,u)}t=t.sibling}}function Mu(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,u=a.flags;switch(a.tag){case 22:Mu(l,a),u&2048&&mc(a.alternate,a);break;case 24:Mu(l,a),u&2048&&yc(a.alternate,a);break;default:Mu(l,a)}t=t.sibling}}var Au=8192;function Mn(e){if(e.subtreeFlags&Au)for(e=e.child;e!==null;)vh(e),e=e.sibling}function vh(e){switch(e.tag){case 26:Mn(e),e.flags&Au&&e.memoizedState!==null&&o0(sl,e.memoizedState,e.memoizedProps);break;case 5:Mn(e);break;case 3:case 4:var t=sl;sl=ii(e.stateNode.containerInfo),Mn(e),sl=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Au,Au=16777216,Mn(e),Au=t):Mn(e));break;default:Mn(e)}}function gh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function zu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];vt=a,bh(a,e)}gh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ph(e),e=e.sibling}function ph(e){switch(e.tag){case 0:case 11:case 15:zu(e),e.flags&2048&&aa(9,e,e.return);break;case 3:zu(e);break;case 12:zu(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,$r(e)):zu(e);break;default:zu(e)}}function $r(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];vt=a,bh(a,e)}gh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:aa(8,t,t.return),$r(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,$r(t));break;default:$r(t)}e=e.sibling}}function bh(e,t){for(;vt!==null;){var l=vt;switch(l.tag){case 0:case 11:case 15:aa(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:fu(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,vt=a;else e:for(l=e;vt!==null;){a=vt;var u=a.sibling,i=a.return;if(fh(a),a===l){vt=null;break e}if(u!==null){u.return=i,vt=u;break e}vt=i}}}var xg={getCacheForType:function(e){var t=xt(dt),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},wg=typeof WeakMap=="function"?WeakMap:Map,ke=0,Fe=null,Ue=null,Be=0,Ve=0,Gt=null,ra=!1,An=!1,vc=!1,jl=0,ut=0,ia=0,Ka=0,gc=0,el=0,zn=0,Ou=null,Lt=null,pc=!1,bc=0,Fr=1/0,Wr=null,oa=null,Et=0,ca=null,On=null,Dn=0,Sc=0,Ec=null,Sh=null,Du=0,Rc=null;function Xt(){if((ke&2)!==0&&Be!==0)return Be&-Be;if(D.T!==null){var e=gn;return e!==0?e:Oc()}return R()}function Eh(){el===0&&(el=(Be&536870912)===0||Xe?ln():536870912);var e=It.current;return e!==null&&(e.flags|=32),el}function kt(e,t,l){(e===Fe&&(Ve===2||Ve===9)||e.cancelPendingCommit!==null)&&(_n(e,0),fa(e,Be,el,!1)),Oa(e,l),((ke&2)===0||e!==Fe)&&(e===Fe&&((ke&2)===0&&(Ka|=l),ut===4&&fa(e,Be,el,!1)),Sl(e))}function Rh(e,t,l){if((ke&6)!==0)throw Error(c(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||yl(e,t),u=a?zg(e,t):wc(e,t,!0),i=a;do{if(u===0){An&&!a&&fa(e,t,0,!1);break}else{if(l=e.current.alternate,i&&!Mg(l)){u=wc(e,t,!1),i=!1;continue}if(u===2){if(i=t,e.errorRecoveryDisabledLanes&i)var s=0;else s=e.pendingLanes&-536870913,s=s!==0?s:s&536870912?536870912:0;if(s!==0){t=s;e:{var m=e;u=Ou;var b=m.current.memoizedState.isDehydrated;if(b&&(_n(m,s).flags|=256),s=wc(m,s,!1),s!==2){if(vc&&!b){m.errorRecoveryDisabledLanes|=i,Ka|=i,u=4;break e}i=Lt,Lt=u,i!==null&&(Lt===null?Lt=i:Lt.push.apply(Lt,i))}u=s}if(i=!1,u!==2)continue}}if(u===1){_n(e,0),fa(e,t,0,!0);break}e:{switch(a=e,i=u,i){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:fa(a,t,el,!ra);break e;case 2:Lt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(u=bc+300-Ht(),10<u)){if(fa(a,t,el,!ra),za(a,0,!0)!==0)break e;a.timeoutHandle=Wh(Th.bind(null,a,l,Lt,Wr,pc,t,el,Ka,zn,ra,i,2,-0,0),u);break e}Th(a,l,Lt,Wr,pc,t,el,Ka,zn,ra,i,0,-0,0)}}break}while(!0);Sl(e)}function Th(e,t,l,a,u,i,s,m,b,_,Y,Q,N,L){if(e.timeoutHandle=-1,Q=t.subtreeFlags,(Q&8192||(Q&16785408)===16785408)&&(Bu={stylesheets:null,count:0,unsuspend:i0},vh(t),Q=c0(),Q!==null)){e.cancelPendingCommit=Q(Dh.bind(null,e,t,i,l,a,u,s,m,b,Y,1,N,L)),fa(e,i,s,!_);return}Dh(e,t,i,l,a,u,s,m,b)}function Mg(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var u=l[a],i=u.getSnapshot;u=u.value;try{if(!Bt(i(),u))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function fa(e,t,l,a){t&=~gc,t&=~Ka,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var u=t;0<u;){var i=31-bt(u),s=1<<i;a[i]=-1,u&=~s}l!==0&&Da(e,l,t)}function Pr(){return(ke&6)===0?(_u(0),!1):!0}function Tc(){if(Ue!==null){if(Ve===0)var e=Ue.return;else e=Ue,Dl=Ga=null,qo(e),Tn=null,Eu=0,e=Ue;for(;e!==null;)lh(e.alternate,e),e=e.return;Ue=null}}function _n(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,Vg(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Tc(),Fe=e,Ue=l=Al(e.current,null),Be=t,Ve=0,Gt=null,ra=!1,An=yl(e,t),vc=!1,zn=el=gc=Ka=ia=ut=0,Lt=Ou=null,pc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var u=31-bt(a),i=1<<u;t|=e[u],a&=~i}return jl=t,Sr(),l}function xh(e,t){Ae=null,D.H=Yr,t===du||t===Or?(t=Gs(),Ve=3):t===js?(t=Gs(),Ve=4):Ve=t===Xd?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Gt=t,Ue===null&&(ut=1,Vr(e,$t(t,e.current)))}function wh(){var e=D.H;return D.H=Yr,e===null?Yr:e}function Mh(){var e=D.A;return D.A=xg,e}function xc(){ut=4,ra||(Be&4194048)!==Be&&It.current!==null||(An=!0),(ia&134217727)===0&&(Ka&134217727)===0||Fe===null||fa(Fe,Be,el,!1)}function wc(e,t,l){var a=ke;ke|=2;var u=wh(),i=Mh();(Fe!==e||Be!==t)&&(Wr=null,_n(e,t)),t=!1;var s=ut;e:do try{if(Ve!==0&&Ue!==null){var m=Ue,b=Gt;switch(Ve){case 8:Tc(),s=6;break e;case 3:case 2:case 9:case 6:It.current===null&&(t=!0);var _=Ve;if(Ve=0,Gt=null,Cn(e,m,b,_),l&&An){s=0;break e}break;default:_=Ve,Ve=0,Gt=null,Cn(e,m,b,_)}}Ag(),s=ut;break}catch(Y){xh(e,Y)}while(!0);return t&&e.shellSuspendCounter++,Dl=Ga=null,ke=a,D.H=u,D.A=i,Ue===null&&(Fe=null,Be=0,Sr()),s}function Ag(){for(;Ue!==null;)Ah(Ue)}function zg(e,t){var l=ke;ke|=2;var a=wh(),u=Mh();Fe!==e||Be!==t?(Wr=null,Fr=Ht()+500,_n(e,t)):An=yl(e,t);e:do try{if(Ve!==0&&Ue!==null){t=Ue;var i=Gt;t:switch(Ve){case 1:Ve=0,Gt=null,Cn(e,t,i,1);break;case 2:case 9:if(Ys(i)){Ve=0,Gt=null,zh(t);break}t=function(){Ve!==2&&Ve!==9||Fe!==e||(Ve=7),Sl(e)},i.then(t,t);break e;case 3:Ve=7;break e;case 4:Ve=5;break e;case 7:Ys(i)?(Ve=0,Gt=null,zh(t)):(Ve=0,Gt=null,Cn(e,t,i,7));break;case 5:var s=null;switch(Ue.tag){case 26:s=Ue.memoizedState;case 5:case 27:var m=Ue;if(!s||cm(s)){Ve=0,Gt=null;var b=m.sibling;if(b!==null)Ue=b;else{var _=m.return;_!==null?(Ue=_,Ir(_)):Ue=null}break t}}Ve=0,Gt=null,Cn(e,t,i,5);break;case 6:Ve=0,Gt=null,Cn(e,t,i,6);break;case 8:Tc(),ut=6;break e;default:throw Error(c(462))}}Og();break}catch(Y){xh(e,Y)}while(!0);return Dl=Ga=null,D.H=a,D.A=u,ke=l,Ue!==null?0:(Fe=null,Be=0,Sr(),ut)}function Og(){for(;Ue!==null&&!Vi();)Ah(Ue)}function Ah(e){var t=eh(e.alternate,e,jl);e.memoizedProps=e.pendingProps,t===null?Ir(e):Ue=t}function zh(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Jd(l,t,t.pendingProps,t.type,void 0,Be);break;case 11:t=Jd(l,t,t.pendingProps,t.type.render,t.ref,Be);break;case 5:qo(t);default:lh(l,t),t=Ue=Os(t,jl),t=eh(l,t,jl)}e.memoizedProps=e.pendingProps,t===null?Ir(e):Ue=t}function Cn(e,t,l,a){Dl=Ga=null,qo(t),Tn=null,Eu=0;var u=t.return;try{if(pg(e,u,t,l,Be)){ut=1,Vr(e,$t(l,e.current)),Ue=null;return}}catch(i){if(u!==null)throw Ue=u,i;ut=1,Vr(e,$t(l,e.current)),Ue=null;return}t.flags&32768?(Xe||a===1?e=!0:An||(Be&536870912)!==0?e=!1:(ra=e=!0,(a===2||a===9||a===3||a===6)&&(a=It.current,a!==null&&a.tag===13&&(a.flags|=16384))),Oh(t,e)):Ir(t)}function Ir(e){var t=e;do{if((t.flags&32768)!==0){Oh(t,ra);return}e=t.return;var l=Sg(t.alternate,t,jl);if(l!==null){Ue=l;return}if(t=t.sibling,t!==null){Ue=t;return}Ue=t=e}while(t!==null);ut===0&&(ut=5)}function Oh(e,t){do{var l=Eg(e.alternate,e);if(l!==null){l.flags&=32767,Ue=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){Ue=e;return}Ue=e=l}while(e!==null);ut=6,Ue=null}function Dh(e,t,l,a,u,i,s,m,b){e.cancelPendingCommit=null;do ei();while(Et!==0);if((ke&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(i=t.lanes|t.childLanes,i|=yo,fr(e,l,i,s,m,b),e===Fe&&(Ue=Fe=null,Be=0),On=t,ca=e,Dn=l,Sc=i,Ec=u,Sh=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Ug(tn,function(){return Lh(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=D.T,D.T=null,u=J.p,J.p=2,s=ke,ke|=4;try{Rg(e,t,l)}finally{ke=s,J.p=u,D.T=a}}Et=1,_h(),Ch(),Uh()}}function _h(){if(Et===1){Et=0;var e=ca,t=On,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=D.T,D.T=null;var a=J.p;J.p=2;var u=ke;ke|=4;try{hh(t,e);var i=Bc,s=bs(e.containerInfo),m=i.focusedElem,b=i.selectionRange;if(s!==m&&m&&m.ownerDocument&&ps(m.ownerDocument.documentElement,m)){if(b!==null&&co(m)){var _=b.start,Y=b.end;if(Y===void 0&&(Y=_),"selectionStart"in m)m.selectionStart=_,m.selectionEnd=Math.min(Y,m.value.length);else{var Q=m.ownerDocument||document,N=Q&&Q.defaultView||window;if(N.getSelection){var L=N.getSelection(),be=m.textContent.length,me=Math.min(b.start,be),Je=b.end===void 0?me:Math.min(b.end,be);!L.extend&&me>Je&&(s=Je,Je=me,me=s);var M=gs(m,me),T=gs(m,Je);if(M&&T&&(L.rangeCount!==1||L.anchorNode!==M.node||L.anchorOffset!==M.offset||L.focusNode!==T.node||L.focusOffset!==T.offset)){var O=Q.createRange();O.setStart(M.node,M.offset),L.removeAllRanges(),me>Je?(L.addRange(O),L.extend(T.node,T.offset)):(O.setEnd(T.node,T.offset),L.addRange(O))}}}}for(Q=[],L=m;L=L.parentNode;)L.nodeType===1&&Q.push({element:L,left:L.scrollLeft,top:L.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<Q.length;m++){var G=Q[m];G.element.scrollLeft=G.left,G.element.scrollTop=G.top}}di=!!Hc,Bc=Hc=null}finally{ke=u,J.p=a,D.T=l}}e.current=t,Et=2}}function Ch(){if(Et===2){Et=0;var e=ca,t=On,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=D.T,D.T=null;var a=J.p;J.p=2;var u=ke;ke|=4;try{ch(e,t.alternate,t)}finally{ke=u,J.p=a,D.T=l}}Et=3}}function Uh(){if(Et===4||Et===3){Et=0,Qi();var e=ca,t=On,l=Dn,a=Sh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Et=5:(Et=0,On=ca=null,Nh(e,e.pendingLanes));var u=e.pendingLanes;if(u===0&&(oa=null),Jn(l),t=t.stateNode,tt&&typeof tt.onCommitFiberRoot=="function")try{tt.onCommitFiberRoot(Ot,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=D.T,u=J.p,J.p=2,D.T=null;try{for(var i=e.onRecoverableError,s=0;s<a.length;s++){var m=a[s];i(m.value,{componentStack:m.stack})}}finally{D.T=t,J.p=u}}(Dn&3)!==0&&ei(),Sl(e),u=e.pendingLanes,(l&4194090)!==0&&(u&42)!==0?e===Rc?Du++:(Du=0,Rc=e):Du=0,_u(0)}}function Nh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,fu(t)))}function ei(e){return _h(),Ch(),Uh(),Lh()}function Lh(){if(Et!==5)return!1;var e=ca,t=Sc;Sc=0;var l=Jn(Dn),a=D.T,u=J.p;try{J.p=32>l?32:l,D.T=null,l=Ec,Ec=null;var i=ca,s=Dn;if(Et=0,On=ca=null,Dn=0,(ke&6)!==0)throw Error(c(331));var m=ke;if(ke|=4,ph(i.current),yh(i,i.current,s,l),ke=m,_u(0,!1),tt&&typeof tt.onPostCommitFiberRoot=="function")try{tt.onPostCommitFiberRoot(Ot,i)}catch{}return!0}finally{J.p=u,D.T=a,Nh(e,t)}}function Hh(e,t,l){t=$t(l,t),t=ec(e.stateNode,t,2),e=Il(e,t,2),e!==null&&(Oa(e,2),Sl(e))}function $e(e,t,l){if(e.tag===3)Hh(e,e,l);else for(;t!==null;){if(t.tag===3){Hh(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(oa===null||!oa.has(a))){e=$t(l,e),l=qd(2),a=Il(t,l,2),a!==null&&(Gd(l,a,t,e),Oa(a,2),Sl(a));break}}t=t.return}}function Mc(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new wg;var u=new Set;a.set(t,u)}else u=a.get(t),u===void 0&&(u=new Set,a.set(t,u));u.has(l)||(vc=!0,u.add(l),e=Dg.bind(null,e,t,l),t.then(e,e))}function Dg(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Fe===e&&(Be&l)===l&&(ut===4||ut===3&&(Be&62914560)===Be&&300>Ht()-bc?(ke&2)===0&&_n(e,0):gc|=l,zn===Be&&(zn=0)),Sl(e)}function Bh(e,t){t===0&&(t=cr()),e=hn(e,t),e!==null&&(Oa(e,t),Sl(e))}function _g(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Bh(e,l)}function Cg(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,u=e.memoizedState;u!==null&&(l=u.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(c(314))}a!==null&&a.delete(t),Bh(e,l)}function Ug(e,t){return Qn(e,t)}var ti=null,Un=null,Ac=!1,li=!1,zc=!1,Ja=0;function Sl(e){e!==Un&&e.next===null&&(Un===null?ti=Un=e:Un=Un.next=e),li=!0,Ac||(Ac=!0,Lg())}function _u(e,t){if(!zc&&li){zc=!0;do for(var l=!1,a=ti;a!==null;){if(e!==0){var u=a.pendingLanes;if(u===0)var i=0;else{var s=a.suspendedLanes,m=a.pingedLanes;i=(1<<31-bt(42|e)+1)-1,i&=u&~(s&~m),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,Gh(a,i))}else i=Be,i=za(a,a===Fe?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||yl(a,i)||(l=!0,Gh(a,i));a=a.next}while(l);zc=!1}}function Ng(){jh()}function jh(){li=Ac=!1;var e=0;Ja!==0&&(kg()&&(e=Ja),Ja=0);for(var t=Ht(),l=null,a=ti;a!==null;){var u=a.next,i=Yh(a,t);i===0?(a.next=null,l===null?ti=u:l.next=u,u===null&&(Un=l)):(l=a,(e!==0||(i&3)!==0)&&(li=!0)),a=u}_u(e)}function Yh(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,u=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var s=31-bt(i),m=1<<s,b=u[s];b===-1?((m&l)===0||(m&a)!==0)&&(u[s]=or(m,t)):b<=t&&(e.expiredLanes|=m),i&=~m}if(t=Fe,l=Be,l=za(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(Ve===2||Ve===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&rl(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||yl(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&rl(a),Jn(l)){case 2:case 8:l=ur;break;case 32:l=tn;break;case 268435456:l=Vl;break;default:l=tn}return a=qh.bind(null,e),l=Qn(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&rl(a),e.callbackPriority=2,e.callbackNode=null,2}function qh(e,t){if(Et!==0&&Et!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(ei()&&e.callbackNode!==l)return null;var a=Be;return a=za(e,e===Fe?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Rh(e,a,t),Yh(e,Ht()),e.callbackNode!=null&&e.callbackNode===l?qh.bind(null,e):null)}function Gh(e,t){if(ei())return null;Rh(e,t,!0)}function Lg(){Qg(function(){(ke&6)!==0?Qn(nr,Ng):jh()})}function Oc(){return Ja===0&&(Ja=ln()),Ja}function Xh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:hr(""+e)}function kh(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function Hg(e,t,l,a,u){if(t==="submit"&&l&&l.stateNode===u){var i=Xh((u[$]||null).action),s=a.submitter;s&&(t=(t=s[$]||null)?Xh(t.formAction):s.getAttribute("formAction"),t!==null&&(i=t,s=null));var m=new gr("action","action",null,a,u);e.push({event:m,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Ja!==0){var b=s?kh(u,s):new FormData(u);$o(l,{pending:!0,data:b,method:u.method,action:i},null,b)}}else typeof i=="function"&&(m.preventDefault(),b=s?kh(u,s):new FormData(u),$o(l,{pending:!0,data:b,method:u.method,action:i},i,b))},currentTarget:u}]})}}for(var Dc=0;Dc<mo.length;Dc++){var _c=mo[Dc],Bg=_c.toLowerCase(),jg=_c[0].toUpperCase()+_c.slice(1);fl(Bg,"on"+jg)}fl(Rs,"onAnimationEnd"),fl(Ts,"onAnimationIteration"),fl(xs,"onAnimationStart"),fl("dblclick","onDoubleClick"),fl("focusin","onFocus"),fl("focusout","onBlur"),fl(tg,"onTransitionRun"),fl(lg,"onTransitionStart"),fl(ag,"onTransitionCancel"),fl(ws,"onTransitionEnd"),Zt("onMouseEnter",["mouseout","mouseover"]),Zt("onMouseLeave",["mouseout","mouseover"]),Zt("onPointerEnter",["pointerout","pointerover"]),Zt("onPointerLeave",["pointerout","pointerover"]),At("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),At("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),At("onBeforeInput",["compositionend","keypress","textInput","paste"]),At("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),At("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),At("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Cu));function Vh(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],u=a.event;a=a.listeners;e:{var i=void 0;if(t)for(var s=a.length-1;0<=s;s--){var m=a[s],b=m.instance,_=m.currentTarget;if(m=m.listener,b!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=_;try{i(u)}catch(Y){kr(Y)}u.currentTarget=null,i=b}else for(s=0;s<a.length;s++){if(m=a[s],b=m.instance,_=m.currentTarget,m=m.listener,b!==i&&u.isPropagationStopped())break e;i=m,u.currentTarget=_;try{i(u)}catch(Y){kr(Y)}u.currentTarget=null,i=b}}}}function Ne(e,t){var l=t[he];l===void 0&&(l=t[he]=new Set);var a=e+"__bubble";l.has(a)||(Qh(t,e,2,!1),l.add(a))}function Cc(e,t,l){var a=0;t&&(a|=4),Qh(l,e,a,t)}var ai="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[ai]){e[ai]=!0,Le.forEach(function(l){l!=="selectionchange"&&(Yg.has(l)||Cc(l,!1,e),Cc(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ai]||(t[ai]=!0,Cc("selectionchange",!1,t))}}function Qh(e,t,l,a){switch(ym(t)){case 2:var u=d0;break;case 8:u=h0;break;default:u=Kc}l=u.bind(null,t,l,e),u=void 0,!eo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),a?u!==void 0?e.addEventListener(t,l,{capture:!0,passive:u}):e.addEventListener(t,l,!0):u!==void 0?e.addEventListener(t,l,{passive:u}):e.addEventListener(t,l,!1)}function Nc(e,t,l,a,u){var i=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var s=a.tag;if(s===3||s===4){var m=a.stateNode.containerInfo;if(m===u)break;if(s===4)for(s=a.return;s!==null;){var b=s.tag;if((b===3||b===4)&&s.stateNode.containerInfo===u)return;s=s.return}for(;m!==null;){if(s=Ee(m),s===null)return;if(b=s.tag,b===5||b===6||b===26||b===27){a=i=s;continue e}m=m.parentNode}}a=a.return}Pf(function(){var _=i,Y=Pi(l),Q=[];e:{var N=Ms.get(e);if(N!==void 0){var L=gr,be=e;switch(e){case"keypress":if(yr(l)===0)break e;case"keydown":case"keyup":L=Uv;break;case"focusin":be="focus",L=no;break;case"focusout":be="blur",L=no;break;case"beforeblur":case"afterblur":L=no;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":L=ts;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":L=Ev;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":L=Hv;break;case Rs:case Ts:case xs:L=xv;break;case ws:L=jv;break;case"scroll":case"scrollend":L=bv;break;case"wheel":L=qv;break;case"copy":case"cut":case"paste":L=Mv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":L=as;break;case"toggle":case"beforetoggle":L=Xv}var me=(t&4)!==0,Je=!me&&(e==="scroll"||e==="scrollend"),M=me?N!==null?N+"Capture":null:N;me=[];for(var T=_,O;T!==null;){var G=T;if(O=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||O===null||M===null||(G=Wn(T,M),G!=null&&me.push(Uu(T,G,O))),Je)break;T=T.return}0<me.length&&(N=new L(N,be,null,l,Y),Q.push({event:N,listeners:me}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",L=e==="mouseout"||e==="pointerout",N&&l!==Wi&&(be=l.relatedTarget||l.fromElement)&&(Ee(be)||be[re]))break e;if((L||N)&&(N=Y.window===Y?Y:(N=Y.ownerDocument)?N.defaultView||N.parentWindow:window,L?(be=l.relatedTarget||l.toElement,L=_,be=be?Ee(be):null,be!==null&&(Je=d(be),me=be.tag,be!==Je||me!==5&&me!==27&&me!==6)&&(be=null)):(L=null,be=_),L!==be)){if(me=ts,G="onMouseLeave",M="onMouseEnter",T="mouse",(e==="pointerout"||e==="pointerover")&&(me=as,G="onPointerLeave",M="onPointerEnter",T="pointer"),Je=L==null?N:We(L),O=be==null?N:We(be),N=new me(G,T+"leave",L,l,Y),N.target=Je,N.relatedTarget=O,G=null,Ee(Y)===_&&(me=new me(M,T+"enter",be,l,Y),me.target=O,me.relatedTarget=Je,G=me),Je=G,L&&be)t:{for(me=L,M=be,T=0,O=me;O;O=Nn(O))T++;for(O=0,G=M;G;G=Nn(G))O++;for(;0<T-O;)me=Nn(me),T--;for(;0<O-T;)M=Nn(M),O--;for(;T--;){if(me===M||M!==null&&me===M.alternate)break t;me=Nn(me),M=Nn(M)}me=null}else me=null;L!==null&&Zh(Q,N,L,me,!1),be!==null&&Je!==null&&Zh(Q,Je,be,me,!0)}}e:{if(N=_?We(_):window,L=N.nodeName&&N.nodeName.toLowerCase(),L==="select"||L==="input"&&N.type==="file")var te=ss;else if(cs(N))if(ds)te=Pv;else{te=Fv;var _e=$v}else L=N.nodeName,!L||L.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?_&&Fi(_.elementType)&&(te=ss):te=Wv;if(te&&(te=te(e,_))){fs(Q,te,l,Y);break e}_e&&_e(e,N,_),e==="focusout"&&_&&N.type==="number"&&_.memoizedProps.value!=null&&$i(N,"number",N.value)}switch(_e=_?We(_):window,e){case"focusin":(cs(_e)||_e.contentEditable==="true")&&(fn=_e,fo=_,uu=null);break;case"focusout":uu=fo=fn=null;break;case"mousedown":so=!0;break;case"contextmenu":case"mouseup":case"dragend":so=!1,Ss(Q,l,Y);break;case"selectionchange":if(eg)break;case"keydown":case"keyup":Ss(Q,l,Y)}var fe;if(ro)e:{switch(e){case"compositionstart":var ye="onCompositionStart";break e;case"compositionend":ye="onCompositionEnd";break e;case"compositionupdate":ye="onCompositionUpdate";break e}ye=void 0}else cn?is(e,l)&&(ye="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(ye="onCompositionStart");ye&&(ns&&l.locale!=="ko"&&(cn||ye!=="onCompositionStart"?ye==="onCompositionEnd"&&cn&&(fe=If()):($l=Y,to="value"in $l?$l.value:$l.textContent,cn=!0)),_e=ni(_,ye),0<_e.length&&(ye=new ls(ye,e,null,l,Y),Q.push({event:ye,listeners:_e}),fe?ye.data=fe:(fe=os(l),fe!==null&&(ye.data=fe)))),(fe=Vv?Qv(e,l):Zv(e,l))&&(ye=ni(_,"onBeforeInput"),0<ye.length&&(_e=new ls("onBeforeInput","beforeinput",null,l,Y),Q.push({event:_e,listeners:ye}),_e.data=fe)),Hg(Q,e,_,l,Y)}Vh(Q,t)})}function Uu(e,t,l){return{instance:e,listener:t,currentTarget:l}}function ni(e,t){for(var l=t+"Capture",a=[];e!==null;){var u=e,i=u.stateNode;if(u=u.tag,u!==5&&u!==26&&u!==27||i===null||(u=Wn(e,l),u!=null&&a.unshift(Uu(e,u,i)),u=Wn(e,t),u!=null&&a.push(Uu(e,u,i))),e.tag===3)return a;e=e.return}return[]}function Nn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Zh(e,t,l,a,u){for(var i=t._reactName,s=[];l!==null&&l!==a;){var m=l,b=m.alternate,_=m.stateNode;if(m=m.tag,b!==null&&b===a)break;m!==5&&m!==26&&m!==27||_===null||(b=_,u?(_=Wn(l,i),_!=null&&s.unshift(Uu(l,_,b))):u||(_=Wn(l,i),_!=null&&s.push(Uu(l,_,b)))),l=l.return}s.length!==0&&e.push({event:t,listeners:s})}var qg=/\r\n?/g,Gg=/\u0000|\uFFFD/g;function Kh(e){return(typeof e=="string"?e:""+e).replace(qg,`
`).replace(Gg,"")}function Jh(e,t){return t=Kh(t),Kh(e)===t}function ui(){}function Ke(e,t,l,a,u,i){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||un(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&un(e,""+a);break;case"className":wl(e,"class",a);break;case"tabIndex":wl(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":wl(e,l,a);break;case"style":Ff(e,a,i);break;case"data":if(t!=="object"){wl(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=hr(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(t!=="input"&&Ke(e,t,"name",u.name,u,null),Ke(e,t,"formEncType",u.formEncType,u,null),Ke(e,t,"formMethod",u.formMethod,u,null),Ke(e,t,"formTarget",u.formTarget,u,null)):(Ke(e,t,"encType",u.encType,u,null),Ke(e,t,"method",u.method,u,null),Ke(e,t,"target",u.target,u,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=hr(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=ui);break;case"onScroll":a!=null&&Ne("scroll",e);break;case"onScrollEnd":a!=null&&Ne("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(u.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=hr(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":Ne("beforetoggle",e),Ne("toggle",e),Kt(e,"popover",a);break;case"xlinkActuate":De(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":De(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":De(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":De(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":De(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":De(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":De(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":De(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":De(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Kt(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=gv.get(l)||l,Kt(e,l,a))}}function Lc(e,t,l,a,u,i){switch(l){case"style":Ff(e,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(u.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"children":typeof a=="string"?un(e,a):(typeof a=="number"||typeof a=="bigint")&&un(e,""+a);break;case"onScroll":a!=null&&Ne("scroll",e);break;case"onScrollEnd":a!=null&&Ne("scrollend",e);break;case"onClick":a!=null&&(e.onclick=ui);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ql.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(u=l.endsWith("Capture"),t=l.slice(2,u?l.length-7:void 0),i=e[$]||null,i=i!=null?i[l]:null,typeof i=="function"&&e.removeEventListener(t,i,u),typeof a=="function")){typeof i!="function"&&i!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,u);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Kt(e,l,a)}}}function Rt(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ne("error",e),Ne("load",e);var a=!1,u=!1,i;for(i in l)if(l.hasOwnProperty(i)){var s=l[i];if(s!=null)switch(i){case"src":a=!0;break;case"srcSet":u=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ke(e,t,i,s,l,null)}}u&&Ke(e,t,"srcSet",l.srcSet,l,null),a&&Ke(e,t,"src",l.src,l,null);return;case"input":Ne("invalid",e);var m=i=s=u=null,b=null,_=null;for(a in l)if(l.hasOwnProperty(a)){var Y=l[a];if(Y!=null)switch(a){case"name":u=Y;break;case"type":s=Y;break;case"checked":b=Y;break;case"defaultChecked":_=Y;break;case"value":i=Y;break;case"defaultValue":m=Y;break;case"children":case"dangerouslySetInnerHTML":if(Y!=null)throw Error(c(137,t));break;default:Ke(e,t,a,Y,l,null)}}Zf(e,i,m,b,_,s,u,!1),sr(e);return;case"select":Ne("invalid",e),a=s=i=null;for(u in l)if(l.hasOwnProperty(u)&&(m=l[u],m!=null))switch(u){case"value":i=m;break;case"defaultValue":s=m;break;case"multiple":a=m;default:Ke(e,t,u,m,l,null)}t=i,l=s,e.multiple=!!a,t!=null?nn(e,!!a,t,!1):l!=null&&nn(e,!!a,l,!0);return;case"textarea":Ne("invalid",e),i=u=a=null;for(s in l)if(l.hasOwnProperty(s)&&(m=l[s],m!=null))switch(s){case"value":a=m;break;case"defaultValue":u=m;break;case"children":i=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(c(91));break;default:Ke(e,t,s,m,l,null)}Jf(e,a,u,i),sr(e);return;case"option":for(b in l)if(l.hasOwnProperty(b)&&(a=l[b],a!=null))switch(b){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Ke(e,t,b,a,l,null)}return;case"dialog":Ne("beforetoggle",e),Ne("toggle",e),Ne("cancel",e),Ne("close",e);break;case"iframe":case"object":Ne("load",e);break;case"video":case"audio":for(a=0;a<Cu.length;a++)Ne(Cu[a],e);break;case"image":Ne("error",e),Ne("load",e);break;case"details":Ne("toggle",e);break;case"embed":case"source":case"link":Ne("error",e),Ne("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(_ in l)if(l.hasOwnProperty(_)&&(a=l[_],a!=null))switch(_){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Ke(e,t,_,a,l,null)}return;default:if(Fi(t)){for(Y in l)l.hasOwnProperty(Y)&&(a=l[Y],a!==void 0&&Lc(e,t,Y,a,l,void 0));return}}for(m in l)l.hasOwnProperty(m)&&(a=l[m],a!=null&&Ke(e,t,m,a,l,null))}function Xg(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var u=null,i=null,s=null,m=null,b=null,_=null,Y=null;for(L in l){var Q=l[L];if(l.hasOwnProperty(L)&&Q!=null)switch(L){case"checked":break;case"value":break;case"defaultValue":b=Q;default:a.hasOwnProperty(L)||Ke(e,t,L,null,a,Q)}}for(var N in a){var L=a[N];if(Q=l[N],a.hasOwnProperty(N)&&(L!=null||Q!=null))switch(N){case"type":i=L;break;case"name":u=L;break;case"checked":_=L;break;case"defaultChecked":Y=L;break;case"value":s=L;break;case"defaultValue":m=L;break;case"children":case"dangerouslySetInnerHTML":if(L!=null)throw Error(c(137,t));break;default:L!==Q&&Ke(e,t,N,L,a,Q)}}Ji(e,s,m,b,_,Y,i,u);return;case"select":L=s=m=N=null;for(i in l)if(b=l[i],l.hasOwnProperty(i)&&b!=null)switch(i){case"value":break;case"multiple":L=b;default:a.hasOwnProperty(i)||Ke(e,t,i,null,a,b)}for(u in a)if(i=a[u],b=l[u],a.hasOwnProperty(u)&&(i!=null||b!=null))switch(u){case"value":N=i;break;case"defaultValue":m=i;break;case"multiple":s=i;default:i!==b&&Ke(e,t,u,i,a,b)}t=m,l=s,a=L,N!=null?nn(e,!!l,N,!1):!!a!=!!l&&(t!=null?nn(e,!!l,t,!0):nn(e,!!l,l?[]:"",!1));return;case"textarea":L=N=null;for(m in l)if(u=l[m],l.hasOwnProperty(m)&&u!=null&&!a.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:Ke(e,t,m,null,a,u)}for(s in a)if(u=a[s],i=l[s],a.hasOwnProperty(s)&&(u!=null||i!=null))switch(s){case"value":N=u;break;case"defaultValue":L=u;break;case"children":break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(c(91));break;default:u!==i&&Ke(e,t,s,u,a,i)}Kf(e,N,L);return;case"option":for(var be in l)if(N=l[be],l.hasOwnProperty(be)&&N!=null&&!a.hasOwnProperty(be))switch(be){case"selected":e.selected=!1;break;default:Ke(e,t,be,null,a,N)}for(b in a)if(N=a[b],L=l[b],a.hasOwnProperty(b)&&N!==L&&(N!=null||L!=null))switch(b){case"selected":e.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:Ke(e,t,b,N,a,L)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var me in l)N=l[me],l.hasOwnProperty(me)&&N!=null&&!a.hasOwnProperty(me)&&Ke(e,t,me,null,a,N);for(_ in a)if(N=a[_],L=l[_],a.hasOwnProperty(_)&&N!==L&&(N!=null||L!=null))switch(_){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(c(137,t));break;default:Ke(e,t,_,N,a,L)}return;default:if(Fi(t)){for(var Je in l)N=l[Je],l.hasOwnProperty(Je)&&N!==void 0&&!a.hasOwnProperty(Je)&&Lc(e,t,Je,void 0,a,N);for(Y in a)N=a[Y],L=l[Y],!a.hasOwnProperty(Y)||N===L||N===void 0&&L===void 0||Lc(e,t,Y,N,a,L);return}}for(var M in l)N=l[M],l.hasOwnProperty(M)&&N!=null&&!a.hasOwnProperty(M)&&Ke(e,t,M,null,a,N);for(Q in a)N=a[Q],L=l[Q],!a.hasOwnProperty(Q)||N===L||N==null&&L==null||Ke(e,t,Q,N,a,L)}var Hc=null,Bc=null;function ri(e){return e.nodeType===9?e:e.ownerDocument}function $h(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Fh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function jc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yc=null;function kg(){var e=window.event;return e&&e.type==="popstate"?e===Yc?!1:(Yc=e,!0):(Yc=null,!1)}var Wh=typeof setTimeout=="function"?setTimeout:void 0,Vg=typeof clearTimeout=="function"?clearTimeout:void 0,Ph=typeof Promise=="function"?Promise:void 0,Qg=typeof queueMicrotask=="function"?queueMicrotask:typeof Ph<"u"?function(e){return Ph.resolve(null).then(e).catch(Zg)}:Wh;function Zg(e){setTimeout(function(){throw e})}function sa(e){return e==="head"}function Ih(e,t){var l=t,a=0,u=0;do{var i=l.nextSibling;if(e.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var s=e.ownerDocument;if(l&1&&Nu(s.documentElement),l&2&&Nu(s.body),l&4)for(l=s.head,Nu(l),s=l.firstChild;s;){var m=s.nextSibling,b=s.nodeName;s[ge]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&s.rel.toLowerCase()==="stylesheet"||l.removeChild(s),s=m}}if(u===0){e.removeChild(i),Xu(t);return}u--}else l==="$"||l==="$?"||l==="$!"?u++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);Xu(t)}function qc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":qc(l),pe(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function Kg(e,t,l,a){for(;e.nodeType===1;){var u=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[ge])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==u.rel||e.getAttribute("href")!==(u.href==null||u.href===""?null:u.href)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin)||e.getAttribute("title")!==(u.title==null?null:u.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(u.src==null?null:u.src)||e.getAttribute("type")!==(u.type==null?null:u.type)||e.getAttribute("crossorigin")!==(u.crossOrigin==null?null:u.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=u.name==null?null:""+u.name;if(u.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=dl(e.nextSibling),e===null)break}return null}function Jg(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=dl(e.nextSibling),e===null))return null;return e}function Gc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function $g(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function dl(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Xc=null;function em(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function tm(e,t,l){switch(t=ri(l),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function Nu(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);pe(e)}var tl=new Map,lm=new Set;function ii(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Yl=J.d;J.d={f:Fg,r:Wg,D:Pg,C:Ig,L:e0,m:t0,X:a0,S:l0,M:n0};function Fg(){var e=Yl.f(),t=Pr();return e||t}function Wg(e){var t=je(e);t!==null&&t.tag===5&&t.type==="form"?Rd(t):Yl.r(e)}var Ln=typeof document>"u"?null:document;function am(e,t,l){var a=Ln;if(a&&typeof t=="string"&&t){var u=Jt(t);u='link[rel="'+e+'"][href="'+u+'"]',typeof l=="string"&&(u+='[crossorigin="'+l+'"]'),lm.has(u)||(lm.add(u),e={rel:e,crossOrigin:l,href:t},a.querySelector(u)===null&&(t=a.createElement("link"),Rt(t,"link",e),qe(t),a.head.appendChild(t)))}}function Pg(e){Yl.D(e),am("dns-prefetch",e,null)}function Ig(e,t){Yl.C(e,t),am("preconnect",e,t)}function e0(e,t,l){Yl.L(e,t,l);var a=Ln;if(a&&e&&t){var u='link[rel="preload"][as="'+Jt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(u+='[imagesrcset="'+Jt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(u+='[imagesizes="'+Jt(l.imageSizes)+'"]')):u+='[href="'+Jt(e)+'"]';var i=u;switch(t){case"style":i=Hn(e);break;case"script":i=Bn(e)}tl.has(i)||(e=p({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),tl.set(i,e),a.querySelector(u)!==null||t==="style"&&a.querySelector(Lu(i))||t==="script"&&a.querySelector(Hu(i))||(t=a.createElement("link"),Rt(t,"link",e),qe(t),a.head.appendChild(t)))}}function t0(e,t){Yl.m(e,t);var l=Ln;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",u='link[rel="modulepreload"][as="'+Jt(a)+'"][href="'+Jt(e)+'"]',i=u;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Bn(e)}if(!tl.has(i)&&(e=p({rel:"modulepreload",href:e},t),tl.set(i,e),l.querySelector(u)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Hu(i)))return}a=l.createElement("link"),Rt(a,"link",e),qe(a),l.head.appendChild(a)}}}function l0(e,t,l){Yl.S(e,t,l);var a=Ln;if(a&&e){var u=lt(a).hoistableStyles,i=Hn(e);t=t||"default";var s=u.get(i);if(!s){var m={loading:0,preload:null};if(s=a.querySelector(Lu(i)))m.loading=5;else{e=p({rel:"stylesheet",href:e,"data-precedence":t},l),(l=tl.get(i))&&kc(e,l);var b=s=a.createElement("link");qe(b),Rt(b,"link",e),b._p=new Promise(function(_,Y){b.onload=_,b.onerror=Y}),b.addEventListener("load",function(){m.loading|=1}),b.addEventListener("error",function(){m.loading|=2}),m.loading|=4,oi(s,t,a)}s={type:"stylesheet",instance:s,count:1,state:m},u.set(i,s)}}}function a0(e,t){Yl.X(e,t);var l=Ln;if(l&&e){var a=lt(l).hoistableScripts,u=Bn(e),i=a.get(u);i||(i=l.querySelector(Hu(u)),i||(e=p({src:e,async:!0},t),(t=tl.get(u))&&Vc(e,t),i=l.createElement("script"),qe(i),Rt(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(u,i))}}function n0(e,t){Yl.M(e,t);var l=Ln;if(l&&e){var a=lt(l).hoistableScripts,u=Bn(e),i=a.get(u);i||(i=l.querySelector(Hu(u)),i||(e=p({src:e,async:!0,type:"module"},t),(t=tl.get(u))&&Vc(e,t),i=l.createElement("script"),qe(i),Rt(i,"link",e),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(u,i))}}function nm(e,t,l,a){var u=(u=se.current)?ii(u):null;if(!u)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Hn(l.href),l=lt(u).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Hn(l.href);var i=lt(u).hoistableStyles,s=i.get(e);if(s||(u=u.ownerDocument||u,s={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,s),(i=u.querySelector(Lu(e)))&&!i._p&&(s.instance=i,s.state.loading=5),tl.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},tl.set(e,l),i||u0(u,e,l,s.state))),t&&a===null)throw Error(c(528,""));return s}if(t&&a!==null)throw Error(c(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Bn(l),l=lt(u).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Hn(e){return'href="'+Jt(e)+'"'}function Lu(e){return'link[rel="stylesheet"]['+e+"]"}function um(e){return p({},e,{"data-precedence":e.precedence,precedence:null})}function u0(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Rt(t,"link",l),qe(t),e.head.appendChild(t))}function Bn(e){return'[src="'+Jt(e)+'"]'}function Hu(e){return"script[async]"+e}function rm(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Jt(l.href)+'"]');if(a)return t.instance=a,qe(a),a;var u=p({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),qe(a),Rt(a,"style",u),oi(a,l.precedence,e),t.instance=a;case"stylesheet":u=Hn(l.href);var i=e.querySelector(Lu(u));if(i)return t.state.loading|=4,t.instance=i,qe(i),i;a=um(l),(u=tl.get(u))&&kc(a,u),i=(e.ownerDocument||e).createElement("link"),qe(i);var s=i;return s._p=new Promise(function(m,b){s.onload=m,s.onerror=b}),Rt(i,"link",a),t.state.loading|=4,oi(i,l.precedence,e),t.instance=i;case"script":return i=Bn(l.src),(u=e.querySelector(Hu(i)))?(t.instance=u,qe(u),u):(a=l,(u=tl.get(i))&&(a=p({},l),Vc(a,u)),e=e.ownerDocument||e,u=e.createElement("script"),qe(u),Rt(u,"link",a),e.head.appendChild(u),t.instance=u);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,oi(a,l.precedence,e));return t.instance}function oi(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),u=a.length?a[a.length-1]:null,i=u,s=0;s<a.length;s++){var m=a[s];if(m.dataset.precedence===t)i=m;else if(i!==u)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function kc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Vc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ci=null;function im(e,t,l){if(ci===null){var a=new Map,u=ci=new Map;u.set(l,a)}else u=ci,a=u.get(l),a||(a=new Map,u.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),u=0;u<l.length;u++){var i=l[u];if(!(i[ge]||i[Z]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var s=i.getAttribute(t)||"";s=e+s;var m=a.get(s);m?m.push(i):a.set(s,[i])}}return a}function om(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function r0(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function cm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Bu=null;function i0(){}function o0(e,t,l){if(Bu===null)throw Error(c(475));var a=Bu;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var u=Hn(l.href),i=e.querySelector(Lu(u));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=fi.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=i,qe(i);return}i=e.ownerDocument||e,l=um(l),(u=tl.get(u))&&kc(l,u),i=i.createElement("link"),qe(i);var s=i;s._p=new Promise(function(m,b){s.onload=m,s.onerror=b}),Rt(i,"link",l),t.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=fi.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function c0(){if(Bu===null)throw Error(c(475));var e=Bu;return e.stylesheets&&e.count===0&&Qc(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&Qc(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function fi(){if(this.count--,this.count===0){if(this.stylesheets)Qc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var si=null;function Qc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,si=new Map,t.forEach(f0,e),si=null,fi.call(e))}function f0(e,t){if(!(t.state.loading&4)){var l=si.get(e);if(l)var a=l.get(null);else{l=new Map,si.set(e,l);for(var u=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<u.length;i++){var s=u[i];(s.nodeName==="LINK"||s.getAttribute("media")!=="not all")&&(l.set(s.dataset.precedence,s),a=s)}a&&l.set(null,a)}u=t.instance,s=u.getAttribute("data-precedence"),i=l.get(s)||a,i===a&&l.set(null,u),l.set(s,u),this.count++,a=fi.bind(this),u.addEventListener("load",a),u.addEventListener("error",a),i?i.parentNode.insertBefore(u,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(u,e.firstChild)),t.state.loading|=4}}var ju={$$typeof:K,Provider:null,Consumer:null,_currentValue:X,_currentValue2:X,_threadCount:0};function s0(e,t,l,a,u,i,s,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=an(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=an(0),this.hiddenUpdates=an(null),this.identifierPrefix=a,this.onUncaughtError=u,this.onCaughtError=i,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function fm(e,t,l,a,u,i,s,m,b,_,Y,Q){return e=new s0(e,t,l,s,m,b,_,Q),t=1,i===!0&&(t|=24),i=jt(3,null,null,t),e.current=i,i.stateNode=e,t=Mo(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:t},Do(i),e}function sm(e){return e?(e=mn,e):mn}function dm(e,t,l,a,u,i){u=sm(u),a.context===null?a.context=u:a.pendingContext=u,a=Pl(t),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=Il(e,a,t),l!==null&&(kt(l,e,t),mu(l,e,t))}function hm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function Zc(e,t){hm(e,t),(e=e.alternate)&&hm(e,t)}function mm(e){if(e.tag===13){var t=hn(e,67108864);t!==null&&kt(t,e,67108864),Zc(e,67108864)}}var di=!0;function d0(e,t,l,a){var u=D.T;D.T=null;var i=J.p;try{J.p=2,Kc(e,t,l,a)}finally{J.p=i,D.T=u}}function h0(e,t,l,a){var u=D.T;D.T=null;var i=J.p;try{J.p=8,Kc(e,t,l,a)}finally{J.p=i,D.T=u}}function Kc(e,t,l,a){if(di){var u=Jc(a);if(u===null)Nc(e,t,a,hi,l),vm(e,a);else if(y0(u,e,t,l,a))a.stopPropagation();else if(vm(e,a),t&4&&-1<m0.indexOf(e)){for(;u!==null;){var i=je(u);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var s=xl(i.pendingLanes);if(s!==0){var m=i;for(m.pendingLanes|=2,m.entangledLanes|=2;s;){var b=1<<31-bt(s);m.entanglements[1]|=b,s&=~b}Sl(i),(ke&6)===0&&(Fr=Ht()+500,_u(0))}}break;case 13:m=hn(i,2),m!==null&&kt(m,i,2),Pr(),Zc(i,2)}if(i=Jc(a),i===null&&Nc(e,t,a,hi,l),i===u)break;u=i}u!==null&&a.stopPropagation()}else Nc(e,t,a,null,l)}}function Jc(e){return e=Pi(e),$c(e)}var hi=null;function $c(e){if(hi=null,e=Ee(e),e!==null){var t=d(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=y(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return hi=e,null}function ym(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Zi()){case nr:return 2;case ur:return 8;case tn:case Tl:return 32;case Vl:return 268435456;default:return 32}default:return 32}}var Fc=!1,da=null,ha=null,ma=null,Yu=new Map,qu=new Map,ya=[],m0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function vm(e,t){switch(e){case"focusin":case"focusout":da=null;break;case"dragenter":case"dragleave":ha=null;break;case"mouseover":case"mouseout":ma=null;break;case"pointerover":case"pointerout":Yu.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":qu.delete(t.pointerId)}}function Gu(e,t,l,a,u,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[u]},t!==null&&(t=je(t),t!==null&&mm(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function y0(e,t,l,a,u){switch(t){case"focusin":return da=Gu(da,e,t,l,a,u),!0;case"dragenter":return ha=Gu(ha,e,t,l,a,u),!0;case"mouseover":return ma=Gu(ma,e,t,l,a,u),!0;case"pointerover":var i=u.pointerId;return Yu.set(i,Gu(Yu.get(i)||null,e,t,l,a,u)),!0;case"gotpointercapture":return i=u.pointerId,qu.set(i,Gu(qu.get(i)||null,e,t,l,a,u)),!0}return!1}function gm(e){var t=Ee(e.target);if(t!==null){var l=d(t);if(l!==null){if(t=l.tag,t===13){if(t=y(l),t!==null){e.blockedOn=t,z(e.priority,function(){if(l.tag===13){var a=Xt();a=Kn(a);var u=hn(l,a);u!==null&&kt(u,l,a),Zc(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function mi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=Jc(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);Wi=a,l.target.dispatchEvent(a),Wi=null}else return t=je(l),t!==null&&mm(t),e.blockedOn=l,!1;t.shift()}return!0}function pm(e,t,l){mi(e)&&l.delete(t)}function v0(){Fc=!1,da!==null&&mi(da)&&(da=null),ha!==null&&mi(ha)&&(ha=null),ma!==null&&mi(ma)&&(ma=null),Yu.forEach(pm),qu.forEach(pm)}function yi(e,t){e.blockedOn===t&&(e.blockedOn=null,Fc||(Fc=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,v0)))}var vi=null;function bm(e){vi!==e&&(vi=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){vi===e&&(vi=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],u=e[t+2];if(typeof a!="function"){if($c(a||l)===null)continue;break}var i=je(l);i!==null&&(e.splice(t,3),t-=3,$o(i,{pending:!0,data:u,method:l.method,action:a},a,u))}}))}function Xu(e){function t(b){return yi(b,e)}da!==null&&yi(da,e),ha!==null&&yi(ha,e),ma!==null&&yi(ma,e),Yu.forEach(t),qu.forEach(t);for(var l=0;l<ya.length;l++){var a=ya[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<ya.length&&(l=ya[0],l.blockedOn===null);)gm(l),l.blockedOn===null&&ya.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var u=l[a],i=l[a+1],s=u[$]||null;if(typeof i=="function")s||bm(l);else if(s){var m=null;if(i&&i.hasAttribute("formAction")){if(u=i,s=i[$]||null)m=s.formAction;else if($c(u)!==null)continue}else m=s.action;typeof m=="function"?l[a+1]=m:(l.splice(a,3),a-=3),bm(l)}}}function Wc(e){this._internalRoot=e}gi.prototype.render=Wc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var l=t.current,a=Xt();dm(l,a,e,t,null,null)},gi.prototype.unmount=Wc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dm(e.current,2,null,e,null,null),Pr(),t[re]=null}};function gi(e){this._internalRoot=e}gi.prototype.unstable_scheduleHydration=function(e){if(e){var t=R();e={blockedOn:null,target:e,priority:t};for(var l=0;l<ya.length&&t!==0&&t<ya[l].priority;l++);ya.splice(l,0,e),l===0&&gm(e)}};var Sm=r.version;if(Sm!=="19.1.0")throw Error(c(527,Sm,"19.1.0"));J.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=h(t),e=e!==null?v(e):null,e=e===null?null:e.stateNode,e};var g0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var pi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!pi.isDisabled&&pi.supportsFiber)try{Ot=pi.inject(g0),tt=pi}catch{}}return Vu.createRoot=function(e,t){if(!f(e))throw Error(c(299));var l=!1,a="",u=Hd,i=Bd,s=jd,m=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(s=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=fm(e,1,!1,null,null,l,a,u,i,s,m,null),e[re]=t.current,Uc(e),new Wc(t)},Vu.hydrateRoot=function(e,t,l){if(!f(e))throw Error(c(299));var a=!1,u="",i=Hd,s=Bd,m=jd,b=null,_=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(u=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(s=l.onCaughtError),l.onRecoverableError!==void 0&&(m=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(b=l.unstable_transitionCallbacks),l.formState!==void 0&&(_=l.formState)),t=fm(e,1,!0,t,l??null,a,u,i,s,m,b,_),t.context=sm(null),l=t.current,a=Xt(),a=Kn(a),u=Pl(a),u.callback=null,Il(l,u,a),l=a,t.current.lanes=l,Oa(t,l),Sl(t),e[re]=t.current,Uc(e),new gi(t)},Vu.version="19.1.0",Vu}var _m;function C0(){if(_m)return ef.exports;_m=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),ef.exports=_0(),ef.exports}var OS=C0(),Qu={},Cm;function U0(){if(Cm)return Qu;Cm=1,Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.parse=y,Qu.serialize=v;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,f=Object.prototype.toString,d=(()=>{const x=function(){};return x.prototype=Object.create(null),x})();function y(x,C){const w=new d,j=x.length;if(j<2)return w;const k=C?.decode||p;let B=0;do{const ne=x.indexOf("=",B);if(ne===-1)break;const K=x.indexOf(";",B),ue=K===-1?j:K;if(ne>ue){B=x.lastIndexOf(";",ne-1)+1;continue}const P=g(x,B,ne),U=h(x,ne,P),oe=x.slice(P,U);if(w[oe]===void 0){let F=g(x,ne+1,ue),ce=h(x,ue,F);const Me=k(x.slice(F,ce));w[oe]=Me}B=ue+1}while(B<j);return w}function g(x,C,w){do{const j=x.charCodeAt(C);if(j!==32&&j!==9)return C}while(++C<w);return w}function h(x,C,w){for(;C>w;){const j=x.charCodeAt(--C);if(j!==32&&j!==9)return C+1}return w}function v(x,C,w){const j=w?.encode||encodeURIComponent;if(!n.test(x))throw new TypeError(`argument name is invalid: ${x}`);const k=j(C);if(!r.test(k))throw new TypeError(`argument val is invalid: ${C}`);let B=x+"="+k;if(!w)return B;if(w.maxAge!==void 0){if(!Number.isInteger(w.maxAge))throw new TypeError(`option maxAge is invalid: ${w.maxAge}`);B+="; Max-Age="+w.maxAge}if(w.domain){if(!o.test(w.domain))throw new TypeError(`option domain is invalid: ${w.domain}`);B+="; Domain="+w.domain}if(w.path){if(!c.test(w.path))throw new TypeError(`option path is invalid: ${w.path}`);B+="; Path="+w.path}if(w.expires){if(!A(w.expires)||!Number.isFinite(w.expires.valueOf()))throw new TypeError(`option expires is invalid: ${w.expires}`);B+="; Expires="+w.expires.toUTCString()}if(w.httpOnly&&(B+="; HttpOnly"),w.secure&&(B+="; Secure"),w.partitioned&&(B+="; Partitioned"),w.priority)switch(typeof w.priority=="string"?w.priority.toLowerCase():void 0){case"low":B+="; Priority=Low";break;case"medium":B+="; Priority=Medium";break;case"high":B+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${w.priority}`)}if(w.sameSite)switch(typeof w.sameSite=="string"?w.sameSite.toLowerCase():w.sameSite){case!0:case"strict":B+="; SameSite=Strict";break;case"lax":B+="; SameSite=Lax";break;case"none":B+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${w.sameSite}`)}return B}function p(x){if(x.indexOf("%")===-1)return x;try{return decodeURIComponent(x)}catch{return x}}function A(x){return f.call(x)==="[object Date]"}return Qu}U0();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var my=n=>{throw TypeError(n)},N0=(n,r,o)=>r.has(n)||my("Cannot "+o),nf=(n,r,o)=>(N0(n,r,"read from private field"),o?o.call(n):r.get(n)),L0=(n,r,o)=>r.has(n)?my("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,o),Um="popstate";function H0(n={}){function r(c,f){let{pathname:d,search:y,hash:g}=c.location;return Iu("",{pathname:d,search:y,hash:g},f.state&&f.state.usr||null,f.state&&f.state.key||"default")}function o(c,f){return typeof f=="string"?f:xa(f)}return j0(r,o,null,n)}function Ce(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function rt(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function B0(){return Math.random().toString(36).substring(2,10)}function Nm(n,r){return{usr:n.state,key:n.key,idx:r}}function Iu(n,r,o=null,c){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof r=="string"?wa(r):r,state:o,key:r&&r.key||c||B0()}}function xa({pathname:n="/",search:r="",hash:o=""}){return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),o&&o!=="#"&&(n+=o.charAt(0)==="#"?o:"#"+o),n}function wa(n){let r={};if(n){let o=n.indexOf("#");o>=0&&(r.hash=n.substring(o),n=n.substring(0,o));let c=n.indexOf("?");c>=0&&(r.search=n.substring(c),n=n.substring(0,c)),n&&(r.pathname=n)}return r}function j0(n,r,o,c={}){let{window:f=document.defaultView,v5Compat:d=!1}=c,y=f.history,g="POP",h=null,v=p();v==null&&(v=0,y.replaceState({...y.state,idx:v},""));function p(){return(y.state||{idx:null}).idx}function A(){g="POP";let k=p(),B=k==null?null:k-v;v=k,h&&h({action:g,location:j.location,delta:B})}function x(k,B){g="PUSH";let ne=Iu(j.location,k,B);v=p()+1;let K=Nm(ne,v),ue=j.createHref(ne);try{y.pushState(K,"",ue)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;f.location.assign(ue)}d&&h&&h({action:g,location:j.location,delta:1})}function C(k,B){g="REPLACE";let ne=Iu(j.location,k,B);v=p();let K=Nm(ne,v),ue=j.createHref(ne);y.replaceState(K,"",ue),d&&h&&h({action:g,location:j.location,delta:0})}function w(k){return yy(k)}let j={get action(){return g},get location(){return n(f,y)},listen(k){if(h)throw new Error("A history only accepts one active listener");return f.addEventListener(Um,A),h=k,()=>{f.removeEventListener(Um,A),h=null}},createHref(k){return r(f,k)},createURL:w,encodeLocation(k){let B=w(k);return{pathname:B.pathname,search:B.search,hash:B.hash}},push:x,replace:C,go(k){return y.go(k)}};return j}function yy(n,r=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),Ce(o,"No window.location.(origin|href) available to create URL");let c=typeof n=="string"?n:xa(n);return c=c.replace(/ $/,"%20"),!r&&c.startsWith("//")&&(c=o+c),new URL(c,o)}var Fu,Lm=class{constructor(n){if(L0(this,Fu,new Map),n)for(let[r,o]of n)this.set(r,o)}get(n){if(nf(this,Fu).has(n))return nf(this,Fu).get(n);if(n.defaultValue!==void 0)return n.defaultValue;throw new Error("No value found for context")}set(n,r){nf(this,Fu).set(n,r)}};Fu=new WeakMap;var Y0=new Set(["lazy","caseSensitive","path","id","index","children"]);function q0(n){return Y0.has(n)}var G0=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function X0(n){return G0.has(n)}function k0(n){return n.index===!0}function _i(n,r,o=[],c={}){return n.map((f,d)=>{let y=[...o,String(d)],g=typeof f.id=="string"?f.id:y.join("-");if(Ce(f.index!==!0||!f.children,"Cannot specify children on an index route"),Ce(!c[g],`Found a route id collision on id "${g}".  Route id's must be globally unique within Data Router usages`),k0(f)){let h={...f,...r(f),id:g};return c[g]=h,h}else{let h={...f,...r(f),id:g,children:void 0};return c[g]=h,f.children&&(h.children=_i(f.children,r,y,c)),h}})}function ba(n,r,o="/"){return Ai(n,r,o,!1)}function Ai(n,r,o,c){let f=typeof r=="string"?wa(r):r,d=nl(f.pathname||"/",o);if(d==null)return null;let y=vy(n);Q0(y);let g=null;for(let h=0;g==null&&h<y.length;++h){let v=lp(d);g=ep(y[h],v,c)}return g}function V0(n,r){let{route:o,pathname:c,params:f}=n;return{id:o.id,pathname:c,params:f,data:r[o.id],handle:o.handle}}function vy(n,r=[],o=[],c=""){let f=(d,y,g)=>{let h={relativePath:g===void 0?d.path||"":g,caseSensitive:d.caseSensitive===!0,childrenIndex:y,route:d};h.relativePath.startsWith("/")&&(Ce(h.relativePath.startsWith(c),`Absolute route path "${h.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),h.relativePath=h.relativePath.slice(c.length));let v=El([c,h.relativePath]),p=o.concat(h);d.children&&d.children.length>0&&(Ce(d.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${v}".`),vy(d.children,r,p,v)),!(d.path==null&&!d.index)&&r.push({path:v,score:P0(v,d.index),routesMeta:p})};return n.forEach((d,y)=>{if(d.path===""||!d.path?.includes("?"))f(d,y);else for(let g of gy(d.path))f(d,y,g)}),r}function gy(n){let r=n.split("/");if(r.length===0)return[];let[o,...c]=r,f=o.endsWith("?"),d=o.replace(/\?$/,"");if(c.length===0)return f?[d,""]:[d];let y=gy(c.join("/")),g=[];return g.push(...y.map(h=>h===""?d:[d,h].join("/"))),f&&g.push(...y),g.map(h=>n.startsWith("/")&&h===""?"/":h)}function Q0(n){n.sort((r,o)=>r.score!==o.score?o.score-r.score:I0(r.routesMeta.map(c=>c.childrenIndex),o.routesMeta.map(c=>c.childrenIndex)))}var Z0=/^:[\w-]+$/,K0=3,J0=2,$0=1,F0=10,W0=-2,Hm=n=>n==="*";function P0(n,r){let o=n.split("/"),c=o.length;return o.some(Hm)&&(c+=W0),r&&(c+=J0),o.filter(f=>!Hm(f)).reduce((f,d)=>f+(Z0.test(d)?K0:d===""?$0:F0),c)}function I0(n,r){return n.length===r.length&&n.slice(0,-1).every((c,f)=>c===r[f])?n[n.length-1]-r[r.length-1]:0}function ep(n,r,o=!1){let{routesMeta:c}=n,f={},d="/",y=[];for(let g=0;g<c.length;++g){let h=c[g],v=g===c.length-1,p=d==="/"?r:r.slice(d.length)||"/",A=Ci({path:h.relativePath,caseSensitive:h.caseSensitive,end:v},p),x=h.route;if(!A&&v&&o&&!c[c.length-1].route.index&&(A=Ci({path:h.relativePath,caseSensitive:h.caseSensitive,end:!1},p)),!A)return null;Object.assign(f,A.params),y.push({params:f,pathname:El([d,A.pathname]),pathnameBase:up(El([d,A.pathnameBase])),route:x}),A.pathnameBase!=="/"&&(d=El([d,A.pathnameBase]))}return y}function Ci(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[o,c]=tp(n.path,n.caseSensitive,n.end),f=r.match(o);if(!f)return null;let d=f[0],y=d.replace(/(.)\/+$/,"$1"),g=f.slice(1);return{params:c.reduce((v,{paramName:p,isOptional:A},x)=>{if(p==="*"){let w=g[x]||"";y=d.slice(0,d.length-w.length).replace(/(.)\/+$/,"$1")}const C=g[x];return A&&!C?v[p]=void 0:v[p]=(C||"").replace(/%2F/g,"/"),v},{}),pathname:d,pathnameBase:y,pattern:n}}function tp(n,r=!1,o=!0){rt(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let c=[],f="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(y,g,h)=>(c.push({paramName:g,isOptional:h!=null}),h?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(c.push({paramName:"*"}),f+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?f+="\\/*$":n!==""&&n!=="/"&&(f+="(?:(?=\\/|$))"),[new RegExp(f,r?void 0:"i"),c]}function lp(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return rt(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),n}}function nl(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let o=r.endsWith("/")?r.length-1:r.length,c=n.charAt(o);return c&&c!=="/"?null:n.slice(o)||"/"}function ap(n,r="/"){let{pathname:o,search:c="",hash:f=""}=typeof n=="string"?wa(n):n;return{pathname:o?o.startsWith("/")?o:np(o,r):r,search:rp(c),hash:ip(f)}}function np(n,r){let o=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(f=>{f===".."?o.length>1&&o.pop():f!=="."&&o.push(f)}),o.length>1?o.join("/"):"/"}function uf(n,r,o,c){return`Cannot include a '${n}' character in a manually specified \`to.${r}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function py(n){return n.filter((r,o)=>o===0||r.route.path&&r.route.path.length>0)}function Li(n){let r=py(n);return r.map((o,c)=>c===r.length-1?o.pathname:o.pathnameBase)}function Hi(n,r,o,c=!1){let f;typeof n=="string"?f=wa(n):(f={...n},Ce(!f.pathname||!f.pathname.includes("?"),uf("?","pathname","search",f)),Ce(!f.pathname||!f.pathname.includes("#"),uf("#","pathname","hash",f)),Ce(!f.search||!f.search.includes("#"),uf("#","search","hash",f)));let d=n===""||f.pathname==="",y=d?"/":f.pathname,g;if(y==null)g=o;else{let A=r.length-1;if(!c&&y.startsWith("..")){let x=y.split("/");for(;x[0]==="..";)x.shift(),A-=1;f.pathname=x.join("/")}g=A>=0?r[A]:"/"}let h=ap(f,g),v=y&&y!=="/"&&y.endsWith("/"),p=(d||y===".")&&o.endsWith("/");return!h.pathname.endsWith("/")&&(v||p)&&(h.pathname+="/"),h}var El=n=>n.join("/").replace(/\/\/+/g,"/"),up=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),rp=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,ip=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n,Ui=class{constructor(n,r,o,c=!1){this.status=n,this.statusText=r||"",this.internal=c,o instanceof Error?(this.data=o.toString(),this.error=o):this.data=o}};function er(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var by=["POST","PUT","PATCH","DELETE"],op=new Set(by),cp=["GET",...by],fp=new Set(cp),sp=new Set([301,302,303,307,308]),dp=new Set([307,308]),rf={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},hp={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Zu={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},zf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mp=n=>({hasErrorBoundary:!!n.hasErrorBoundary}),Sy="remix-router-transitions",Ey=Symbol("ResetLoaderData");function yp(n){const r=n.window?n.window:typeof window<"u"?window:void 0,o=typeof r<"u"&&typeof r.document<"u"&&typeof r.document.createElement<"u";Ce(n.routes.length>0,"You must provide a non-empty routes array to createRouter");let c=n.hydrationRouteProperties||[],f=n.mapRouteProperties||mp,d={},y=_i(n.routes,f,void 0,d),g,h=n.basename||"/",v=n.dataStrategy||Sp,p={unstable_middleware:!1,...n.future},A=null,x=new Set,C=null,w=null,j=null,k=n.hydrationData!=null,B=ba(y,n.history.location,h),ne=!1,K=null,ue;if(B==null&&!n.patchRoutesOnNavigation){let R=al(404,{pathname:n.history.location.pathname}),{matches:z,route:H}=Jm(y);ue=!0,B=z,K={[H.id]:R}}else if(B&&!n.hydrationData&&Da(B,y,n.history.location.pathname).active&&(B=null),B)if(B.some(R=>R.route.lazy))ue=!1;else if(!B.some(R=>R.route.loader))ue=!0;else{let R=n.hydrationData?n.hydrationData.loaderData:null,z=n.hydrationData?n.hydrationData.errors:null;if(z){let H=B.findIndex(Z=>z[Z.route.id]!==void 0);ue=B.slice(0,H+1).every(Z=>!vf(Z.route,R,z))}else ue=B.every(H=>!vf(H.route,R,z))}else{ue=!1,B=[];let R=Da(null,y,n.history.location.pathname);R.active&&R.matches&&(ne=!0,B=R.matches)}let P,U={historyAction:n.history.action,location:n.history.location,matches:B,initialized:ue,navigation:rf,restoreScrollPosition:n.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:n.hydrationData&&n.hydrationData.loaderData||{},actionData:n.hydrationData&&n.hydrationData.actionData||null,errors:n.hydrationData&&n.hydrationData.errors||K,fetchers:new Map,blockers:new Map},oe="POP",F=!1,ce,Me=!1,Qe=new Map,Ye=null,Te=!1,xe=!1,we=new Set,D=new Map,J=0,X=-1,Se=new Map,E=new Set,q=new Map,W=new Map,V=new Set,I=new Map,ve,se=null;function Oe(){if(A=n.history.listen(({action:R,location:z,delta:H})=>{if(ve){ve(),ve=void 0;return}rt(I.size===0||H!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let Z=or({currentLocation:U.location,nextLocation:z,historyAction:R});if(Z&&H!=null){let $=new Promise(re=>{ve=re});n.history.go(H*-1),yl(Z,{state:"blocked",location:z,proceed(){yl(Z,{state:"proceeding",proceed:void 0,reset:void 0,location:z}),$.then(()=>n.history.go(H))},reset(){let re=new Map(U.blockers);re.set(Z,Zu),Pe({blockers:re})}});return}return rl(R,z)}),o){_p(r,Qe);let R=()=>Cp(r,Qe);r.addEventListener("pagehide",R),Ye=()=>r.removeEventListener("pagehide",R)}return U.initialized||rl("POP",U.location,{initialHydration:!0}),P}function He(){A&&A(),Ye&&Ye(),x.clear(),ce&&ce.abort(),U.fetchers.forEach((R,z)=>bt(z)),U.blockers.forEach((R,z)=>za(z))}function it(R){return x.add(R),()=>x.delete(R)}function Pe(R,z={}){U={...U,...R};let H=[],Z=[];U.fetchers.forEach(($,re)=>{$.state==="idle"&&(V.has(re)?H.push(re):Z.push(re))}),V.forEach($=>{!U.fetchers.has($)&&!D.has($)&&H.push($)}),[...x].forEach($=>$(U,{deletedFetchers:H,viewTransitionOpts:z.viewTransitionOpts,flushSync:z.flushSync===!0})),H.forEach($=>bt($)),Z.forEach($=>U.fetchers.delete($))}function Mt(R,z,{flushSync:H}={}){let Z=U.actionData!=null&&U.navigation.formMethod!=null&&Qt(U.navigation.formMethod)&&U.navigation.state==="loading"&&R.state?._isRedirect!==!0,$;z.actionData?Object.keys(z.actionData).length>0?$=z.actionData:$=null:Z?$=U.actionData:$=null;let re=z.loaderData?Zm(U.loaderData,z.loaderData,z.matches||[],z.errors):U.loaderData,he=U.blockers;he.size>0&&(he=new Map(he),he.forEach((ie,ge)=>he.set(ge,Zu)));let ee=F===!0||U.navigation.formMethod!=null&&Qt(U.navigation.formMethod)&&R.state?._isRedirect!==!0;g&&(y=g,g=void 0),Te||oe==="POP"||(oe==="PUSH"?n.history.push(R,R.state):oe==="REPLACE"&&n.history.replace(R,R.state));let de;if(oe==="POP"){let ie=Qe.get(U.location.pathname);ie&&ie.has(R.pathname)?de={currentLocation:U.location,nextLocation:R}:Qe.has(R.pathname)&&(de={currentLocation:R,nextLocation:U.location})}else if(Me){let ie=Qe.get(U.location.pathname);ie?ie.add(R.pathname):(ie=new Set([R.pathname]),Qe.set(U.location.pathname,ie)),de={currentLocation:U.location,nextLocation:R}}Pe({...z,actionData:$,loaderData:re,historyAction:oe,location:R,initialized:!0,navigation:rf,revalidation:"idle",restoreScrollPosition:fr(R,z.matches||U.matches),preventScrollReset:ee,blockers:he},{viewTransitionOpts:de,flushSync:H===!0}),oe="POP",F=!1,Me=!1,Te=!1,xe=!1,se?.resolve(),se=null}async function ul(R,z){if(typeof R=="number"){n.history.go(R);return}let H=yf(U.location,U.matches,h,R,z?.fromRouteId,z?.relative),{path:Z,submission:$,error:re}=Bm(!1,H,z),he=U.location,ee=Iu(U.location,Z,z&&z.state);ee={...ee,...n.history.encodeLocation(ee)};let de=z&&z.replace!=null?z.replace:void 0,ie="PUSH";de===!0?ie="REPLACE":de===!1||$!=null&&Qt($.formMethod)&&$.formAction===U.location.pathname+U.location.search&&(ie="REPLACE");let ge=z&&"preventScrollReset"in z?z.preventScrollReset===!0:void 0,pe=(z&&z.flushSync)===!0,Ee=or({currentLocation:he,nextLocation:ee,historyAction:ie});if(Ee){yl(Ee,{state:"blocked",location:ee,proceed(){yl(Ee,{state:"proceeding",proceed:void 0,reset:void 0,location:ee}),ul(R,z)},reset(){let je=new Map(U.blockers);je.set(Ee,Zu),Pe({blockers:je})}});return}await rl(ie,ee,{submission:$,pendingError:re,preventScrollReset:ge,replace:z&&z.replace,enableViewTransition:z&&z.viewTransition,flushSync:pe})}function Qn(){se||(se=Up()),Zn(),Pe({revalidation:"loading"});let R=se.promise;return U.navigation.state==="submitting"?R:U.navigation.state==="idle"?(rl(U.historyAction,U.location,{startUninterruptedRevalidation:!0}),R):(rl(oe||U.historyAction,U.navigation.location,{overrideNavigation:U.navigation,enableViewTransition:Me===!0}),R)}async function rl(R,z,H){ce&&ce.abort(),ce=null,oe=R,Te=(H&&H.startUninterruptedRevalidation)===!0,Oa(U.location,U.matches),F=(H&&H.preventScrollReset)===!0,Me=(H&&H.enableViewTransition)===!0;let Z=g||y,$=H&&H.overrideNavigation,re=H?.initialHydration&&U.matches&&U.matches.length>0&&!ne?U.matches:ba(Z,z,h),he=(H&&H.flushSync)===!0;if(re&&U.initialized&&!xe&&Mp(U.location,z)&&!(H&&H.submission&&Qt(H.submission.formMethod))){Mt(z,{matches:re},{flushSync:he});return}let ee=Da(re,Z,z.pathname);if(ee.active&&ee.matches&&(re=ee.matches),!re){let{error:lt,notFoundMatches:qe,route:Le}=ln(z.pathname);Mt(z,{matches:qe,loaderData:{},errors:{[Le.id]:lt}},{flushSync:he});return}ce=new AbortController;let de=qn(n.history,z,ce.signal,H&&H.submission),ie=new Lm(n.unstable_getContext?await n.unstable_getContext():void 0),ge;if(H&&H.pendingError)ge=[Wa(re).route.id,{type:"error",error:H.pendingError}];else if(H&&H.submission&&Qt(H.submission.formMethod)){let lt=await Vi(de,z,H.submission,re,ie,ee.active,H&&H.initialHydration===!0,{replace:H.replace,flushSync:he});if(lt.shortCircuited)return;if(lt.pendingActionResult){let[qe,Le]=lt.pendingActionResult;if(Vt(Le)&&er(Le.error)&&Le.error.status===404){ce=null,Mt(z,{matches:lt.matches,loaderData:{},errors:{[qe]:Le.error}});return}}re=lt.matches||re,ge=lt.pendingActionResult,$=of(z,H.submission),he=!1,ee.active=!1,de=qn(n.history,de.url,de.signal)}let{shortCircuited:pe,matches:Ee,loaderData:je,errors:We}=await Qi(de,z,re,ie,ee.active,$,H&&H.submission,H&&H.fetcherSubmission,H&&H.replace,H&&H.initialHydration===!0,he,ge);pe||(ce=null,Mt(z,{matches:Ee||re,...Km(ge),loaderData:je,errors:We}))}async function Vi(R,z,H,Z,$,re,he,ee={}){Zn();let de=Op(z,H);if(Pe({navigation:de},{flushSync:ee.flushSync===!0}),re){let pe=await _a(Z,z.pathname,R.signal);if(pe.type==="aborted")return{shortCircuited:!0};if(pe.type==="error"){let Ee=Wa(pe.partialMatches).route.id;return{matches:pe.partialMatches,pendingActionResult:[Ee,{type:"error",error:pe.error}]}}else if(pe.matches)Z=pe.matches;else{let{notFoundMatches:Ee,error:je,route:We}=ln(z.pathname);return{matches:Ee,pendingActionResult:[We.id,{type:"error",error:je}]}}}let ie,ge=Wu(Z,z);if(!ge.route.action&&!ge.route.lazy)ie={type:"error",error:al(405,{method:R.method,pathname:z.pathname,routeId:ge.route.id})};else{let pe=Gn(f,d,R,Z,ge,he?[]:c,$),Ee=await Vl(R,pe,$,null);if(ie=Ee[ge.route.id],!ie){for(let je of Z)if(Ee[je.route.id]){ie=Ee[je.route.id];break}}if(R.signal.aborted)return{shortCircuited:!0}}if(Pa(ie)){let pe;return ee&&ee.replace!=null?pe=ee.replace:pe=km(ie.response.headers.get("Location"),new URL(R.url),h)===U.location.pathname+U.location.search,await Tl(R,ie,!0,{submission:H,replace:pe}),{shortCircuited:!0}}if(Vt(ie)){let pe=Wa(Z,ge.route.id);return(ee&&ee.replace)!==!0&&(oe="PUSH"),{matches:Z,pendingActionResult:[pe.route.id,ie,ge.route.id]}}return{matches:Z,pendingActionResult:[ge.route.id,ie]}}async function Qi(R,z,H,Z,$,re,he,ee,de,ie,ge,pe){let Ee=re||of(z,he),je=he||ee||Fm(Ee),We=!Te&&!ie;if($){if(We){let Tt=Ht(pe);Pe({navigation:Ee,...Tt!==void 0?{actionData:Tt}:{}},{flushSync:ge})}let De=await _a(H,z.pathname,R.signal);if(De.type==="aborted")return{shortCircuited:!0};if(De.type==="error"){let Tt=Wa(De.partialMatches).route.id;return{matches:De.partialMatches,loaderData:{},errors:{[Tt]:De.error}}}else if(De.matches)H=De.matches;else{let{error:Tt,notFoundMatches:ol,route:cl}=ln(z.pathname);return{matches:ol,loaderData:{},errors:{[cl.id]:Tt}}}}let lt=g||y,{dsMatches:qe,revalidatingFetchers:Le}=jm(R,Z,f,d,n.history,U,H,je,z,ie?[]:c,ie===!0,xe,we,V,q,E,lt,h,n.patchRoutesOnNavigation!=null,pe);if(X=++J,!n.dataStrategy&&!qe.some(De=>De.shouldLoad)&&Le.length===0){let De=Ma();return Mt(z,{matches:H,loaderData:{},errors:pe&&Vt(pe[1])?{[pe[0]]:pe[1].error}:null,...Km(pe),...De?{fetchers:new Map(U.fetchers)}:{}},{flushSync:ge}),{shortCircuited:!0}}if(We){let De={};if(!$){De.navigation=Ee;let Tt=Ht(pe);Tt!==void 0&&(De.actionData=Tt)}Le.length>0&&(De.fetchers=Zi(Le)),Pe(De,{flushSync:ge})}Le.forEach(De=>{ml(De.key),De.controller&&D.set(De.key,De.controller)});let Ql=()=>Le.forEach(De=>ml(De.key));ce&&ce.signal.addEventListener("abort",Ql);let{loaderResults:At,fetcherResults:Zt}=await rr(qe,Le,R,Z);if(R.signal.aborted)return{shortCircuited:!0};ce&&ce.signal.removeEventListener("abort",Ql),Le.forEach(De=>D.delete(De.key));let Dt=bi(At);if(Dt)return await Tl(R,Dt.result,!0,{replace:de}),{shortCircuited:!0};if(Dt=bi(Zt),Dt)return E.add(Dt.key),await Tl(R,Dt.result,!0,{replace:de}),{shortCircuited:!0};let{loaderData:Zl,errors:Kl}=Qm(U,H,At,pe,Le,Zt);ie&&U.errors&&(Kl={...U.errors,...Kl});let $n=Ma(),Kt=Aa(X),wl=$n||Kt||Le.length>0;return{matches:H,loaderData:Zl,errors:Kl,...wl?{fetchers:new Map(U.fetchers)}:{}}}function Ht(R){if(R&&!Vt(R[1]))return{[R[0]]:R[1].data};if(U.actionData)return Object.keys(U.actionData).length===0?null:U.actionData}function Zi(R){return R.forEach(z=>{let H=U.fetchers.get(z.key),Z=Ku(void 0,H?H.data:void 0);U.fetchers.set(z.key,Z)}),new Map(U.fetchers)}async function nr(R,z,H,Z){ml(R);let $=(Z&&Z.flushSync)===!0,re=g||y,he=yf(U.location,U.matches,h,H,z,Z?.relative),ee=ba(re,he,h),de=Da(ee,re,he);if(de.active&&de.matches&&(ee=de.matches),!ee){tt(R,z,al(404,{pathname:he}),{flushSync:$});return}let{path:ie,submission:ge,error:pe}=Bm(!0,he,Z);if(pe){tt(R,z,pe,{flushSync:$});return}let Ee=Wu(ee,ie),je=new Lm(n.unstable_getContext?await n.unstable_getContext():void 0),We=(Z&&Z.preventScrollReset)===!0;if(ge&&Qt(ge.formMethod)){await ur(R,z,ie,Ee,ee,je,de.active,$,We,ge);return}q.set(R,{routeId:z,path:ie}),await tn(R,z,ie,Ee,ee,je,de.active,$,We,ge)}async function ur(R,z,H,Z,$,re,he,ee,de,ie){Zn(),q.delete(R);function ge(Ie){if(!Ie.route.action&&!Ie.route.lazy){let Jl=al(405,{method:ie.formMethod,pathname:H,routeId:z});return tt(R,z,Jl,{flushSync:ee}),!0}return!1}if(!he&&ge(Z))return;let pe=U.fetchers.get(R);Ot(R,Dp(ie,pe),{flushSync:ee});let Ee=new AbortController,je=qn(n.history,H,Ee.signal,ie);if(he){let Ie=await _a($,H,je.signal,R);if(Ie.type==="aborted")return;if(Ie.type==="error"){tt(R,z,Ie.error,{flushSync:ee});return}else if(Ie.matches){if($=Ie.matches,Z=Wu($,H),ge(Z))return}else{tt(R,z,al(404,{pathname:H}),{flushSync:ee});return}}D.set(R,Ee);let We=J,lt=Gn(f,d,je,$,Z,c,re),Le=(await Vl(je,lt,re,R))[Z.route.id];if(je.signal.aborted){D.get(R)===Ee&&D.delete(R);return}if(V.has(R)){if(Pa(Le)||Vt(Le)){Ot(R,pa(void 0));return}}else{if(Pa(Le))if(D.delete(R),X>We){Ot(R,pa(void 0));return}else return E.add(R),Ot(R,Ku(ie)),Tl(je,Le,!1,{fetcherSubmission:ie,preventScrollReset:de});if(Vt(Le)){tt(R,z,Le.error);return}}let Ql=U.navigation.location||U.location,At=qn(n.history,Ql,Ee.signal),Zt=g||y,Dt=U.navigation.state!=="idle"?ba(Zt,U.navigation.location,h):U.matches;Ce(Dt,"Didn't find any matches after fetcher action");let Zl=++J;Se.set(R,Zl);let Kl=Ku(ie,Le.data);U.fetchers.set(R,Kl);let{dsMatches:$n,revalidatingFetchers:Kt}=jm(At,re,f,d,n.history,U,Dt,ie,Ql,c,!1,xe,we,V,q,E,Zt,h,n.patchRoutesOnNavigation!=null,[Z.route.id,Le]);Kt.filter(Ie=>Ie.key!==R).forEach(Ie=>{let Jl=Ie.key,Fn=U.fetchers.get(Jl),_t=Ku(void 0,Fn?Fn.data:void 0);U.fetchers.set(Jl,_t),ml(Jl),Ie.controller&&D.set(Jl,Ie.controller)}),Pe({fetchers:new Map(U.fetchers)});let wl=()=>Kt.forEach(Ie=>ml(Ie.key));Ee.signal.addEventListener("abort",wl);let{loaderResults:De,fetcherResults:Tt}=await rr($n,Kt,At,re);if(Ee.signal.aborted)return;if(Ee.signal.removeEventListener("abort",wl),Se.delete(R),D.delete(R),Kt.forEach(Ie=>D.delete(Ie.key)),U.fetchers.has(R)){let Ie=pa(Le.data);U.fetchers.set(R,Ie)}let ol=bi(De);if(ol)return Tl(At,ol.result,!1,{preventScrollReset:de});if(ol=bi(Tt),ol)return E.add(ol.key),Tl(At,ol.result,!1,{preventScrollReset:de});let{loaderData:cl,errors:Ca}=Qm(U,Dt,De,void 0,Kt,Tt);Aa(Zl),U.navigation.state==="loading"&&Zl>X?(Ce(oe,"Expected pending action"),ce&&ce.abort(),Mt(U.navigation.location,{matches:Dt,loaderData:cl,errors:Ca,fetchers:new Map(U.fetchers)})):(Pe({errors:Ca,loaderData:Zm(U.loaderData,cl,Dt,Ca),fetchers:new Map(U.fetchers)}),xe=!1)}async function tn(R,z,H,Z,$,re,he,ee,de,ie){let ge=U.fetchers.get(R);Ot(R,Ku(ie,ge?ge.data:void 0),{flushSync:ee});let pe=new AbortController,Ee=qn(n.history,H,pe.signal);if(he){let Le=await _a($,H,Ee.signal,R);if(Le.type==="aborted")return;if(Le.type==="error"){tt(R,z,Le.error,{flushSync:ee});return}else if(Le.matches)$=Le.matches,Z=Wu($,H);else{tt(R,z,al(404,{pathname:H}),{flushSync:ee});return}}D.set(R,pe);let je=J,We=Gn(f,d,Ee,$,Z,c,re),qe=(await Vl(Ee,We,re,R))[Z.route.id];if(D.get(R)===pe&&D.delete(R),!Ee.signal.aborted){if(V.has(R)){Ot(R,pa(void 0));return}if(Pa(qe))if(X>je){Ot(R,pa(void 0));return}else{E.add(R),await Tl(Ee,qe,!1,{preventScrollReset:de});return}if(Vt(qe)){tt(R,z,qe.error);return}Ot(R,pa(qe.data))}}async function Tl(R,z,H,{submission:Z,fetcherSubmission:$,preventScrollReset:re,replace:he}={}){z.response.headers.has("X-Remix-Revalidate")&&(xe=!0);let ee=z.response.headers.get("Location");Ce(ee,"Expected a Location header on the redirect Response"),ee=km(ee,new URL(R.url),h);let de=Iu(U.location,ee,{_isRedirect:!0});if(o){let We=!1;if(z.response.headers.has("X-Remix-Reload-Document"))We=!0;else if(zf.test(ee)){const lt=yy(ee,!0);We=lt.origin!==r.location.origin||nl(lt.pathname,h)==null}if(We){he?r.location.replace(ee):r.location.assign(ee);return}}ce=null;let ie=he===!0||z.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:ge,formAction:pe,formEncType:Ee}=U.navigation;!Z&&!$&&ge&&pe&&Ee&&(Z=Fm(U.navigation));let je=Z||$;if(dp.has(z.response.status)&&je&&Qt(je.formMethod))await rl(ie,de,{submission:{...je,formAction:ee},preventScrollReset:re||F,enableViewTransition:H?Me:void 0});else{let We=of(de,Z);await rl(ie,de,{overrideNavigation:We,fetcherSubmission:$,preventScrollReset:re||F,enableViewTransition:H?Me:void 0})}}async function Vl(R,z,H,Z){let $,re={};try{$=await Ep(v,R,z,Z,H,!1)}catch(he){return z.filter(ee=>ee.shouldLoad).forEach(ee=>{re[ee.route.id]={type:"error",error:he}}),re}if(R.signal.aborted)return re;for(let[he,ee]of Object.entries($))if(Ap(ee)){let de=ee.result;re[he]={type:"redirect",response:xp(de,R,he,z,h)}}else re[he]=await Tp(ee);return re}async function rr(R,z,H,Z){let $=Vl(H,R,Z,null),re=Promise.all(z.map(async de=>{if(de.matches&&de.match&&de.request&&de.controller){let ge=(await Vl(de.request,de.matches,Z,de.key))[de.match.route.id];return{[de.key]:ge}}else return Promise.resolve({[de.key]:{type:"error",error:al(404,{pathname:de.path})}})})),he=await $,ee=(await re).reduce((de,ie)=>Object.assign(de,ie),{});return{loaderResults:he,fetcherResults:ee}}function Zn(){xe=!0,q.forEach((R,z)=>{D.has(z)&&we.add(z),ml(z)})}function Ot(R,z,H={}){U.fetchers.set(R,z),Pe({fetchers:new Map(U.fetchers)},{flushSync:(H&&H.flushSync)===!0})}function tt(R,z,H,Z={}){let $=Wa(U.matches,z);bt(R),Pe({errors:{[$.route.id]:H},fetchers:new Map(U.fetchers)},{flushSync:(Z&&Z.flushSync)===!0})}function il(R){return W.set(R,(W.get(R)||0)+1),V.has(R)&&V.delete(R),U.fetchers.get(R)||hp}function bt(R){let z=U.fetchers.get(R);D.has(R)&&!(z&&z.state==="loading"&&Se.has(R))&&ml(R),q.delete(R),Se.delete(R),E.delete(R),V.delete(R),we.delete(R),U.fetchers.delete(R)}function Ki(R){let z=(W.get(R)||0)-1;z<=0?(W.delete(R),V.add(R)):W.set(R,z),Pe({fetchers:new Map(U.fetchers)})}function ml(R){let z=D.get(R);z&&(z.abort(),D.delete(R))}function ir(R){for(let z of R){let H=il(z),Z=pa(H.data);U.fetchers.set(z,Z)}}function Ma(){let R=[],z=!1;for(let H of E){let Z=U.fetchers.get(H);Ce(Z,`Expected fetcher: ${H}`),Z.state==="loading"&&(E.delete(H),R.push(H),z=!0)}return ir(R),z}function Aa(R){let z=[];for(let[H,Z]of Se)if(Z<R){let $=U.fetchers.get(H);Ce($,`Expected fetcher: ${H}`),$.state==="loading"&&(ml(H),Se.delete(H),z.push(H))}return ir(z),z.length>0}function xl(R,z){let H=U.blockers.get(R)||Zu;return I.get(R)!==z&&I.set(R,z),H}function za(R){U.blockers.delete(R),I.delete(R)}function yl(R,z){let H=U.blockers.get(R)||Zu;Ce(H.state==="unblocked"&&z.state==="blocked"||H.state==="blocked"&&z.state==="blocked"||H.state==="blocked"&&z.state==="proceeding"||H.state==="blocked"&&z.state==="unblocked"||H.state==="proceeding"&&z.state==="unblocked",`Invalid blocker state transition: ${H.state} -> ${z.state}`);let Z=new Map(U.blockers);Z.set(R,z),Pe({blockers:Z})}function or({currentLocation:R,nextLocation:z,historyAction:H}){if(I.size===0)return;I.size>1&&rt(!1,"A router only supports one blocker at a time");let Z=Array.from(I.entries()),[$,re]=Z[Z.length-1],he=U.blockers.get($);if(!(he&&he.state==="proceeding")&&re({currentLocation:R,nextLocation:z,historyAction:H}))return $}function ln(R){let z=al(404,{pathname:R}),H=g||y,{matches:Z,route:$}=Jm(H);return{notFoundMatches:Z,route:$,error:z}}function cr(R,z,H){if(C=R,j=z,w=H||null,!k&&U.navigation===rf){k=!0;let Z=fr(U.location,U.matches);Z!=null&&Pe({restoreScrollPosition:Z})}return()=>{C=null,j=null,w=null}}function an(R,z){return w&&w(R,z.map(Z=>V0(Z,U.loaderData)))||R.key}function Oa(R,z){if(C&&j){let H=an(R,z);C[H]=j()}}function fr(R,z){if(C){let H=an(R,z),Z=C[H];if(typeof Z=="number")return Z}return null}function Da(R,z,H){if(n.patchRoutesOnNavigation)if(R){if(Object.keys(R[0].params).length>0)return{active:!0,matches:Ai(z,H,h,!0)}}else return{active:!0,matches:Ai(z,H,h,!0)||[]};return{active:!1,matches:null}}async function _a(R,z,H,Z){if(!n.patchRoutesOnNavigation)return{type:"success",matches:R};let $=R;for(;;){let re=g==null,he=g||y,ee=d;try{await n.patchRoutesOnNavigation({signal:H,path:z,matches:$,fetcherKey:Z,patch:(ge,pe)=>{H.aborted||Ym(ge,pe,he,ee,f)}})}catch(ge){return{type:"error",error:ge,partialMatches:$}}finally{re&&!H.aborted&&(y=[...y])}if(H.aborted)return{type:"aborted"};let de=ba(he,z,h);if(de)return{type:"success",matches:de};let ie=Ai(he,z,h,!0);if(!ie||$.length===ie.length&&$.every((ge,pe)=>ge.route.id===ie[pe].route.id))return{type:"success",matches:null};$=ie}}function Kn(R){d={},g=_i(R,f,void 0,d)}function Jn(R,z){let H=g==null;Ym(R,z,g||y,d,f),H&&(y=[...y],Pe({}))}return P={get basename(){return h},get future(){return p},get state(){return U},get routes(){return y},get window(){return r},initialize:Oe,subscribe:it,enableScrollRestoration:cr,navigate:ul,fetch:nr,revalidate:Qn,createHref:R=>n.history.createHref(R),encodeLocation:R=>n.history.encodeLocation(R),getFetcher:il,deleteFetcher:Ki,dispose:He,getBlocker:xl,deleteBlocker:za,patchRoutes:Jn,_internalFetchControllers:D,_internalSetRoutes:Kn},P}function vp(n){return n!=null&&("formData"in n&&n.formData!=null||"body"in n&&n.body!==void 0)}function yf(n,r,o,c,f,d){let y,g;if(f){y=[];for(let v of r)if(y.push(v),v.route.id===f){g=v;break}}else y=r,g=r[r.length-1];let h=Hi(c||".",Li(y),nl(n.pathname,o)||n.pathname,d==="path");if(c==null&&(h.search=n.search,h.hash=n.hash),(c==null||c===""||c===".")&&g){let v=Of(h.search);if(g.route.index&&!v)h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index";else if(!g.route.index&&v){let p=new URLSearchParams(h.search),A=p.getAll("index");p.delete("index"),A.filter(C=>C).forEach(C=>p.append("index",C));let x=p.toString();h.search=x?`?${x}`:""}}return o!=="/"&&(h.pathname=h.pathname==="/"?o:El([o,h.pathname])),xa(h)}function Bm(n,r,o){if(!o||!vp(o))return{path:r};if(o.formMethod&&!zp(o.formMethod))return{path:r,error:al(405,{method:o.formMethod})};let c=()=>({path:r,error:al(400,{type:"invalid-body"})}),d=(o.formMethod||"get").toUpperCase(),y=Ay(r);if(o.body!==void 0){if(o.formEncType==="text/plain"){if(!Qt(d))return c();let A=typeof o.body=="string"?o.body:o.body instanceof FormData||o.body instanceof URLSearchParams?Array.from(o.body.entries()).reduce((x,[C,w])=>`${x}${C}=${w}
`,""):String(o.body);return{path:r,submission:{formMethod:d,formAction:y,formEncType:o.formEncType,formData:void 0,json:void 0,text:A}}}else if(o.formEncType==="application/json"){if(!Qt(d))return c();try{let A=typeof o.body=="string"?JSON.parse(o.body):o.body;return{path:r,submission:{formMethod:d,formAction:y,formEncType:o.formEncType,formData:void 0,json:A,text:void 0}}}catch{return c()}}}Ce(typeof FormData=="function","FormData is not available in this environment");let g,h;if(o.formData)g=pf(o.formData),h=o.formData;else if(o.body instanceof FormData)g=pf(o.body),h=o.body;else if(o.body instanceof URLSearchParams)g=o.body,h=Vm(g);else if(o.body==null)g=new URLSearchParams,h=new FormData;else try{g=new URLSearchParams(o.body),h=Vm(g)}catch{return c()}let v={formMethod:d,formAction:y,formEncType:o&&o.formEncType||"application/x-www-form-urlencoded",formData:h,json:void 0,text:void 0};if(Qt(v.formMethod))return{path:r,submission:v};let p=wa(r);return n&&p.search&&Of(p.search)&&g.append("index",""),p.search=`?${g}`,{path:xa(p),submission:v}}function jm(n,r,o,c,f,d,y,g,h,v,p,A,x,C,w,j,k,B,ne,K){let ue=K?Vt(K[1])?K[1].error:K[1].data:void 0,P=f.createURL(d.location),U=f.createURL(h),oe;if(p&&d.errors){let Te=Object.keys(d.errors)[0];oe=y.findIndex(xe=>xe.route.id===Te)}else if(K&&Vt(K[1])){let Te=K[0];oe=y.findIndex(xe=>xe.route.id===Te)-1}let F=K?K[1].statusCode:void 0,ce=F&&F>=400,Me={currentUrl:P,currentParams:d.matches[0]?.params||{},nextUrl:U,nextParams:y[0].params,...g,actionResult:ue,actionStatus:F},Qe=y.map((Te,xe)=>{let{route:we}=Te,D=null;if(oe!=null&&xe>oe?D=!1:we.lazy?D=!0:we.loader==null?D=!1:p?D=vf(we,d.loaderData,d.errors):gp(d.loaderData,d.matches[xe],Te)&&(D=!0),D!==null)return gf(o,c,n,Te,v,r,D);let J=ce?!1:A||P.pathname+P.search===U.pathname+U.search||P.search!==U.search||pp(d.matches[xe],Te),X={...Me,defaultShouldRevalidate:J},Se=Ni(Te,X);return gf(o,c,n,Te,v,r,Se,X)}),Ye=[];return w.forEach((Te,xe)=>{if(p||!y.some(W=>W.route.id===Te.routeId)||C.has(xe))return;let we=d.fetchers.get(xe),D=we&&we.state!=="idle"&&we.data===void 0,J=ba(k,Te.path,B);if(!J){if(ne&&D)return;Ye.push({key:xe,routeId:Te.routeId,path:Te.path,matches:null,match:null,request:null,controller:null});return}if(j.has(xe))return;let X=Wu(J,Te.path),Se=new AbortController,E=qn(f,Te.path,Se.signal),q=null;if(x.has(xe))x.delete(xe),q=Gn(o,c,E,J,X,v,r);else if(D)A&&(q=Gn(o,c,E,J,X,v,r));else{let W={...Me,defaultShouldRevalidate:ce?!1:A};Ni(X,W)&&(q=Gn(o,c,E,J,X,v,r,W))}q&&Ye.push({key:xe,routeId:Te.routeId,path:Te.path,matches:q,match:X,request:E,controller:Se})}),{dsMatches:Qe,revalidatingFetchers:Ye}}function vf(n,r,o){if(n.lazy)return!0;if(!n.loader)return!1;let c=r!=null&&n.id in r,f=o!=null&&o[n.id]!==void 0;return!c&&f?!1:typeof n.loader=="function"&&n.loader.hydrate===!0?!0:!c&&!f}function gp(n,r,o){let c=!r||o.route.id!==r.route.id,f=!n.hasOwnProperty(o.route.id);return c||f}function pp(n,r){let o=n.route.path;return n.pathname!==r.pathname||o!=null&&o.endsWith("*")&&n.params["*"]!==r.params["*"]}function Ni(n,r){if(n.route.shouldRevalidate){let o=n.route.shouldRevalidate(r);if(typeof o=="boolean")return o}return r.defaultShouldRevalidate}function Ym(n,r,o,c,f){let d;if(n){let h=c[n];Ce(h,`No route found to patch children into: routeId = ${n}`),h.children||(h.children=[]),d=h.children}else d=o;let y=r.filter(h=>!d.some(v=>Ry(h,v))),g=_i(y,f,[n||"_","patch",String(d?.length||"0")],c);d.push(...g)}function Ry(n,r){return"id"in n&&"id"in r&&n.id===r.id?!0:n.index===r.index&&n.path===r.path&&n.caseSensitive===r.caseSensitive?(!n.children||n.children.length===0)&&(!r.children||r.children.length===0)?!0:n.children.every((o,c)=>r.children?.some(f=>Ry(o,f))):!1}var qm=new WeakMap,Ty=({key:n,route:r,manifest:o,mapRouteProperties:c})=>{let f=o[r.id];if(Ce(f,"No route found in manifest"),!f.lazy||typeof f.lazy!="object")return;let d=f.lazy[n];if(!d)return;let y=qm.get(f);y||(y={},qm.set(f,y));let g=y[n];if(g)return g;let h=(async()=>{let v=q0(n),A=f[n]!==void 0&&n!=="hasErrorBoundary";if(v)rt(!v,"Route property "+n+" is not a supported lazy route property. This property will be ignored."),y[n]=Promise.resolve();else if(A)rt(!1,`Route "${f.id}" has a static property "${n}" defined. The lazy property will be ignored.`);else{let x=await d();x!=null&&(Object.assign(f,{[n]:x}),Object.assign(f,c(f)))}typeof f.lazy=="object"&&(f.lazy[n]=void 0,Object.values(f.lazy).every(x=>x===void 0)&&(f.lazy=void 0))})();return y[n]=h,h},Gm=new WeakMap;function bp(n,r,o,c,f){let d=o[n.id];if(Ce(d,"No route found in manifest"),!n.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof n.lazy=="function"){let p=Gm.get(d);if(p)return{lazyRoutePromise:p,lazyHandlerPromise:p};let A=(async()=>{Ce(typeof n.lazy=="function","No lazy route function found");let x=await n.lazy(),C={};for(let w in x){let j=x[w];if(j===void 0)continue;let k=X0(w),ne=d[w]!==void 0&&w!=="hasErrorBoundary";k?rt(!k,"Route property "+w+" is not a supported property to be returned from a lazy route function. This property will be ignored."):ne?rt(!ne,`Route "${d.id}" has a static property "${w}" defined but its lazy function is also returning a value for this property. The lazy route property "${w}" will be ignored.`):C[w]=j}Object.assign(d,C),Object.assign(d,{...c(d),lazy:void 0})})();return Gm.set(d,A),A.catch(()=>{}),{lazyRoutePromise:A,lazyHandlerPromise:A}}let y=Object.keys(n.lazy),g=[],h;for(let p of y){if(f&&f.includes(p))continue;let A=Ty({key:p,route:n,manifest:o,mapRouteProperties:c});A&&(g.push(A),p===r&&(h=A))}let v=g.length>0?Promise.all(g).then(()=>{}):void 0;return v?.catch(()=>{}),h?.catch(()=>{}),{lazyRoutePromise:v,lazyHandlerPromise:h}}async function Xm(n){let r=n.matches.filter(f=>f.shouldLoad),o={};return(await Promise.all(r.map(f=>f.resolve()))).forEach((f,d)=>{o[r[d].route.id]=f}),o}async function Sp(n){return n.matches.some(r=>r.route.unstable_middleware)?xy(n,!1,()=>Xm(n),(r,o)=>({[o]:{type:"error",result:r}})):Xm(n)}async function xy(n,r,o,c){let{matches:f,request:d,params:y,context:g}=n,h={handlerResult:void 0};try{let v=f.flatMap(A=>A.route.unstable_middleware?A.route.unstable_middleware.map(x=>[A.route.id,x]):[]),p=await wy({request:d,params:y,context:g},v,r,h,o);return r?p:h.handlerResult}catch(v){if(!h.middlewareError)throw v;let p=await c(h.middlewareError.error,h.middlewareError.routeId);return h.handlerResult?Object.assign(h.handlerResult,p):p}}async function wy(n,r,o,c,f,d=0){let{request:y}=n;if(y.signal.aborted)throw y.signal.reason?y.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${y.method} ${y.url}`);let g=r[d];if(!g)return c.handlerResult=await f(),c.handlerResult;let[h,v]=g,p=!1,A,x=async()=>{if(p)throw new Error("You may only call `next()` once per middleware");p=!0,await wy(n,r,o,c,f,d+1)};try{let C=await v({request:n.request,params:n.params,context:n.context},x);return p?C===void 0?A:C:x()}catch(C){throw c.middlewareError?c.middlewareError.error!==C&&(c.middlewareError={routeId:h,error:C}):c.middlewareError={routeId:h,error:C},C}}function My(n,r,o,c,f){let d=Ty({key:"unstable_middleware",route:c.route,manifest:r,mapRouteProperties:n}),y=bp(c.route,Qt(o.method)?"action":"loader",r,n,f);return{middleware:d,route:y.lazyRoutePromise,handler:y.lazyHandlerPromise}}function gf(n,r,o,c,f,d,y,g=null){let h=!1,v=My(n,r,o,c,f);return{...c,_lazyPromises:v,shouldLoad:y,unstable_shouldRevalidateArgs:g,unstable_shouldCallHandler(p){return h=!0,g?typeof p=="boolean"?Ni(c,{...g,defaultShouldRevalidate:p}):Ni(c,g):y},resolve(p){return h||y||p&&o.method==="GET"&&(c.route.lazy||c.route.loader)?Rp({request:o,match:c,lazyHandlerPromise:v?.handler,lazyRoutePromise:v?.route,handlerOverride:p,scopedContext:d}):Promise.resolve({type:"data",result:void 0})}}}function Gn(n,r,o,c,f,d,y,g=null){return c.map(h=>h.route.id!==f.route.id?{...h,shouldLoad:!1,unstable_shouldRevalidateArgs:g,unstable_shouldCallHandler:()=>!1,_lazyPromises:My(n,r,o,h,d),resolve:()=>Promise.resolve({type:"data",result:void 0})}:gf(n,r,o,h,d,y,!0,g))}async function Ep(n,r,o,c,f,d){o.some(v=>v._lazyPromises?.middleware)&&await Promise.all(o.map(v=>v._lazyPromises?.middleware));let y={request:r,params:o[0].params,context:f,matches:o},h=await n({...y,fetcherKey:c,unstable_runClientMiddleware:v=>{let p=y;return xy(p,!1,()=>v({...p,fetcherKey:c,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(A,x)=>({[x]:{type:"error",result:A}}))}});try{await Promise.all(o.flatMap(v=>[v._lazyPromises?.handler,v._lazyPromises?.route]))}catch{}return h}async function Rp({request:n,match:r,lazyHandlerPromise:o,lazyRoutePromise:c,handlerOverride:f,scopedContext:d}){let y,g,h=Qt(n.method),v=h?"action":"loader",p=A=>{let x,C=new Promise((k,B)=>x=B);g=()=>x(),n.signal.addEventListener("abort",g);let w=k=>typeof A!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${v}" [routeId: ${r.route.id}]`)):A({request:n,params:r.params,context:d},...k!==void 0?[k]:[]),j=(async()=>{try{return{type:"data",result:await(f?f(B=>w(B)):w())}}catch(k){return{type:"error",result:k}}})();return Promise.race([j,C])};try{let A=h?r.route.action:r.route.loader;if(o||c)if(A){let x,[C]=await Promise.all([p(A).catch(w=>{x=w}),o,c]);if(x!==void 0)throw x;y=C}else{await o;let x=h?r.route.action:r.route.loader;if(x)[y]=await Promise.all([p(x),c]);else if(v==="action"){let C=new URL(n.url),w=C.pathname+C.search;throw al(405,{method:n.method,pathname:w,routeId:r.route.id})}else return{type:"data",result:void 0}}else if(A)y=await p(A);else{let x=new URL(n.url),C=x.pathname+x.search;throw al(404,{pathname:C})}}catch(A){return{type:"error",result:A}}finally{g&&n.signal.removeEventListener("abort",g)}return y}async function Tp(n){let{result:r,type:o}=n;if(zy(r)){let c;try{let f=r.headers.get("Content-Type");f&&/\bapplication\/json\b/.test(f)?r.body==null?c=null:c=await r.json():c=await r.text()}catch(f){return{type:"error",error:f}}return o==="error"?{type:"error",error:new Ui(r.status,r.statusText,c),statusCode:r.status,headers:r.headers}:{type:"data",data:c,statusCode:r.status,headers:r.headers}}return o==="error"?$m(r)?r.data instanceof Error?{type:"error",error:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:new Ui(r.init?.status||500,void 0,r.data),statusCode:er(r)?r.status:void 0,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:r,statusCode:er(r)?r.status:void 0}:$m(r)?{type:"data",data:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"data",data:r}}function xp(n,r,o,c,f){let d=n.headers.get("Location");if(Ce(d,"Redirects returned/thrown from loaders/actions must have a Location header"),!zf.test(d)){let y=c.slice(0,c.findIndex(g=>g.route.id===o)+1);d=yf(new URL(r.url),y,f,d),n.headers.set("Location",d)}return n}function km(n,r,o){if(zf.test(n)){let c=n,f=c.startsWith("//")?new URL(r.protocol+c):new URL(c),d=nl(f.pathname,o)!=null;if(f.origin===r.origin&&d)return f.pathname+f.search+f.hash}return n}function qn(n,r,o,c){let f=n.createURL(Ay(r)).toString(),d={signal:o};if(c&&Qt(c.formMethod)){let{formMethod:y,formEncType:g}=c;d.method=y.toUpperCase(),g==="application/json"?(d.headers=new Headers({"Content-Type":g}),d.body=JSON.stringify(c.json)):g==="text/plain"?d.body=c.text:g==="application/x-www-form-urlencoded"&&c.formData?d.body=pf(c.formData):d.body=c.formData}return new Request(f,d)}function pf(n){let r=new URLSearchParams;for(let[o,c]of n.entries())r.append(o,typeof c=="string"?c:c.name);return r}function Vm(n){let r=new FormData;for(let[o,c]of n.entries())r.append(o,c);return r}function wp(n,r,o,c=!1,f=!1){let d={},y=null,g,h=!1,v={},p=o&&Vt(o[1])?o[1].error:void 0;return n.forEach(A=>{if(!(A.route.id in r))return;let x=A.route.id,C=r[x];if(Ce(!Pa(C),"Cannot handle redirect results in processLoaderData"),Vt(C)){let w=C.error;if(p!==void 0&&(w=p,p=void 0),y=y||{},f)y[x]=w;else{let j=Wa(n,x);y[j.route.id]==null&&(y[j.route.id]=w)}c||(d[x]=Ey),h||(h=!0,g=er(C.error)?C.error.status:500),C.headers&&(v[x]=C.headers)}else d[x]=C.data,C.statusCode&&C.statusCode!==200&&!h&&(g=C.statusCode),C.headers&&(v[x]=C.headers)}),p!==void 0&&o&&(y={[o[0]]:p},o[2]&&(d[o[2]]=void 0)),{loaderData:d,errors:y,statusCode:g||200,loaderHeaders:v}}function Qm(n,r,o,c,f,d){let{loaderData:y,errors:g}=wp(r,o,c);return f.filter(h=>!h.matches||h.matches.some(v=>v.shouldLoad)).forEach(h=>{let{key:v,match:p,controller:A}=h,x=d[v];if(Ce(x,"Did not find corresponding fetcher result"),!(A&&A.signal.aborted))if(Vt(x)){let C=Wa(n.matches,p?.route.id);g&&g[C.route.id]||(g={...g,[C.route.id]:x.error}),n.fetchers.delete(v)}else if(Pa(x))Ce(!1,"Unhandled fetcher revalidation redirect");else{let C=pa(x.data);n.fetchers.set(v,C)}}),{loaderData:y,errors:g}}function Zm(n,r,o,c){let f=Object.entries(r).filter(([,d])=>d!==Ey).reduce((d,[y,g])=>(d[y]=g,d),{});for(let d of o){let y=d.route.id;if(!r.hasOwnProperty(y)&&n.hasOwnProperty(y)&&d.route.loader&&(f[y]=n[y]),c&&c.hasOwnProperty(y))break}return f}function Km(n){return n?Vt(n[1])?{actionData:{}}:{actionData:{[n[0]]:n[1].data}}:{}}function Wa(n,r){return(r?n.slice(0,n.findIndex(c=>c.route.id===r)+1):[...n]).reverse().find(c=>c.route.hasErrorBoundary===!0)||n[0]}function Jm(n){let r=n.length===1?n[0]:n.find(o=>o.index||!o.path||o.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:r}],route:r}}function al(n,{pathname:r,routeId:o,method:c,type:f,message:d}={}){let y="Unknown Server Error",g="Unknown @remix-run/router error";return n===400?(y="Bad Request",c&&r&&o?g=`You made a ${c} request to "${r}" but did not provide a \`loader\` for route "${o}", so there is no way to handle the request.`:f==="invalid-body"&&(g="Unable to encode submission body")):n===403?(y="Forbidden",g=`Route "${o}" does not match URL "${r}"`):n===404?(y="Not Found",g=`No route matches URL "${r}"`):n===405&&(y="Method Not Allowed",c&&r&&o?g=`You made a ${c.toUpperCase()} request to "${r}" but did not provide an \`action\` for route "${o}", so there is no way to handle the request.`:c&&(g=`Invalid request method "${c.toUpperCase()}"`)),new Ui(n||500,y,new Error(g),!0)}function bi(n){let r=Object.entries(n);for(let o=r.length-1;o>=0;o--){let[c,f]=r[o];if(Pa(f))return{key:c,result:f}}}function Ay(n){let r=typeof n=="string"?wa(n):n;return xa({...r,hash:""})}function Mp(n,r){return n.pathname!==r.pathname||n.search!==r.search?!1:n.hash===""?r.hash!=="":n.hash===r.hash?!0:r.hash!==""}function Ap(n){return zy(n.result)&&sp.has(n.result.status)}function Vt(n){return n.type==="error"}function Pa(n){return(n&&n.type)==="redirect"}function $m(n){return typeof n=="object"&&n!=null&&"type"in n&&"data"in n&&"init"in n&&n.type==="DataWithResponseInit"}function zy(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.headers=="object"&&typeof n.body<"u"}function zp(n){return fp.has(n.toUpperCase())}function Qt(n){return op.has(n.toUpperCase())}function Of(n){return new URLSearchParams(n).getAll("index").some(r=>r==="")}function Wu(n,r){let o=typeof r=="string"?wa(r).search:r.search;if(n[n.length-1].route.index&&Of(o||""))return n[n.length-1];let c=py(n);return c[c.length-1]}function Fm(n){let{formMethod:r,formAction:o,formEncType:c,text:f,formData:d,json:y}=n;if(!(!r||!o||!c)){if(f!=null)return{formMethod:r,formAction:o,formEncType:c,formData:void 0,json:void 0,text:f};if(d!=null)return{formMethod:r,formAction:o,formEncType:c,formData:d,json:void 0,text:void 0};if(y!==void 0)return{formMethod:r,formAction:o,formEncType:c,formData:void 0,json:y,text:void 0}}}function of(n,r){return r?{state:"loading",location:n,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}:{state:"loading",location:n,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Op(n,r){return{state:"submitting",location:n,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}}function Ku(n,r){return n?{state:"loading",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:r}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:r}}function Dp(n,r){return{state:"submitting",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:r?r.data:void 0}}function pa(n){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:n}}function _p(n,r){try{let o=n.sessionStorage.getItem(Sy);if(o){let c=JSON.parse(o);for(let[f,d]of Object.entries(c||{}))d&&Array.isArray(d)&&r.set(f,new Set(d||[]))}}catch{}}function Cp(n,r){if(r.size>0){let o={};for(let[c,f]of r)o[c]=[...f];try{n.sessionStorage.setItem(Sy,JSON.stringify(o))}catch(c){rt(!1,`Failed to save applied view transitions in sessionStorage (${c}).`)}}}function Up(){let n,r,o=new Promise((c,f)=>{n=async d=>{c(d);try{await o}catch{}},r=async d=>{f(d);try{await o}catch{}}});return{promise:o,resolve:n,reject:r}}var en=S.createContext(null);en.displayName="DataRouter";var tr=S.createContext(null);tr.displayName="DataRouterState";var Df=S.createContext({isTransitioning:!1});Df.displayName="ViewTransition";var Oy=S.createContext(new Map);Oy.displayName="Fetchers";var Np=S.createContext(null);Np.displayName="Await";var hl=S.createContext(null);hl.displayName="Navigation";var Bi=S.createContext(null);Bi.displayName="Location";var Rl=S.createContext({outlet:null,matches:[],isDataRoute:!1});Rl.displayName="Route";var _f=S.createContext(null);_f.displayName="RouteError";function Lp(n,{relative:r}={}){Ce(Xn(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:c}=S.useContext(hl),{hash:f,pathname:d,search:y}=lr(n,{relative:r}),g=d;return o!=="/"&&(g=d==="/"?o:El([o,d])),c.createHref({pathname:g,search:y,hash:f})}function Xn(){return S.useContext(Bi)!=null}function kl(){return Ce(Xn(),"useLocation() may be used only in the context of a <Router> component."),S.useContext(Bi).location}var Dy="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function _y(n){S.useContext(hl).static||S.useLayoutEffect(n)}function Cf(){let{isDataRoute:n}=S.useContext(Rl);return n?Jp():Hp()}function Hp(){Ce(Xn(),"useNavigate() may be used only in the context of a <Router> component.");let n=S.useContext(en),{basename:r,navigator:o}=S.useContext(hl),{matches:c}=S.useContext(Rl),{pathname:f}=kl(),d=JSON.stringify(Li(c)),y=S.useRef(!1);return _y(()=>{y.current=!0}),S.useCallback((h,v={})=>{if(rt(y.current,Dy),!y.current)return;if(typeof h=="number"){o.go(h);return}let p=Hi(h,JSON.parse(d),f,v.relative==="path");n==null&&r!=="/"&&(p.pathname=p.pathname==="/"?r:El([r,p.pathname])),(v.replace?o.replace:o.push)(p,v.state,v)},[r,o,d,f,n])}S.createContext(null);function lr(n,{relative:r}={}){let{matches:o}=S.useContext(Rl),{pathname:c}=kl(),f=JSON.stringify(Li(o));return S.useMemo(()=>Hi(n,JSON.parse(f),c,r==="path"),[n,f,c,r])}function Bp(n,r,o,c){Ce(Xn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:f}=S.useContext(hl),{matches:d}=S.useContext(Rl),y=d[d.length-1],g=y?y.params:{},h=y?y.pathname:"/",v=y?y.pathnameBase:"/",p=y&&y.route;{let B=p&&p.path||"";Cy(h,!p||B.endsWith("*")||B.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${B}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${B}"> to <Route path="${B==="/"?"*":`${B}/*`}">.`)}let A=kl(),x;x=A;let C=x.pathname||"/",w=C;if(v!=="/"){let B=v.replace(/^\//,"").split("/");w="/"+C.replace(/^\//,"").split("/").slice(B.length).join("/")}let j=ba(n,{pathname:w});return rt(p||j!=null,`No routes matched location "${x.pathname}${x.search}${x.hash}" `),rt(j==null||j[j.length-1].route.element!==void 0||j[j.length-1].route.Component!==void 0||j[j.length-1].route.lazy!==void 0,`Matched leaf route at location "${x.pathname}${x.search}${x.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Xp(j&&j.map(B=>Object.assign({},B,{params:Object.assign({},g,B.params),pathname:El([v,f.encodeLocation?f.encodeLocation(B.pathname).pathname:B.pathname]),pathnameBase:B.pathnameBase==="/"?v:El([v,f.encodeLocation?f.encodeLocation(B.pathnameBase).pathname:B.pathnameBase])})),d,o,c)}function jp(){let n=Kp(),r=er(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),o=n instanceof Error?n.stack:null,c="rgba(200,200,200, 0.5)",f={padding:"0.5rem",backgroundColor:c},d={padding:"2px 4px",backgroundColor:c},y=null;return console.error("Error handled by React Router default ErrorBoundary:",n),y=S.createElement(S.Fragment,null,S.createElement("p",null,"💿 Hey developer 👋"),S.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",S.createElement("code",{style:d},"ErrorBoundary")," or"," ",S.createElement("code",{style:d},"errorElement")," prop on your route.")),S.createElement(S.Fragment,null,S.createElement("h2",null,"Unexpected Application Error!"),S.createElement("h3",{style:{fontStyle:"italic"}},r),o?S.createElement("pre",{style:f},o):null,y)}var Yp=S.createElement(jp,null),qp=class extends S.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,r){return r.location!==n.location||r.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:r.error,location:r.location,revalidation:n.revalidation||r.revalidation}}componentDidCatch(n,r){console.error("React Router caught the following error during render",n,r)}render(){return this.state.error!==void 0?S.createElement(Rl.Provider,{value:this.props.routeContext},S.createElement(_f.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Gp({routeContext:n,match:r,children:o}){let c=S.useContext(en);return c&&c.static&&c.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=r.route.id),S.createElement(Rl.Provider,{value:n},o)}function Xp(n,r=[],o=null,c=null){if(n==null){if(!o)return null;if(o.errors)n=o.matches;else if(r.length===0&&!o.initialized&&o.matches.length>0)n=o.matches;else return null}let f=n,d=o?.errors;if(d!=null){let h=f.findIndex(v=>v.route.id&&d?.[v.route.id]!==void 0);Ce(h>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(d).join(",")}`),f=f.slice(0,Math.min(f.length,h+1))}let y=!1,g=-1;if(o)for(let h=0;h<f.length;h++){let v=f[h];if((v.route.HydrateFallback||v.route.hydrateFallbackElement)&&(g=h),v.route.id){let{loaderData:p,errors:A}=o,x=v.route.loader&&!p.hasOwnProperty(v.route.id)&&(!A||A[v.route.id]===void 0);if(v.route.lazy||x){y=!0,g>=0?f=f.slice(0,g+1):f=[f[0]];break}}}return f.reduceRight((h,v,p)=>{let A,x=!1,C=null,w=null;o&&(A=d&&v.route.id?d[v.route.id]:void 0,C=v.route.errorElement||Yp,y&&(g<0&&p===0?(Cy("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,w=null):g===p&&(x=!0,w=v.route.hydrateFallbackElement||null)));let j=r.concat(f.slice(0,p+1)),k=()=>{let B;return A?B=C:x?B=w:v.route.Component?B=S.createElement(v.route.Component,null):v.route.element?B=v.route.element:B=h,S.createElement(Gp,{match:v,routeContext:{outlet:h,matches:j,isDataRoute:o!=null},children:B})};return o&&(v.route.ErrorBoundary||v.route.errorElement||p===0)?S.createElement(qp,{location:o.location,revalidation:o.revalidation,component:C,error:A,children:k(),routeContext:{outlet:null,matches:j,isDataRoute:!0}}):k()},null)}function Uf(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function kp(n){let r=S.useContext(en);return Ce(r,Uf(n)),r}function Vp(n){let r=S.useContext(tr);return Ce(r,Uf(n)),r}function Qp(n){let r=S.useContext(Rl);return Ce(r,Uf(n)),r}function Nf(n){let r=Qp(n),o=r.matches[r.matches.length-1];return Ce(o.route.id,`${n} can only be used on routes that contain a unique "id"`),o.route.id}function Zp(){return Nf("useRouteId")}function Kp(){let n=S.useContext(_f),r=Vp("useRouteError"),o=Nf("useRouteError");return n!==void 0?n:r.errors?.[o]}function Jp(){let{router:n}=kp("useNavigate"),r=Nf("useNavigate"),o=S.useRef(!1);return _y(()=>{o.current=!0}),S.useCallback(async(f,d={})=>{rt(o.current,Dy),o.current&&(typeof f=="number"?n.navigate(f):await n.navigate(f,{fromRouteId:r,...d}))},[n,r])}var Wm={};function Cy(n,r,o){!r&&!Wm[n]&&(Wm[n]=!0,rt(!1,o))}var Pm={};function Im(n,r){!n&&!Pm[r]&&(Pm[r]=!0,console.warn(r))}function $p(n){let r={hasErrorBoundary:n.hasErrorBoundary||n.ErrorBoundary!=null||n.errorElement!=null};return n.Component&&(n.element&&rt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(r,{element:S.createElement(n.Component),Component:void 0})),n.HydrateFallback&&(n.hydrateFallbackElement&&rt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(r,{hydrateFallbackElement:S.createElement(n.HydrateFallback),HydrateFallback:void 0})),n.ErrorBoundary&&(n.errorElement&&rt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(r,{errorElement:S.createElement(n.ErrorBoundary),ErrorBoundary:void 0})),r}var Fp=["HydrateFallback","hydrateFallbackElement"],Wp=class{constructor(){this.status="pending",this.promise=new Promise((n,r)=>{this.resolve=o=>{this.status==="pending"&&(this.status="resolved",n(o))},this.reject=o=>{this.status==="pending"&&(this.status="rejected",r(o))}})}};function Pp({router:n,flushSync:r}){let[o,c]=S.useState(n.state),[f,d]=S.useState(),[y,g]=S.useState({isTransitioning:!1}),[h,v]=S.useState(),[p,A]=S.useState(),[x,C]=S.useState(),w=S.useRef(new Map),j=S.useCallback((K,{deletedFetchers:ue,flushSync:P,viewTransitionOpts:U})=>{K.fetchers.forEach((F,ce)=>{F.data!==void 0&&w.current.set(ce,F.data)}),ue.forEach(F=>w.current.delete(F)),Im(P===!1||r!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let oe=n.window!=null&&n.window.document!=null&&typeof n.window.document.startViewTransition=="function";if(Im(U==null||oe,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!U||!oe){r&&P?r(()=>c(K)):S.startTransition(()=>c(K));return}if(r&&P){r(()=>{p&&(h&&h.resolve(),p.skipTransition()),g({isTransitioning:!0,flushSync:!0,currentLocation:U.currentLocation,nextLocation:U.nextLocation})});let F=n.window.document.startViewTransition(()=>{r(()=>c(K))});F.finished.finally(()=>{r(()=>{v(void 0),A(void 0),d(void 0),g({isTransitioning:!1})})}),r(()=>A(F));return}p?(h&&h.resolve(),p.skipTransition(),C({state:K,currentLocation:U.currentLocation,nextLocation:U.nextLocation})):(d(K),g({isTransitioning:!0,flushSync:!1,currentLocation:U.currentLocation,nextLocation:U.nextLocation}))},[n.window,r,p,h]);S.useLayoutEffect(()=>n.subscribe(j),[n,j]),S.useEffect(()=>{y.isTransitioning&&!y.flushSync&&v(new Wp)},[y]),S.useEffect(()=>{if(h&&f&&n.window){let K=f,ue=h.promise,P=n.window.document.startViewTransition(async()=>{S.startTransition(()=>c(K)),await ue});P.finished.finally(()=>{v(void 0),A(void 0),d(void 0),g({isTransitioning:!1})}),A(P)}},[f,h,n.window]),S.useEffect(()=>{h&&f&&o.location.key===f.location.key&&h.resolve()},[h,p,o.location,f]),S.useEffect(()=>{!y.isTransitioning&&x&&(d(x.state),g({isTransitioning:!0,flushSync:!1,currentLocation:x.currentLocation,nextLocation:x.nextLocation}),C(void 0))},[y.isTransitioning,x]);let k=S.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:K=>n.navigate(K),push:(K,ue,P)=>n.navigate(K,{state:ue,preventScrollReset:P?.preventScrollReset}),replace:(K,ue,P)=>n.navigate(K,{replace:!0,state:ue,preventScrollReset:P?.preventScrollReset})}),[n]),B=n.basename||"/",ne=S.useMemo(()=>({router:n,navigator:k,static:!1,basename:B}),[n,k,B]);return S.createElement(S.Fragment,null,S.createElement(en.Provider,{value:ne},S.createElement(tr.Provider,{value:o},S.createElement(Oy.Provider,{value:w.current},S.createElement(Df.Provider,{value:y},S.createElement(tb,{basename:B,location:o.location,navigationType:o.historyAction,navigator:k},S.createElement(Ip,{routes:n.routes,future:n.future,state:o})))))),null)}var Ip=S.memo(eb);function eb({routes:n,future:r,state:o}){return Bp(n,void 0,o,r)}function DS({to:n,replace:r,state:o,relative:c}){Ce(Xn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:f}=S.useContext(hl);rt(!f,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:d}=S.useContext(Rl),{pathname:y}=kl(),g=Cf(),h=Hi(n,Li(d),y,c==="path"),v=JSON.stringify(h);return S.useEffect(()=>{g(JSON.parse(v),{replace:r,state:o,relative:c})},[g,v,c,r,o]),null}function tb({basename:n="/",children:r=null,location:o,navigationType:c="POP",navigator:f,static:d=!1}){Ce(!Xn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let y=n.replace(/^\/*/,"/"),g=S.useMemo(()=>({basename:y,navigator:f,static:d,future:{}}),[y,f,d]);typeof o=="string"&&(o=wa(o));let{pathname:h="/",search:v="",hash:p="",state:A=null,key:x="default"}=o,C=S.useMemo(()=>{let w=nl(h,y);return w==null?null:{location:{pathname:w,search:v,hash:p,state:A,key:x},navigationType:c}},[y,h,v,p,A,x,c]);return rt(C!=null,`<Router basename="${y}"> is not able to match the URL "${h}${v}${p}" because it does not start with the basename, so the <Router> won't render anything.`),C==null?null:S.createElement(hl.Provider,{value:g},S.createElement(Bi.Provider,{children:r,value:C}))}var zi="get",Oi="application/x-www-form-urlencoded";function ji(n){return n!=null&&typeof n.tagName=="string"}function lb(n){return ji(n)&&n.tagName.toLowerCase()==="button"}function ab(n){return ji(n)&&n.tagName.toLowerCase()==="form"}function nb(n){return ji(n)&&n.tagName.toLowerCase()==="input"}function ub(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function rb(n,r){return n.button===0&&(!r||r==="_self")&&!ub(n)}function bf(n=""){return new URLSearchParams(typeof n=="string"||Array.isArray(n)||n instanceof URLSearchParams?n:Object.keys(n).reduce((r,o)=>{let c=n[o];return r.concat(Array.isArray(c)?c.map(f=>[o,f]):[[o,c]])},[]))}function ib(n,r){let o=bf(n);return r&&r.forEach((c,f)=>{o.has(f)||r.getAll(f).forEach(d=>{o.append(f,d)})}),o}var Si=null;function ob(){if(Si===null)try{new FormData(document.createElement("form"),0),Si=!1}catch{Si=!0}return Si}var cb=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function cf(n){return n!=null&&!cb.has(n)?(rt(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Oi}"`),null):n}function fb(n,r){let o,c,f,d,y;if(ab(n)){let g=n.getAttribute("action");c=g?nl(g,r):null,o=n.getAttribute("method")||zi,f=cf(n.getAttribute("enctype"))||Oi,d=new FormData(n)}else if(lb(n)||nb(n)&&(n.type==="submit"||n.type==="image")){let g=n.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let h=n.getAttribute("formaction")||g.getAttribute("action");if(c=h?nl(h,r):null,o=n.getAttribute("formmethod")||g.getAttribute("method")||zi,f=cf(n.getAttribute("formenctype"))||cf(g.getAttribute("enctype"))||Oi,d=new FormData(g,n),!ob()){let{name:v,type:p,value:A}=n;if(p==="image"){let x=v?`${v}.`:"";d.append(`${x}x`,"0"),d.append(`${x}y`,"0")}else v&&d.append(v,A)}}else{if(ji(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=zi,c=null,f=Oi,y=n}return d&&f==="text/plain"&&(y=d,d=void 0),{action:c,method:o.toLowerCase(),encType:f,formData:d,body:y}}function Lf(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}async function sb(n,r){if(n.id in r)return r[n.id];try{let o=await import(n.module);return r[n.id]=o,o}catch(o){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function db(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function hb(n,r,o){let c=await Promise.all(n.map(async f=>{let d=r.routes[f.route.id];if(d){let y=await sb(d,o);return y.links?y.links():[]}return[]}));return gb(c.flat(1).filter(db).filter(f=>f.rel==="stylesheet"||f.rel==="preload").map(f=>f.rel==="stylesheet"?{...f,rel:"prefetch",as:"style"}:{...f,rel:"prefetch"}))}function ey(n,r,o,c,f,d){let y=(h,v)=>o[v]?h.route.id!==o[v].route.id:!0,g=(h,v)=>o[v].pathname!==h.pathname||o[v].route.path?.endsWith("*")&&o[v].params["*"]!==h.params["*"];return d==="assets"?r.filter((h,v)=>y(h,v)||g(h,v)):d==="data"?r.filter((h,v)=>{let p=c.routes[h.route.id];if(!p||!p.hasLoader)return!1;if(y(h,v)||g(h,v))return!0;if(h.route.shouldRevalidate){let A=h.route.shouldRevalidate({currentUrl:new URL(f.pathname+f.search+f.hash,window.origin),currentParams:o[0]?.params||{},nextUrl:new URL(n,window.origin),nextParams:h.params,defaultShouldRevalidate:!0});if(typeof A=="boolean")return A}return!0}):[]}function mb(n,r,{includeHydrateFallback:o}={}){return yb(n.map(c=>{let f=r.routes[c.route.id];if(!f)return[];let d=[f.module];return f.clientActionModule&&(d=d.concat(f.clientActionModule)),f.clientLoaderModule&&(d=d.concat(f.clientLoaderModule)),o&&f.hydrateFallbackModule&&(d=d.concat(f.hydrateFallbackModule)),f.imports&&(d=d.concat(f.imports)),d}).flat(1))}function yb(n){return[...new Set(n)]}function vb(n){let r={},o=Object.keys(n).sort();for(let c of o)r[c]=n[c];return r}function gb(n,r){let o=new Set;return new Set(r),n.reduce((c,f)=>{let d=JSON.stringify(vb(f));return o.has(d)||(o.add(d),c.push({key:d,link:f})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var pb=new Set([100,101,204,205]);function bb(n,r){let o=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return o.pathname==="/"?o.pathname="_root.data":r&&nl(o.pathname,r)==="/"?o.pathname=`${r.replace(/\/$/,"")}/_root.data`:o.pathname=`${o.pathname.replace(/\/$/,"")}.data`,o}function Uy(){let n=S.useContext(en);return Lf(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function Sb(){let n=S.useContext(tr);return Lf(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Hf=S.createContext(void 0);Hf.displayName="FrameworkContext";function Ny(){let n=S.useContext(Hf);return Lf(n,"You must render this element inside a <HydratedRouter> element"),n}function Eb(n,r){let o=S.useContext(Hf),[c,f]=S.useState(!1),[d,y]=S.useState(!1),{onFocus:g,onBlur:h,onMouseEnter:v,onMouseLeave:p,onTouchStart:A}=r,x=S.useRef(null);S.useEffect(()=>{if(n==="render"&&y(!0),n==="viewport"){let j=B=>{B.forEach(ne=>{y(ne.isIntersecting)})},k=new IntersectionObserver(j,{threshold:.5});return x.current&&k.observe(x.current),()=>{k.disconnect()}}},[n]),S.useEffect(()=>{if(c){let j=setTimeout(()=>{y(!0)},100);return()=>{clearTimeout(j)}}},[c]);let C=()=>{f(!0)},w=()=>{f(!1),y(!1)};return o?n!=="intent"?[d,x,{}]:[d,x,{onFocus:Ju(g,C),onBlur:Ju(h,w),onMouseEnter:Ju(v,C),onMouseLeave:Ju(p,w),onTouchStart:Ju(A,C)}]:[!1,x,{}]}function Ju(n,r){return o=>{n&&n(o),o.defaultPrevented||r(o)}}function Rb({page:n,...r}){let{router:o}=Uy(),c=S.useMemo(()=>ba(o.routes,n,o.basename),[o.routes,n,o.basename]);return c?S.createElement(xb,{page:n,matches:c,...r}):null}function Tb(n){let{manifest:r,routeModules:o}=Ny(),[c,f]=S.useState([]);return S.useEffect(()=>{let d=!1;return hb(n,r,o).then(y=>{d||f(y)}),()=>{d=!0}},[n,r,o]),c}function xb({page:n,matches:r,...o}){let c=kl(),{manifest:f,routeModules:d}=Ny(),{basename:y}=Uy(),{loaderData:g,matches:h}=Sb(),v=S.useMemo(()=>ey(n,r,h,f,c,"data"),[n,r,h,f,c]),p=S.useMemo(()=>ey(n,r,h,f,c,"assets"),[n,r,h,f,c]),A=S.useMemo(()=>{if(n===c.pathname+c.search+c.hash)return[];let w=new Set,j=!1;if(r.forEach(B=>{let ne=f.routes[B.route.id];!ne||!ne.hasLoader||(!v.some(K=>K.route.id===B.route.id)&&B.route.id in g&&d[B.route.id]?.shouldRevalidate||ne.hasClientLoader?j=!0:w.add(B.route.id))}),w.size===0)return[];let k=bb(n,y);return j&&w.size>0&&k.searchParams.set("_routes",r.filter(B=>w.has(B.route.id)).map(B=>B.route.id).join(",")),[k.pathname+k.search]},[y,g,c,f,v,r,n,d]),x=S.useMemo(()=>mb(p,f),[p,f]),C=Tb(p);return S.createElement(S.Fragment,null,A.map(w=>S.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...o})),x.map(w=>S.createElement("link",{key:w,rel:"modulepreload",href:w,...o})),C.map(({key:w,link:j})=>S.createElement("link",{key:w,...j})))}function wb(...n){return r=>{n.forEach(o=>{typeof o=="function"?o(r):o!=null&&(o.current=r)})}}var Ly=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ly&&(window.__reactRouterVersion="7.6.3")}catch{}function _S(n,r){return yp({basename:r?.basename,unstable_getContext:r?.unstable_getContext,future:r?.future,history:H0({window:r?.window}),hydrationData:Mb(),routes:n,mapRouteProperties:$p,hydrationRouteProperties:Fp,dataStrategy:r?.dataStrategy,patchRoutesOnNavigation:r?.patchRoutesOnNavigation,window:r?.window}).initialize()}function Mb(){let n=window?.__staticRouterHydrationData;return n&&n.errors&&(n={...n,errors:Ab(n.errors)}),n}function Ab(n){if(!n)return null;let r=Object.entries(n),o={};for(let[c,f]of r)if(f&&f.__type==="RouteErrorResponse")o[c]=new Ui(f.status,f.statusText,f.data,f.internal===!0);else if(f&&f.__type==="Error"){if(f.__subType){let d=window[f.__subType];if(typeof d=="function")try{let y=new d(f.message);y.stack="",o[c]=y}catch{}}if(o[c]==null){let d=new Error(f.message);d.stack="",o[c]=d}}else o[c]=f;return o}var Hy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,By=S.forwardRef(function({onClick:r,discover:o="render",prefetch:c="none",relative:f,reloadDocument:d,replace:y,state:g,target:h,to:v,preventScrollReset:p,viewTransition:A,...x},C){let{basename:w}=S.useContext(hl),j=typeof v=="string"&&Hy.test(v),k,B=!1;if(typeof v=="string"&&j&&(k=v,Ly))try{let ce=new URL(window.location.href),Me=v.startsWith("//")?new URL(ce.protocol+v):new URL(v),Qe=nl(Me.pathname,w);Me.origin===ce.origin&&Qe!=null?v=Qe+Me.search+Me.hash:B=!0}catch{rt(!1,`<Link to="${v}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let ne=Lp(v,{relative:f}),[K,ue,P]=Eb(c,x),U=_b(v,{replace:y,state:g,target:h,preventScrollReset:p,relative:f,viewTransition:A});function oe(ce){r&&r(ce),ce.defaultPrevented||U(ce)}let F=S.createElement("a",{...x,...P,href:k||ne,onClick:B||d?r:oe,ref:wb(C,ue),target:h,"data-discover":!j&&o==="render"?"true":void 0});return K&&!j?S.createElement(S.Fragment,null,F,S.createElement(Rb,{page:ne})):F});By.displayName="Link";var zb=S.forwardRef(function({"aria-current":r="page",caseSensitive:o=!1,className:c="",end:f=!1,style:d,to:y,viewTransition:g,children:h,...v},p){let A=lr(y,{relative:v.relative}),x=kl(),C=S.useContext(tr),{navigator:w,basename:j}=S.useContext(hl),k=C!=null&&Hb(A)&&g===!0,B=w.encodeLocation?w.encodeLocation(A).pathname:A.pathname,ne=x.pathname,K=C&&C.navigation&&C.navigation.location?C.navigation.location.pathname:null;o||(ne=ne.toLowerCase(),K=K?K.toLowerCase():null,B=B.toLowerCase()),K&&j&&(K=nl(K,j)||K);const ue=B!=="/"&&B.endsWith("/")?B.length-1:B.length;let P=ne===B||!f&&ne.startsWith(B)&&ne.charAt(ue)==="/",U=K!=null&&(K===B||!f&&K.startsWith(B)&&K.charAt(B.length)==="/"),oe={isActive:P,isPending:U,isTransitioning:k},F=P?r:void 0,ce;typeof c=="function"?ce=c(oe):ce=[c,P?"active":null,U?"pending":null,k?"transitioning":null].filter(Boolean).join(" ");let Me=typeof d=="function"?d(oe):d;return S.createElement(By,{...v,"aria-current":F,className:ce,ref:p,style:Me,to:y,viewTransition:g},typeof h=="function"?h(oe):h)});zb.displayName="NavLink";var Ob=S.forwardRef(({discover:n="render",fetcherKey:r,navigate:o,reloadDocument:c,replace:f,state:d,method:y=zi,action:g,onSubmit:h,relative:v,preventScrollReset:p,viewTransition:A,...x},C)=>{let w=Nb(),j=Lb(g,{relative:v}),k=y.toLowerCase()==="get"?"get":"post",B=typeof g=="string"&&Hy.test(g),ne=K=>{if(h&&h(K),K.defaultPrevented)return;K.preventDefault();let ue=K.nativeEvent.submitter,P=ue?.getAttribute("formmethod")||y;w(ue||K.currentTarget,{fetcherKey:r,method:P,navigate:o,replace:f,state:d,relative:v,preventScrollReset:p,viewTransition:A})};return S.createElement("form",{ref:C,method:k,action:j,onSubmit:c?h:ne,...x,"data-discover":!B&&n==="render"?"true":void 0})});Ob.displayName="Form";function Db(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function jy(n){let r=S.useContext(en);return Ce(r,Db(n)),r}function _b(n,{target:r,replace:o,state:c,preventScrollReset:f,relative:d,viewTransition:y}={}){let g=Cf(),h=kl(),v=lr(n,{relative:d});return S.useCallback(p=>{if(rb(p,r)){p.preventDefault();let A=o!==void 0?o:xa(h)===xa(v);g(n,{replace:A,state:c,preventScrollReset:f,relative:d,viewTransition:y})}},[h,g,v,o,c,r,n,f,d,y])}function CS(n){rt(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let r=S.useRef(bf(n)),o=S.useRef(!1),c=kl(),f=S.useMemo(()=>ib(c.search,o.current?null:r.current),[c.search]),d=Cf(),y=S.useCallback((g,h)=>{const v=bf(typeof g=="function"?g(f):g);o.current=!0,d("?"+v,h)},[d,f]);return[f,y]}var Cb=0,Ub=()=>`__${String(++Cb)}__`;function Nb(){let{router:n}=jy("useSubmit"),{basename:r}=S.useContext(hl),o=Zp();return S.useCallback(async(c,f={})=>{let{action:d,method:y,encType:g,formData:h,body:v}=fb(c,r);if(f.navigate===!1){let p=f.fetcherKey||Ub();await n.fetch(p,o,f.action||d,{preventScrollReset:f.preventScrollReset,formData:h,body:v,formMethod:f.method||y,formEncType:f.encType||g,flushSync:f.flushSync})}else await n.navigate(f.action||d,{preventScrollReset:f.preventScrollReset,formData:h,body:v,formMethod:f.method||y,formEncType:f.encType||g,replace:f.replace,state:f.state,fromRouteId:o,flushSync:f.flushSync,viewTransition:f.viewTransition})},[n,r,o])}function Lb(n,{relative:r}={}){let{basename:o}=S.useContext(hl),c=S.useContext(Rl);Ce(c,"useFormAction must be used inside a RouteContext");let[f]=c.matches.slice(-1),d={...lr(n||".",{relative:r})},y=kl();if(n==null){d.search=y.search;let g=new URLSearchParams(d.search),h=g.getAll("index");if(h.some(p=>p==="")){g.delete("index"),h.filter(A=>A).forEach(A=>g.append("index",A));let p=g.toString();d.search=p?`?${p}`:""}}return(!n||n===".")&&f.route.index&&(d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(d.pathname=d.pathname==="/"?o:El([o,d.pathname])),xa(d)}function Hb(n,r={}){let o=S.useContext(Df);Ce(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=jy("useViewTransitionState"),f=lr(n,{relative:r.relative});if(!o.isTransitioning)return!1;let d=nl(o.currentLocation.pathname,c)||o.currentLocation.pathname,y=nl(o.nextLocation.pathname,c)||o.nextLocation.pathname;return Ci(f.pathname,y)!=null||Ci(f.pathname,d)!=null}[...pb];var Bb=hy();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function US(n){return S.createElement(Pp,{flushSync:Bb.flushSync,...n})}const jb=n=>n;function Yb(n,r=jb){const o=pt.useSyncExternalStore(n.subscribe,()=>r(n.getState()),()=>r(n.getInitialState()));return pt.useDebugValue(o),o}const qb=n=>{const r=b0(n),o=c=>Yb(r,c);return Object.assign(o,r),o},NS=n=>qb,Bf="-",Gb=n=>{const r=kb(n),{conflictingClassGroups:o,conflictingClassGroupModifiers:c}=n;return{getClassGroupId:y=>{const g=y.split(Bf);return g[0]===""&&g.length!==1&&g.shift(),Yy(g,r)||Xb(y)},getConflictingClassGroupIds:(y,g)=>{const h=o[y]||[];return g&&c[y]?[...h,...c[y]]:h}}},Yy=(n,r)=>{if(n.length===0)return r.classGroupId;const o=n[0],c=r.nextPart.get(o),f=c?Yy(n.slice(1),c):void 0;if(f)return f;if(r.validators.length===0)return;const d=n.join(Bf);return r.validators.find(({validator:y})=>y(d))?.classGroupId},ty=/^\[(.+)\]$/,Xb=n=>{if(ty.test(n)){const r=ty.exec(n)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},kb=n=>{const{theme:r,classGroups:o}=n,c={nextPart:new Map,validators:[]};for(const f in o)Sf(o[f],c,f,r);return c},Sf=(n,r,o,c)=>{n.forEach(f=>{if(typeof f=="string"){const d=f===""?r:ly(r,f);d.classGroupId=o;return}if(typeof f=="function"){if(Vb(f)){Sf(f(c),r,o,c);return}r.validators.push({validator:f,classGroupId:o});return}Object.entries(f).forEach(([d,y])=>{Sf(y,ly(r,d),o,c)})})},ly=(n,r)=>{let o=n;return r.split(Bf).forEach(c=>{o.nextPart.has(c)||o.nextPart.set(c,{nextPart:new Map,validators:[]}),o=o.nextPart.get(c)}),o},Vb=n=>n.isThemeGetter,Qb=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,o=new Map,c=new Map;const f=(d,y)=>{o.set(d,y),r++,r>n&&(r=0,c=o,o=new Map)};return{get(d){let y=o.get(d);if(y!==void 0)return y;if((y=c.get(d))!==void 0)return f(d,y),y},set(d,y){o.has(d)?o.set(d,y):f(d,y)}}},Ef="!",Rf=":",Zb=Rf.length,Kb=n=>{const{prefix:r,experimentalParseClassName:o}=n;let c=f=>{const d=[];let y=0,g=0,h=0,v;for(let w=0;w<f.length;w++){let j=f[w];if(y===0&&g===0){if(j===Rf){d.push(f.slice(h,w)),h=w+Zb;continue}if(j==="/"){v=w;continue}}j==="["?y++:j==="]"?y--:j==="("?g++:j===")"&&g--}const p=d.length===0?f:f.substring(h),A=Jb(p),x=A!==p,C=v&&v>h?v-h:void 0;return{modifiers:d,hasImportantModifier:x,baseClassName:A,maybePostfixModifierPosition:C}};if(r){const f=r+Rf,d=c;c=y=>y.startsWith(f)?d(y.substring(f.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:y,maybePostfixModifierPosition:void 0}}if(o){const f=c;c=d=>o({className:d,parseClassName:f})}return c},Jb=n=>n.endsWith(Ef)?n.substring(0,n.length-1):n.startsWith(Ef)?n.substring(1):n,$b=n=>{const r=Object.fromEntries(n.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const f=[];let d=[];return c.forEach(y=>{y[0]==="["||r[y]?(f.push(...d.sort(),y),d=[]):d.push(y)}),f.push(...d.sort()),f}},Fb=n=>({cache:Qb(n.cacheSize),parseClassName:Kb(n),sortModifiers:$b(n),...Gb(n)}),Wb=/\s+/,Pb=(n,r)=>{const{parseClassName:o,getClassGroupId:c,getConflictingClassGroupIds:f,sortModifiers:d}=r,y=[],g=n.trim().split(Wb);let h="";for(let v=g.length-1;v>=0;v-=1){const p=g[v],{isExternal:A,modifiers:x,hasImportantModifier:C,baseClassName:w,maybePostfixModifierPosition:j}=o(p);if(A){h=p+(h.length>0?" "+h:h);continue}let k=!!j,B=c(k?w.substring(0,j):w);if(!B){if(!k){h=p+(h.length>0?" "+h:h);continue}if(B=c(w),!B){h=p+(h.length>0?" "+h:h);continue}k=!1}const ne=d(x).join(":"),K=C?ne+Ef:ne,ue=K+B;if(y.includes(ue))continue;y.push(ue);const P=f(B,k);for(let U=0;U<P.length;++U){const oe=P[U];y.push(K+oe)}h=p+(h.length>0?" "+h:h)}return h};function Ib(){let n=0,r,o,c="";for(;n<arguments.length;)(r=arguments[n++])&&(o=qy(r))&&(c&&(c+=" "),c+=o);return c}const qy=n=>{if(typeof n=="string")return n;let r,o="";for(let c=0;c<n.length;c++)n[c]&&(r=qy(n[c]))&&(o&&(o+=" "),o+=r);return o};function e1(n,...r){let o,c,f,d=y;function y(h){const v=r.reduce((p,A)=>A(p),n());return o=Fb(v),c=o.cache.get,f=o.cache.set,d=g,g(h)}function g(h){const v=c(h);if(v)return v;const p=Pb(h,o);return f(h,p),p}return function(){return d(Ib.apply(null,arguments))}}const mt=n=>{const r=o=>o[n]||[];return r.isThemeGetter=!0,r},Gy=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Xy=/^\((?:(\w[\w-]*):)?(.+)\)$/i,t1=/^\d+\/\d+$/,l1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,a1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,n1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,u1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,r1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,jn=n=>t1.test(n),ze=n=>!!n&&!Number.isNaN(Number(n)),ga=n=>!!n&&Number.isInteger(Number(n)),ff=n=>n.endsWith("%")&&ze(n.slice(0,-1)),ql=n=>l1.test(n),i1=()=>!0,o1=n=>a1.test(n)&&!n1.test(n),ky=()=>!1,c1=n=>u1.test(n),f1=n=>r1.test(n),s1=n=>!le(n)&&!ae(n),d1=n=>kn(n,Zy,ky),le=n=>Gy.test(n),$a=n=>kn(n,Ky,o1),sf=n=>kn(n,g1,ze),ay=n=>kn(n,Vy,ky),h1=n=>kn(n,Qy,f1),Ei=n=>kn(n,Jy,c1),ae=n=>Xy.test(n),$u=n=>Vn(n,Ky),m1=n=>Vn(n,p1),ny=n=>Vn(n,Vy),y1=n=>Vn(n,Zy),v1=n=>Vn(n,Qy),Ri=n=>Vn(n,Jy,!0),kn=(n,r,o)=>{const c=Gy.exec(n);return c?c[1]?r(c[1]):o(c[2]):!1},Vn=(n,r,o=!1)=>{const c=Xy.exec(n);return c?c[1]?r(c[1]):o:!1},Vy=n=>n==="position"||n==="percentage",Qy=n=>n==="image"||n==="url",Zy=n=>n==="length"||n==="size"||n==="bg-size",Ky=n=>n==="length",g1=n=>n==="number",p1=n=>n==="family-name",Jy=n=>n==="shadow",b1=()=>{const n=mt("color"),r=mt("font"),o=mt("text"),c=mt("font-weight"),f=mt("tracking"),d=mt("leading"),y=mt("breakpoint"),g=mt("container"),h=mt("spacing"),v=mt("radius"),p=mt("shadow"),A=mt("inset-shadow"),x=mt("text-shadow"),C=mt("drop-shadow"),w=mt("blur"),j=mt("perspective"),k=mt("aspect"),B=mt("ease"),ne=mt("animate"),K=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ue=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...ue(),ae,le],U=()=>["auto","hidden","clip","visible","scroll"],oe=()=>["auto","contain","none"],F=()=>[ae,le,h],ce=()=>[jn,"full","auto",...F()],Me=()=>[ga,"none","subgrid",ae,le],Qe=()=>["auto",{span:["full",ga,ae,le]},ga,ae,le],Ye=()=>[ga,"auto",ae,le],Te=()=>["auto","min","max","fr",ae,le],xe=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],we=()=>["start","end","center","stretch","center-safe","end-safe"],D=()=>["auto",...F()],J=()=>[jn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...F()],X=()=>[n,ae,le],Se=()=>[...ue(),ny,ay,{position:[ae,le]}],E=()=>["no-repeat",{repeat:["","x","y","space","round"]}],q=()=>["auto","cover","contain",y1,d1,{size:[ae,le]}],W=()=>[ff,$u,$a],V=()=>["","none","full",v,ae,le],I=()=>["",ze,$u,$a],ve=()=>["solid","dashed","dotted","double"],se=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],Oe=()=>[ze,ff,ny,ay],He=()=>["","none",w,ae,le],it=()=>["none",ze,ae,le],Pe=()=>["none",ze,ae,le],Mt=()=>[ze,ae,le],ul=()=>[jn,"full",...F()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ql],breakpoint:[ql],color:[i1],container:[ql],"drop-shadow":[ql],ease:["in","out","in-out"],font:[s1],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ql],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ql],shadow:[ql],spacing:["px",ze],text:[ql],"text-shadow":[ql],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",jn,le,ae,k]}],container:["container"],columns:[{columns:[ze,le,ae,g]}],"break-after":[{"break-after":K()}],"break-before":[{"break-before":K()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:U()}],"overflow-x":[{"overflow-x":U()}],"overflow-y":[{"overflow-y":U()}],overscroll:[{overscroll:oe()}],"overscroll-x":[{"overscroll-x":oe()}],"overscroll-y":[{"overscroll-y":oe()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ce()}],"inset-x":[{"inset-x":ce()}],"inset-y":[{"inset-y":ce()}],start:[{start:ce()}],end:[{end:ce()}],top:[{top:ce()}],right:[{right:ce()}],bottom:[{bottom:ce()}],left:[{left:ce()}],visibility:["visible","invisible","collapse"],z:[{z:[ga,"auto",ae,le]}],basis:[{basis:[jn,"full","auto",g,...F()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ze,jn,"auto","initial","none",le]}],grow:[{grow:["",ze,ae,le]}],shrink:[{shrink:["",ze,ae,le]}],order:[{order:[ga,"first","last","none",ae,le]}],"grid-cols":[{"grid-cols":Me()}],"col-start-end":[{col:Qe()}],"col-start":[{"col-start":Ye()}],"col-end":[{"col-end":Ye()}],"grid-rows":[{"grid-rows":Me()}],"row-start-end":[{row:Qe()}],"row-start":[{"row-start":Ye()}],"row-end":[{"row-end":Ye()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Te()}],"auto-rows":[{"auto-rows":Te()}],gap:[{gap:F()}],"gap-x":[{"gap-x":F()}],"gap-y":[{"gap-y":F()}],"justify-content":[{justify:[...xe(),"normal"]}],"justify-items":[{"justify-items":[...we(),"normal"]}],"justify-self":[{"justify-self":["auto",...we()]}],"align-content":[{content:["normal",...xe()]}],"align-items":[{items:[...we(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...we(),{baseline:["","last"]}]}],"place-content":[{"place-content":xe()}],"place-items":[{"place-items":[...we(),"baseline"]}],"place-self":[{"place-self":["auto",...we()]}],p:[{p:F()}],px:[{px:F()}],py:[{py:F()}],ps:[{ps:F()}],pe:[{pe:F()}],pt:[{pt:F()}],pr:[{pr:F()}],pb:[{pb:F()}],pl:[{pl:F()}],m:[{m:D()}],mx:[{mx:D()}],my:[{my:D()}],ms:[{ms:D()}],me:[{me:D()}],mt:[{mt:D()}],mr:[{mr:D()}],mb:[{mb:D()}],ml:[{ml:D()}],"space-x":[{"space-x":F()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":F()}],"space-y-reverse":["space-y-reverse"],size:[{size:J()}],w:[{w:[g,"screen",...J()]}],"min-w":[{"min-w":[g,"screen","none",...J()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[y]},...J()]}],h:[{h:["screen","lh",...J()]}],"min-h":[{"min-h":["screen","lh","none",...J()]}],"max-h":[{"max-h":["screen","lh",...J()]}],"font-size":[{text:["base",o,$u,$a]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ae,sf]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ff,le]}],"font-family":[{font:[m1,le,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[f,ae,le]}],"line-clamp":[{"line-clamp":[ze,"none",ae,sf]}],leading:[{leading:[d,...F()]}],"list-image":[{"list-image":["none",ae,le]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ae,le]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:X()}],"text-color":[{text:X()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ve(),"wavy"]}],"text-decoration-thickness":[{decoration:[ze,"from-font","auto",ae,$a]}],"text-decoration-color":[{decoration:X()}],"underline-offset":[{"underline-offset":[ze,"auto",ae,le]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:F()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ae,le]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ae,le]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Se()}],"bg-repeat":[{bg:E()}],"bg-size":[{bg:q()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ga,ae,le],radial:["",ae,le],conic:[ga,ae,le]},v1,h1]}],"bg-color":[{bg:X()}],"gradient-from-pos":[{from:W()}],"gradient-via-pos":[{via:W()}],"gradient-to-pos":[{to:W()}],"gradient-from":[{from:X()}],"gradient-via":[{via:X()}],"gradient-to":[{to:X()}],rounded:[{rounded:V()}],"rounded-s":[{"rounded-s":V()}],"rounded-e":[{"rounded-e":V()}],"rounded-t":[{"rounded-t":V()}],"rounded-r":[{"rounded-r":V()}],"rounded-b":[{"rounded-b":V()}],"rounded-l":[{"rounded-l":V()}],"rounded-ss":[{"rounded-ss":V()}],"rounded-se":[{"rounded-se":V()}],"rounded-ee":[{"rounded-ee":V()}],"rounded-es":[{"rounded-es":V()}],"rounded-tl":[{"rounded-tl":V()}],"rounded-tr":[{"rounded-tr":V()}],"rounded-br":[{"rounded-br":V()}],"rounded-bl":[{"rounded-bl":V()}],"border-w":[{border:I()}],"border-w-x":[{"border-x":I()}],"border-w-y":[{"border-y":I()}],"border-w-s":[{"border-s":I()}],"border-w-e":[{"border-e":I()}],"border-w-t":[{"border-t":I()}],"border-w-r":[{"border-r":I()}],"border-w-b":[{"border-b":I()}],"border-w-l":[{"border-l":I()}],"divide-x":[{"divide-x":I()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":I()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ve(),"hidden","none"]}],"divide-style":[{divide:[...ve(),"hidden","none"]}],"border-color":[{border:X()}],"border-color-x":[{"border-x":X()}],"border-color-y":[{"border-y":X()}],"border-color-s":[{"border-s":X()}],"border-color-e":[{"border-e":X()}],"border-color-t":[{"border-t":X()}],"border-color-r":[{"border-r":X()}],"border-color-b":[{"border-b":X()}],"border-color-l":[{"border-l":X()}],"divide-color":[{divide:X()}],"outline-style":[{outline:[...ve(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ze,ae,le]}],"outline-w":[{outline:["",ze,$u,$a]}],"outline-color":[{outline:X()}],shadow:[{shadow:["","none",p,Ri,Ei]}],"shadow-color":[{shadow:X()}],"inset-shadow":[{"inset-shadow":["none",A,Ri,Ei]}],"inset-shadow-color":[{"inset-shadow":X()}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:X()}],"ring-offset-w":[{"ring-offset":[ze,$a]}],"ring-offset-color":[{"ring-offset":X()}],"inset-ring-w":[{"inset-ring":I()}],"inset-ring-color":[{"inset-ring":X()}],"text-shadow":[{"text-shadow":["none",x,Ri,Ei]}],"text-shadow-color":[{"text-shadow":X()}],opacity:[{opacity:[ze,ae,le]}],"mix-blend":[{"mix-blend":[...se(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":se()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ze]}],"mask-image-linear-from-pos":[{"mask-linear-from":Oe()}],"mask-image-linear-to-pos":[{"mask-linear-to":Oe()}],"mask-image-linear-from-color":[{"mask-linear-from":X()}],"mask-image-linear-to-color":[{"mask-linear-to":X()}],"mask-image-t-from-pos":[{"mask-t-from":Oe()}],"mask-image-t-to-pos":[{"mask-t-to":Oe()}],"mask-image-t-from-color":[{"mask-t-from":X()}],"mask-image-t-to-color":[{"mask-t-to":X()}],"mask-image-r-from-pos":[{"mask-r-from":Oe()}],"mask-image-r-to-pos":[{"mask-r-to":Oe()}],"mask-image-r-from-color":[{"mask-r-from":X()}],"mask-image-r-to-color":[{"mask-r-to":X()}],"mask-image-b-from-pos":[{"mask-b-from":Oe()}],"mask-image-b-to-pos":[{"mask-b-to":Oe()}],"mask-image-b-from-color":[{"mask-b-from":X()}],"mask-image-b-to-color":[{"mask-b-to":X()}],"mask-image-l-from-pos":[{"mask-l-from":Oe()}],"mask-image-l-to-pos":[{"mask-l-to":Oe()}],"mask-image-l-from-color":[{"mask-l-from":X()}],"mask-image-l-to-color":[{"mask-l-to":X()}],"mask-image-x-from-pos":[{"mask-x-from":Oe()}],"mask-image-x-to-pos":[{"mask-x-to":Oe()}],"mask-image-x-from-color":[{"mask-x-from":X()}],"mask-image-x-to-color":[{"mask-x-to":X()}],"mask-image-y-from-pos":[{"mask-y-from":Oe()}],"mask-image-y-to-pos":[{"mask-y-to":Oe()}],"mask-image-y-from-color":[{"mask-y-from":X()}],"mask-image-y-to-color":[{"mask-y-to":X()}],"mask-image-radial":[{"mask-radial":[ae,le]}],"mask-image-radial-from-pos":[{"mask-radial-from":Oe()}],"mask-image-radial-to-pos":[{"mask-radial-to":Oe()}],"mask-image-radial-from-color":[{"mask-radial-from":X()}],"mask-image-radial-to-color":[{"mask-radial-to":X()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ue()}],"mask-image-conic-pos":[{"mask-conic":[ze]}],"mask-image-conic-from-pos":[{"mask-conic-from":Oe()}],"mask-image-conic-to-pos":[{"mask-conic-to":Oe()}],"mask-image-conic-from-color":[{"mask-conic-from":X()}],"mask-image-conic-to-color":[{"mask-conic-to":X()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Se()}],"mask-repeat":[{mask:E()}],"mask-size":[{mask:q()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ae,le]}],filter:[{filter:["","none",ae,le]}],blur:[{blur:He()}],brightness:[{brightness:[ze,ae,le]}],contrast:[{contrast:[ze,ae,le]}],"drop-shadow":[{"drop-shadow":["","none",C,Ri,Ei]}],"drop-shadow-color":[{"drop-shadow":X()}],grayscale:[{grayscale:["",ze,ae,le]}],"hue-rotate":[{"hue-rotate":[ze,ae,le]}],invert:[{invert:["",ze,ae,le]}],saturate:[{saturate:[ze,ae,le]}],sepia:[{sepia:["",ze,ae,le]}],"backdrop-filter":[{"backdrop-filter":["","none",ae,le]}],"backdrop-blur":[{"backdrop-blur":He()}],"backdrop-brightness":[{"backdrop-brightness":[ze,ae,le]}],"backdrop-contrast":[{"backdrop-contrast":[ze,ae,le]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ze,ae,le]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ze,ae,le]}],"backdrop-invert":[{"backdrop-invert":["",ze,ae,le]}],"backdrop-opacity":[{"backdrop-opacity":[ze,ae,le]}],"backdrop-saturate":[{"backdrop-saturate":[ze,ae,le]}],"backdrop-sepia":[{"backdrop-sepia":["",ze,ae,le]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":F()}],"border-spacing-x":[{"border-spacing-x":F()}],"border-spacing-y":[{"border-spacing-y":F()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ae,le]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ze,"initial",ae,le]}],ease:[{ease:["linear","initial",B,ae,le]}],delay:[{delay:[ze,ae,le]}],animate:[{animate:["none",ne,ae,le]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[j,ae,le]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:it()}],"rotate-x":[{"rotate-x":it()}],"rotate-y":[{"rotate-y":it()}],"rotate-z":[{"rotate-z":it()}],scale:[{scale:Pe()}],"scale-x":[{"scale-x":Pe()}],"scale-y":[{"scale-y":Pe()}],"scale-z":[{"scale-z":Pe()}],"scale-3d":["scale-3d"],skew:[{skew:Mt()}],"skew-x":[{"skew-x":Mt()}],"skew-y":[{"skew-y":Mt()}],transform:[{transform:[ae,le,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ul()}],"translate-x":[{"translate-x":ul()}],"translate-y":[{"translate-y":ul()}],"translate-z":[{"translate-z":ul()}],"translate-none":["translate-none"],accent:[{accent:X()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:X()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ae,le]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":F()}],"scroll-mx":[{"scroll-mx":F()}],"scroll-my":[{"scroll-my":F()}],"scroll-ms":[{"scroll-ms":F()}],"scroll-me":[{"scroll-me":F()}],"scroll-mt":[{"scroll-mt":F()}],"scroll-mr":[{"scroll-mr":F()}],"scroll-mb":[{"scroll-mb":F()}],"scroll-ml":[{"scroll-ml":F()}],"scroll-p":[{"scroll-p":F()}],"scroll-px":[{"scroll-px":F()}],"scroll-py":[{"scroll-py":F()}],"scroll-ps":[{"scroll-ps":F()}],"scroll-pe":[{"scroll-pe":F()}],"scroll-pt":[{"scroll-pt":F()}],"scroll-pr":[{"scroll-pr":F()}],"scroll-pb":[{"scroll-pb":F()}],"scroll-pl":[{"scroll-pl":F()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ae,le]}],fill:[{fill:["none",...X()]}],"stroke-w":[{stroke:[ze,$u,$a,sf]}],stroke:[{stroke:["none",...X()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},LS=e1(b1);var S1=Object.defineProperty,E1=(n,r,o)=>r in n?S1(n,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[r]=o,df=(n,r,o)=>(E1(n,typeof r!="symbol"?r+"":r,o),o);let R1=class{constructor(){df(this,"current",this.detect()),df(this,"handoffState","pending"),df(this,"currentId",0)}set(r){this.current!==r&&(this.handoffState="pending",this.currentId=0,this.current=r)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},Di=new R1;function T1(n){typeof queueMicrotask=="function"?queueMicrotask(n):Promise.resolve().then(n).catch(r=>setTimeout(()=>{throw r}))}function Yi(){let n=[],r={addEventListener(o,c,f,d){return o.addEventListener(c,f,d),r.add(()=>o.removeEventListener(c,f,d))},requestAnimationFrame(...o){let c=requestAnimationFrame(...o);return r.add(()=>cancelAnimationFrame(c))},nextFrame(...o){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...o))},setTimeout(...o){let c=setTimeout(...o);return r.add(()=>clearTimeout(c))},microTask(...o){let c={current:!0};return T1(()=>{c.current&&o[0]()}),r.add(()=>{c.current=!1})},style(o,c,f){let d=o.style.getPropertyValue(c);return Object.assign(o.style,{[c]:f}),this.add(()=>{Object.assign(o.style,{[c]:d})})},group(o){let c=Yi();return o(c),this.add(()=>c.dispose())},add(o){return n.includes(o)||n.push(o),()=>{let c=n.indexOf(o);if(c>=0)for(let f of n.splice(c,1))f()}},dispose(){for(let o of n.splice(0))o()}};return r}function $y(){let[n]=S.useState(Yi);return S.useEffect(()=>()=>n.dispose(),[n]),n}let Ra=(n,r)=>{Di.isServer?S.useEffect(n,r):S.useLayoutEffect(n,r)};function Fy(n){let r=S.useRef(n);return Ra(()=>{r.current=n},[n]),r}let Xl=function(n){let r=Fy(n);return pt.useCallback((...o)=>r.current(...o),[r])};function Tf(...n){return Array.from(new Set(n.flatMap(r=>typeof r=="string"?r.split(" "):[]))).filter(Boolean).join(" ")}function qi(n,r,...o){if(n in r){let f=r[n];return typeof f=="function"?f(...o):f}let c=new Error(`Tried to handle "${n}" but there is no handler defined. Only defined handlers are: ${Object.keys(r).map(f=>`"${f}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(c,qi),c}var Wy=(n=>(n[n.None=0]="None",n[n.RenderStrategy=1]="RenderStrategy",n[n.Static=2]="Static",n))(Wy||{}),Sa=(n=>(n[n.Unmount=0]="Unmount",n[n.Hidden=1]="Hidden",n))(Sa||{});function Py(){let n=w1();return S.useCallback(r=>x1({mergeRefs:n,...r}),[n])}function x1({ourProps:n,theirProps:r,slot:o,defaultTag:c,features:f,visible:d=!0,name:y,mergeRefs:g}){g=g??M1;let h=Iy(r,n);if(d)return Ti(h,o,c,y,g);let v=f??0;if(v&2){let{static:p=!1,...A}=h;if(p)return Ti(A,o,c,y,g)}if(v&1){let{unmount:p=!0,...A}=h;return qi(p?0:1,{0(){return null},1(){return Ti({...A,hidden:!0,style:{display:"none"}},o,c,y,g)}})}return Ti(h,o,c,y,g)}function Ti(n,r={},o,c,f){let{as:d=o,children:y,refName:g="ref",...h}=hf(n,["unmount","static"]),v=n.ref!==void 0?{[g]:n.ref}:{},p=typeof y=="function"?y(r):y;"className"in h&&h.className&&typeof h.className=="function"&&(h.className=h.className(r)),h["aria-labelledby"]&&h["aria-labelledby"]===h.id&&(h["aria-labelledby"]=void 0);let A={};if(r){let x=!1,C=[];for(let[w,j]of Object.entries(r))typeof j=="boolean"&&(x=!0),j===!0&&C.push(w.replace(/([A-Z])/g,k=>`-${k.toLowerCase()}`));if(x){A["data-headlessui-state"]=C.join(" ");for(let w of C)A[`data-${w}`]=""}}if(d===S.Fragment&&(Object.keys(Fa(h)).length>0||Object.keys(Fa(A)).length>0))if(!S.isValidElement(p)||Array.isArray(p)&&p.length>1){if(Object.keys(Fa(h)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${c} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Fa(h)).concat(Object.keys(Fa(A))).map(x=>`  - ${x}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(x=>`  - ${x}`).join(`
`)].join(`
`))}else{let x=p.props,C=x?.className,w=typeof C=="function"?(...B)=>Tf(C(...B),h.className):Tf(C,h.className),j=w?{className:w}:{},k=Iy(p.props,Fa(hf(h,["ref"])));for(let B in A)B in k&&delete A[B];return S.cloneElement(p,Object.assign({},k,A,v,{ref:f(A1(p),v.ref)},j))}return S.createElement(d,Object.assign({},hf(h,["ref"]),d!==S.Fragment&&v,d!==S.Fragment&&A),p)}function w1(){let n=S.useRef([]),r=S.useCallback(o=>{for(let c of n.current)c!=null&&(typeof c=="function"?c(o):c.current=o)},[]);return(...o)=>{if(!o.every(c=>c==null))return n.current=o,r}}function M1(...n){return n.every(r=>r==null)?void 0:r=>{for(let o of n)o!=null&&(typeof o=="function"?o(r):o.current=r)}}function Iy(...n){if(n.length===0)return{};if(n.length===1)return n[0];let r={},o={};for(let c of n)for(let f in c)f.startsWith("on")&&typeof c[f]=="function"?(o[f]!=null||(o[f]=[]),o[f].push(c[f])):r[f]=c[f];if(r.disabled||r["aria-disabled"])for(let c in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(c)&&(o[c]=[f=>{var d;return(d=f?.preventDefault)==null?void 0:d.call(f)}]);for(let c in o)Object.assign(r,{[c](f,...d){let y=o[c];for(let g of y){if((f instanceof Event||f?.nativeEvent instanceof Event)&&f.defaultPrevented)return;g(f,...d)}}});return r}function jf(n){var r;return Object.assign(S.forwardRef(n),{displayName:(r=n.displayName)!=null?r:n.name})}function Fa(n){let r=Object.assign({},n);for(let o in r)r[o]===void 0&&delete r[o];return r}function hf(n,r=[]){let o=Object.assign({},n);for(let c of r)c in o&&delete o[c];return o}function A1(n){return pt.version.split(".")[0]>="19"?n.props.ref:n.ref}let z1=Symbol();function ev(...n){let r=S.useRef(n);S.useEffect(()=>{r.current=n},[n]);let o=Xl(c=>{for(let f of r.current)f!=null&&(typeof f=="function"?f(c):f.current=c)});return n.every(c=>c==null||c?.[z1])?void 0:o}function O1(n=0){let[r,o]=S.useState(n),c=S.useCallback(h=>o(h),[r]),f=S.useCallback(h=>o(v=>v|h),[r]),d=S.useCallback(h=>(r&h)===h,[r]),y=S.useCallback(h=>o(v=>v&~h),[o]),g=S.useCallback(h=>o(v=>v^h),[o]);return{flags:r,setFlag:c,addFlag:f,hasFlag:d,removeFlag:y,toggleFlag:g}}var D1={},uy,ry;typeof process<"u"&&typeof globalThis<"u"&&typeof Element<"u"&&((uy=process==null?void 0:D1)==null?void 0:uy.NODE_ENV)==="test"&&typeof((ry=Element?.prototype)==null?void 0:ry.getAnimations)>"u"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var _1=(n=>(n[n.None=0]="None",n[n.Closed=1]="Closed",n[n.Enter=2]="Enter",n[n.Leave=4]="Leave",n))(_1||{});function C1(n){let r={};for(let o in n)n[o]===!0&&(r[`data-${o}`]="");return r}function U1(n,r,o,c){let[f,d]=S.useState(o),{hasFlag:y,addFlag:g,removeFlag:h}=O1(n&&f?3:0),v=S.useRef(!1),p=S.useRef(!1),A=$y();return Ra(()=>{var x;if(n){if(o&&d(!0),!r){o&&g(3);return}return(x=c?.start)==null||x.call(c,o),N1(r,{inFlight:v,prepare(){p.current?p.current=!1:p.current=v.current,v.current=!0,!p.current&&(o?(g(3),h(4)):(g(4),h(2)))},run(){p.current?o?(h(3),g(4)):(h(4),g(3)):o?h(1):g(1)},done(){var C;p.current&&typeof r.getAnimations=="function"&&r.getAnimations().length>0||(v.current=!1,h(7),o||d(!1),(C=c?.end)==null||C.call(c,o))}})}},[n,o,r,A]),n?[f,{closed:y(1),enter:y(2),leave:y(4),transition:y(2)||y(4)}]:[o,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function N1(n,{prepare:r,run:o,done:c,inFlight:f}){let d=Yi();return H1(n,{prepare:r,inFlight:f}),d.nextFrame(()=>{o(),d.requestAnimationFrame(()=>{d.add(L1(n,c))})}),d.dispose}function L1(n,r){var o,c;let f=Yi();if(!n)return f.dispose;let d=!1;f.add(()=>{d=!0});let y=(c=(o=n.getAnimations)==null?void 0:o.call(n).filter(g=>g instanceof CSSTransition))!=null?c:[];return y.length===0?(r(),f.dispose):(Promise.allSettled(y.map(g=>g.finished)).then(()=>{d||r()}),f.dispose)}function H1(n,{inFlight:r,prepare:o}){if(r!=null&&r.current){o();return}let c=n.style.transition;n.style.transition="none",o(),n.offsetHeight,n.style.transition=c}let Yf=S.createContext(null);Yf.displayName="OpenClosedContext";var Ia=(n=>(n[n.Open=1]="Open",n[n.Closed=2]="Closed",n[n.Closing=4]="Closing",n[n.Opening=8]="Opening",n))(Ia||{});function tv(){return S.useContext(Yf)}function B1({value:n,children:r}){return pt.createElement(Yf.Provider,{value:n},r)}function j1(){let n=typeof document>"u";return"useSyncExternalStore"in wm?(r=>r.useSyncExternalStore)(wm)(()=>()=>{},()=>!1,()=>!n):!1}function lv(){let n=j1(),[r,o]=S.useState(Di.isHandoffComplete);return r&&Di.isHandoffComplete===!1&&o(!1),S.useEffect(()=>{r!==!0&&o(!0)},[r]),S.useEffect(()=>Di.handoff(),[]),n?!1:r}function Y1(){let n=S.useRef(!1);return Ra(()=>(n.current=!0,()=>{n.current=!1}),[]),n}function av(n){var r;return!!(n.enter||n.enterFrom||n.enterTo||n.leave||n.leaveFrom||n.leaveTo)||((r=n.as)!=null?r:uv)!==S.Fragment||pt.Children.count(n.children)===1}let Gi=S.createContext(null);Gi.displayName="TransitionContext";var q1=(n=>(n.Visible="visible",n.Hidden="hidden",n))(q1||{});function G1(){let n=S.useContext(Gi);if(n===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return n}function X1(){let n=S.useContext(Xi);if(n===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return n}let Xi=S.createContext(null);Xi.displayName="NestingContext";function ki(n){return"children"in n?ki(n.children):n.current.filter(({el:r})=>r.current!==null).filter(({state:r})=>r==="visible").length>0}function nv(n,r){let o=Fy(n),c=S.useRef([]),f=Y1(),d=$y(),y=Xl((C,w=Sa.Hidden)=>{let j=c.current.findIndex(({el:k})=>k===C);j!==-1&&(qi(w,{[Sa.Unmount](){c.current.splice(j,1)},[Sa.Hidden](){c.current[j].state="hidden"}}),d.microTask(()=>{var k;!ki(c)&&f.current&&((k=o.current)==null||k.call(o))}))}),g=Xl(C=>{let w=c.current.find(({el:j})=>j===C);return w?w.state!=="visible"&&(w.state="visible"):c.current.push({el:C,state:"visible"}),()=>y(C,Sa.Unmount)}),h=S.useRef([]),v=S.useRef(Promise.resolve()),p=S.useRef({enter:[],leave:[]}),A=Xl((C,w,j)=>{h.current.splice(0),r&&(r.chains.current[w]=r.chains.current[w].filter(([k])=>k!==C)),r?.chains.current[w].push([C,new Promise(k=>{h.current.push(k)})]),r?.chains.current[w].push([C,new Promise(k=>{Promise.all(p.current[w].map(([B,ne])=>ne)).then(()=>k())})]),w==="enter"?v.current=v.current.then(()=>r?.wait.current).then(()=>j(w)):j(w)}),x=Xl((C,w,j)=>{Promise.all(p.current[w].splice(0).map(([k,B])=>B)).then(()=>{var k;(k=h.current.shift())==null||k()}).then(()=>j(w))});return S.useMemo(()=>({children:c,register:g,unregister:y,onStart:A,onStop:x,wait:v,chains:p}),[g,y,c,A,x,p,v])}let uv=S.Fragment,rv=Wy.RenderStrategy;function k1(n,r){var o,c;let{transition:f=!0,beforeEnter:d,afterEnter:y,beforeLeave:g,afterLeave:h,enter:v,enterFrom:p,enterTo:A,entered:x,leave:C,leaveFrom:w,leaveTo:j,...k}=n,[B,ne]=S.useState(null),K=S.useRef(null),ue=av(n),P=ev(...ue?[K,r,ne]:r===null?[]:[r]),U=(o=k.unmount)==null||o?Sa.Unmount:Sa.Hidden,{show:oe,appear:F,initial:ce}=G1(),[Me,Qe]=S.useState(oe?"visible":"hidden"),Ye=X1(),{register:Te,unregister:xe}=Ye;Ra(()=>Te(K),[Te,K]),Ra(()=>{if(U===Sa.Hidden&&K.current){if(oe&&Me!=="visible"){Qe("visible");return}return qi(Me,{hidden:()=>xe(K),visible:()=>Te(K)})}},[Me,K,Te,xe,oe,U]);let we=lv();Ra(()=>{if(ue&&we&&Me==="visible"&&K.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[K,Me,we,ue]);let D=ce&&!F,J=F&&oe&&ce,X=S.useRef(!1),Se=nv(()=>{X.current||(Qe("hidden"),xe(K))},Ye),E=Xl(Oe=>{X.current=!0;let He=Oe?"enter":"leave";Se.onStart(K,He,it=>{it==="enter"?d?.():it==="leave"&&g?.()})}),q=Xl(Oe=>{let He=Oe?"enter":"leave";X.current=!1,Se.onStop(K,He,it=>{it==="enter"?y?.():it==="leave"&&h?.()}),He==="leave"&&!ki(Se)&&(Qe("hidden"),xe(K))});S.useEffect(()=>{ue&&f||(E(oe),q(oe))},[oe,ue,f]);let W=!(!f||!ue||!we||D),[,V]=U1(W,B,oe,{start:E,end:q}),I=Fa({ref:P,className:((c=Tf(k.className,J&&v,J&&p,V.enter&&v,V.enter&&V.closed&&p,V.enter&&!V.closed&&A,V.leave&&C,V.leave&&!V.closed&&w,V.leave&&V.closed&&j,!V.transition&&oe&&x))==null?void 0:c.trim())||void 0,...C1(V)}),ve=0;Me==="visible"&&(ve|=Ia.Open),Me==="hidden"&&(ve|=Ia.Closed),oe&&Me==="hidden"&&(ve|=Ia.Opening),!oe&&Me==="visible"&&(ve|=Ia.Closing);let se=Py();return pt.createElement(Xi.Provider,{value:Se},pt.createElement(B1,{value:ve},se({ourProps:I,theirProps:k,defaultTag:uv,features:rv,visible:Me==="visible",name:"Transition.Child"})))}function V1(n,r){let{show:o,appear:c=!1,unmount:f=!0,...d}=n,y=S.useRef(null),g=av(n),h=ev(...g?[y,r]:r===null?[]:[r]);lv();let v=tv();if(o===void 0&&v!==null&&(o=(v&Ia.Open)===Ia.Open),o===void 0)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[p,A]=S.useState(o?"visible":"hidden"),x=nv(()=>{o||A("hidden")}),[C,w]=S.useState(!0),j=S.useRef([o]);Ra(()=>{C!==!1&&j.current[j.current.length-1]!==o&&(j.current.push(o),w(!1))},[j,o]);let k=S.useMemo(()=>({show:o,appear:c,initial:C}),[o,c,C]);Ra(()=>{o?A("visible"):!ki(x)&&y.current!==null&&A("hidden")},[o,x]);let B={unmount:f},ne=Xl(()=>{var P;C&&w(!1),(P=n.beforeEnter)==null||P.call(n)}),K=Xl(()=>{var P;C&&w(!1),(P=n.beforeLeave)==null||P.call(n)}),ue=Py();return pt.createElement(Xi.Provider,{value:x},pt.createElement(Gi.Provider,{value:k},ue({ourProps:{...B,as:S.Fragment,children:pt.createElement(iv,{ref:h,...B,...d,beforeEnter:ne,beforeLeave:K})},theirProps:{},defaultTag:S.Fragment,features:rv,visible:p==="visible",name:"Transition"})))}function Q1(n,r){let o=S.useContext(Gi)!==null,c=tv()!==null;return pt.createElement(pt.Fragment,null,!o&&c?pt.createElement(xf,{ref:r,...n}):pt.createElement(iv,{ref:r,...n}))}let xf=jf(V1),iv=jf(k1),Z1=jf(Q1),BS=Object.assign(xf,{Child:Z1,Root:xf});function K1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const jS=S.forwardRef(K1);function J1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}const YS=S.forwardRef(J1);function $1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))}const qS=S.forwardRef($1);function F1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const GS=S.forwardRef(F1);function W1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const XS=S.forwardRef(W1);function P1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const kS=S.forwardRef(P1);function I1({title:n,titleId:r,...o},c){return S.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:c,"aria-labelledby":r},o),n?S.createElement("title",{id:r},n):null,S.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const VS=S.forwardRef(I1);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ar(n){return n+.5|0}const Ea=(n,r,o)=>Math.max(Math.min(n,o),r);function Pu(n){return Ea(ar(n*2.55),0,255)}function Ta(n){return Ea(ar(n*255),0,255)}function Gl(n){return Ea(ar(n/2.55)/100,0,1)}function iy(n){return Ea(ar(n*100),0,100)}const ll={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},wf=[..."0123456789ABCDEF"],eS=n=>wf[n&15],tS=n=>wf[(n&240)>>4]+wf[n&15],xi=n=>(n&240)>>4===(n&15),lS=n=>xi(n.r)&&xi(n.g)&&xi(n.b)&&xi(n.a);function aS(n){var r=n.length,o;return n[0]==="#"&&(r===4||r===5?o={r:255&ll[n[1]]*17,g:255&ll[n[2]]*17,b:255&ll[n[3]]*17,a:r===5?ll[n[4]]*17:255}:(r===7||r===9)&&(o={r:ll[n[1]]<<4|ll[n[2]],g:ll[n[3]]<<4|ll[n[4]],b:ll[n[5]]<<4|ll[n[6]],a:r===9?ll[n[7]]<<4|ll[n[8]]:255})),o}const nS=(n,r)=>n<255?r(n):"";function uS(n){var r=lS(n)?eS:tS;return n?"#"+r(n.r)+r(n.g)+r(n.b)+nS(n.a,r):void 0}const rS=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ov(n,r,o){const c=r*Math.min(o,1-o),f=(d,y=(d+n/30)%12)=>o-c*Math.max(Math.min(y-3,9-y,1),-1);return[f(0),f(8),f(4)]}function iS(n,r,o){const c=(f,d=(f+n/60)%6)=>o-o*r*Math.max(Math.min(d,4-d,1),0);return[c(5),c(3),c(1)]}function oS(n,r,o){const c=ov(n,1,.5);let f;for(r+o>1&&(f=1/(r+o),r*=f,o*=f),f=0;f<3;f++)c[f]*=1-r-o,c[f]+=r;return c}function cS(n,r,o,c,f){return n===f?(r-o)/c+(r<o?6:0):r===f?(o-n)/c+2:(n-r)/c+4}function qf(n){const o=n.r/255,c=n.g/255,f=n.b/255,d=Math.max(o,c,f),y=Math.min(o,c,f),g=(d+y)/2;let h,v,p;return d!==y&&(p=d-y,v=g>.5?p/(2-d-y):p/(d+y),h=cS(o,c,f,p,d),h=h*60+.5),[h|0,v||0,g]}function Gf(n,r,o,c){return(Array.isArray(r)?n(r[0],r[1],r[2]):n(r,o,c)).map(Ta)}function Xf(n,r,o){return Gf(ov,n,r,o)}function fS(n,r,o){return Gf(oS,n,r,o)}function sS(n,r,o){return Gf(iS,n,r,o)}function cv(n){return(n%360+360)%360}function dS(n){const r=rS.exec(n);let o=255,c;if(!r)return;r[5]!==c&&(o=r[6]?Pu(+r[5]):Ta(+r[5]));const f=cv(+r[2]),d=+r[3]/100,y=+r[4]/100;return r[1]==="hwb"?c=fS(f,d,y):r[1]==="hsv"?c=sS(f,d,y):c=Xf(f,d,y),{r:c[0],g:c[1],b:c[2],a:o}}function hS(n,r){var o=qf(n);o[0]=cv(o[0]+r),o=Xf(o),n.r=o[0],n.g=o[1],n.b=o[2]}function mS(n){if(!n)return;const r=qf(n),o=r[0],c=iy(r[1]),f=iy(r[2]);return n.a<255?`hsla(${o}, ${c}%, ${f}%, ${Gl(n.a)})`:`hsl(${o}, ${c}%, ${f}%)`}const oy={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},cy={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function yS(){const n={},r=Object.keys(cy),o=Object.keys(oy);let c,f,d,y,g;for(c=0;c<r.length;c++){for(y=g=r[c],f=0;f<o.length;f++)d=o[f],g=g.replace(d,oy[d]);d=parseInt(cy[y],16),n[g]=[d>>16&255,d>>8&255,d&255]}return n}let wi;function vS(n){wi||(wi=yS(),wi.transparent=[0,0,0,0]);const r=wi[n.toLowerCase()];return r&&{r:r[0],g:r[1],b:r[2],a:r.length===4?r[3]:255}}const gS=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function pS(n){const r=gS.exec(n);let o=255,c,f,d;if(r){if(r[7]!==c){const y=+r[7];o=r[8]?Pu(y):Ea(y*255,0,255)}return c=+r[1],f=+r[3],d=+r[5],c=255&(r[2]?Pu(c):Ea(c,0,255)),f=255&(r[4]?Pu(f):Ea(f,0,255)),d=255&(r[6]?Pu(d):Ea(d,0,255)),{r:c,g:f,b:d,a:o}}}function bS(n){return n&&(n.a<255?`rgba(${n.r}, ${n.g}, ${n.b}, ${Gl(n.a)})`:`rgb(${n.r}, ${n.g}, ${n.b})`)}const mf=n=>n<=.0031308?n*12.92:Math.pow(n,1/2.4)*1.055-.055,Yn=n=>n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4);function SS(n,r,o){const c=Yn(Gl(n.r)),f=Yn(Gl(n.g)),d=Yn(Gl(n.b));return{r:Ta(mf(c+o*(Yn(Gl(r.r))-c))),g:Ta(mf(f+o*(Yn(Gl(r.g))-f))),b:Ta(mf(d+o*(Yn(Gl(r.b))-d))),a:n.a+o*(r.a-n.a)}}function Mi(n,r,o){if(n){let c=qf(n);c[r]=Math.max(0,Math.min(c[r]+c[r]*o,r===0?360:1)),c=Xf(c),n.r=c[0],n.g=c[1],n.b=c[2]}}function fv(n,r){return n&&Object.assign(r||{},n)}function fy(n){var r={r:0,g:0,b:0,a:255};return Array.isArray(n)?n.length>=3&&(r={r:n[0],g:n[1],b:n[2],a:255},n.length>3&&(r.a=Ta(n[3]))):(r=fv(n,{r:0,g:0,b:0,a:1}),r.a=Ta(r.a)),r}function ES(n){return n.charAt(0)==="r"?pS(n):dS(n)}class Mf{constructor(r){if(r instanceof Mf)return r;const o=typeof r;let c;o==="object"?c=fy(r):o==="string"&&(c=aS(r)||vS(r)||ES(r)),this._rgb=c,this._valid=!!c}get valid(){return this._valid}get rgb(){var r=fv(this._rgb);return r&&(r.a=Gl(r.a)),r}set rgb(r){this._rgb=fy(r)}rgbString(){return this._valid?bS(this._rgb):void 0}hexString(){return this._valid?uS(this._rgb):void 0}hslString(){return this._valid?mS(this._rgb):void 0}mix(r,o){if(r){const c=this.rgb,f=r.rgb;let d;const y=o===d?.5:o,g=2*y-1,h=c.a-f.a,v=((g*h===-1?g:(g+h)/(1+g*h))+1)/2;d=1-v,c.r=255&v*c.r+d*f.r+.5,c.g=255&v*c.g+d*f.g+.5,c.b=255&v*c.b+d*f.b+.5,c.a=y*c.a+(1-y)*f.a,this.rgb=c}return this}interpolate(r,o){return r&&(this._rgb=SS(this._rgb,r._rgb,o)),this}clone(){return new Mf(this.rgb)}alpha(r){return this._rgb.a=Ta(r),this}clearer(r){const o=this._rgb;return o.a*=1-r,this}greyscale(){const r=this._rgb,o=ar(r.r*.3+r.g*.59+r.b*.11);return r.r=r.g=r.b=o,this}opaquer(r){const o=this._rgb;return o.a*=1+r,this}negate(){const r=this._rgb;return r.r=255-r.r,r.g=255-r.g,r.b=255-r.b,this}lighten(r){return Mi(this._rgb,2,r),this}darken(r){return Mi(this._rgb,2,-r),this}saturate(r){return Mi(this._rgb,1,r),this}desaturate(r){return Mi(this._rgb,1,-r),this}rotate(r){return hS(this._rgb,r),this}}const sv="label";function sy(n,r){typeof n=="function"?n(r):n&&(n.current=r)}function RS(n,r){const o=n.options;o&&r&&Object.assign(o,r)}function dv(n,r){n.labels=r}function hv(n,r){let o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:sv;const c=[];n.datasets=r.map(f=>{const d=n.datasets.find(y=>y[o]===f[o]);return!d||!f.data||c.includes(d)?{...f}:(c.push(d),Object.assign(d,f),d)})}function TS(n){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:sv;const o={labels:[],datasets:[]};return dv(o,n.labels),hv(o,n.datasets,r),o}function xS(n,r){const{height:o=150,width:c=300,redraw:f=!1,datasetIdKey:d,type:y,data:g,options:h,plugins:v=[],fallbackContent:p,updateMode:A,...x}=n,C=S.useRef(null),w=S.useRef(null),j=()=>{C.current&&(w.current=new dy(C.current,{type:y,data:TS(g,d),options:h&&{...h},plugins:v}),sy(r,w.current))},k=()=>{sy(r,null),w.current&&(w.current.destroy(),w.current=null)};return S.useEffect(()=>{!f&&w.current&&h&&RS(w.current,h)},[f,h]),S.useEffect(()=>{!f&&w.current&&dv(w.current.config.data,g.labels)},[f,g.labels]),S.useEffect(()=>{!f&&w.current&&g.datasets&&hv(w.current.config.data,g.datasets,d)},[f,g.datasets]),S.useEffect(()=>{w.current&&(f?(k(),setTimeout(j)):w.current.update(A))},[f,h,g.labels,g.datasets,A]),S.useEffect(()=>{w.current&&(k(),setTimeout(j))},[y]),S.useEffect(()=>(j(),()=>k()),[]),pt.createElement("canvas",{ref:C,role:"img",height:o,width:c,...x},p)}const wS=S.forwardRef(xS);function kf(n,r){return dy.register(r),S.forwardRef((o,c)=>pt.createElement(wS,{...o,ref:c,type:n}))}const QS=kf("line",R0),ZS=kf("bar",S0),KS=kf("doughnut",E0);export{ZS as B,Mf as C,KS as D,XS as F,QS as L,DS as N,pt as R,GS as a,kS as b,NS as c,jS as d,VS as e,Cf as f,CS as g,YS as h,qS as i,zS as j,_S as k,US as l,OS as m,S as r,LS as t,kl as u,BS as z};
