import{C as vs}from"./vendor-eYbLVx36.js";/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function ot(){}const dn=(()=>{let i=0;return()=>i++})();function T(i){return i==null}function H(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function O(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function X(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function et(i,t){return X(i)?i:t}function P(i,t){return typeof i>"u"?t:i}const un=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,Ms=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function E(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function L(i,t,e,s){let n,o,r;if(H(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(O(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function fe(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ge(i){if(H(i))return i.map(ge);if(O(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ge(i[e[n]]);return t}return i}function Ss(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function fn(i,t,e,s){if(!Ss(i))return;const n=t[i],o=e[i];O(n)&&O(o)?Ut(n,o,s):t[i]=ge(o)}function Ut(i,t,e){const s=H(t)?t:[t],n=s.length;if(!O(i))return i;e=e||{};const o=e.merger||fn;let r;for(let a=0;a<n;++a){if(r=s[a],!O(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function Nt(i,t){return Ut(i,t,{merger:gn})}function gn(i,t,e){if(!Ss(i))return;const s=t[i],n=e[i];O(s)&&O(n)?Nt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ge(n))}const ri={"":i=>i,x:i=>i.x,y:i=>i.y};function pn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function mn(i){const t=pn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function Mt(i,t){return(ri[t]||(ri[t]=mn(t)))(i)}function Xe(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Xt=i=>typeof i<"u",ut=i=>typeof i=="function",ai=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function bn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const R=Math.PI,F=2*R,_n=F+R,pe=Number.POSITIVE_INFINITY,xn=R/180,V=R/2,pt=R/4,li=R*2/3,ks=Math.log10,nt=Math.sign;function jt(i,t,e){return Math.abs(i-t)<e}function ci(i){const t=Math.round(i);i=jt(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(ks(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function yn(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function vn(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function Kt(i){return!vn(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function Mn(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Sn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function lt(i){return i*(R/180)}function kn(i){return i*(180/R)}function hi(i){if(!X(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function ws(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*R&&(o+=F),{angle:o,distance:n}}function He(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function wn(i,t){return(i-t+_n)%F-R}function J(i){return(i%F+F)%F}function Gt(i,t,e,s){const n=J(i),o=J(t),r=J(e),a=J(o-n),l=J(r-n),c=J(n-o),h=J(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function Y(i,t,e){return Math.max(t,Math.min(e,i))}function Pn(i){return Y(i,-32768,32767)}function ct(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Ke(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const yt=(i,t,e,s)=>Ke(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Dn=(i,t,e)=>Ke(i,e,s=>i[s][t]>=e);function Cn(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ps=["push","pop","shift","splice","unshift"];function On(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ps.forEach(e=>{const s="_onData"+Xe(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function di(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ps.forEach(o=>{delete i[o]}),delete i._chartjs)}function Ds(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Cs=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Os(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Cs.call(window,()=>{s=!1,i.apply(t,e)}))}}function An(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const Ge=i=>i==="start"?"left":i==="end"?"right":"center",$=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,Tn=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function Ln(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:r,vScale:a,_parsed:l}=i,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=r.axis,{min:d,max:u,minDefined:f,maxDefined:p}=r.getUserBounds();if(f){if(n=Math.min(yt(l,h,d).lo,e?s:yt(t,h,r.getPixelForValue(d)).lo),c){const g=l.slice(0,n+1).reverse().findIndex(m=>!T(m[a.axis]));n-=Math.max(0,g)}n=Y(n,0,s-1)}if(p){let g=Math.max(yt(l,r.axis,u,!0).hi+1,e?0:yt(t,h,r.getPixelForValue(u),!0).hi+1);if(c){const m=l.slice(g-1).findIndex(b=>!T(b[a.axis]));g+=Math.max(0,m)}o=Y(g,n,s)-n}else o=s-n}return{start:n,count:o}}function Rn(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const te=i=>i===0||i===1,ui=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*F/e)),fi=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*F/e)+1,$t={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*V)+1,easeOutSine:i=>Math.sin(i*V),easeInOutSine:i=>-.5*(Math.cos(R*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>te(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>te(i)?i:ui(i,.075,.3),easeOutElastic:i=>te(i)?i:fi(i,.075,.3),easeInOutElastic(i){return te(i)?i:i<.5?.5*ui(i*2,.1125,.45):.5+.5*fi(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-$t.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?$t.easeInBounce(i*2)*.5:$t.easeOutBounce(i*2-1)*.5+.5};function qe(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function gi(i){return qe(i)?i:new vs(i)}function De(i){return qe(i)?i:new vs(i).saturate(.5).darken(.1).hexString()}const In=["x","y","borderWidth","radius","tension"],En=["color","borderColor","backgroundColor"];function zn(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:En},numbers:{type:"number",properties:In}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Fn(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const pi=new Map;function Bn(i,t){t=t||{};const e=i+JSON.stringify(t);let s=pi.get(e);return s||(s=new Intl.NumberFormat(i,t),pi.set(e,s)),s}function Je(i,t,e){return Bn(t,e).format(i)}const Wn={values(i){return H(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Hn(i,e)}const r=ks(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Je(i,s,l)}};function Hn(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var As={formatters:Wn};function Vn(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:As.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const St=Object.create(null),Ve=Object.create(null);function Yt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ce(i,t,e){return typeof t=="string"?Ut(Yt(i,t),e):Ut(Yt(i,""),t)}class Nn{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>De(n.backgroundColor),this.hoverBorderColor=(s,n)=>De(n.borderColor),this.hoverColor=(s,n)=>De(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ce(this,t,e)}get(t){return Yt(this,t)}describe(t,e){return Ce(Ve,t,e)}override(t,e){return Ce(St,t,e)}route(t,e,s,n){const o=Yt(this,t),r=Yt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return O(l)?Object.assign({},c,l):P(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var W=new Nn({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[zn,Fn,Vn]);function jn(i){return!i||T(i.size)||T(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function mi(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function mt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function bi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Ne(i,t,e,s){Ts(i,t,e,s,null)}function Ts(i,t,e,s,n){let o,r,a,l,c,h,d,u;const f=t.pointStyle,p=t.rotation,g=t.radius;let m=(p||0)*xn;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(g)||g<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,g,0,0,F):i.arc(e,s,g,0,F),i.closePath();break;case"triangle":h=n?n/2:g,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*g),m+=li,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*g),m+=li,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*g),i.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(m+pt)*l,d=Math.cos(m+pt)*(n?n/2-c:l),a=Math.sin(m+pt)*l,u=Math.sin(m+pt)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-R,m-V),i.arc(e+u,s-r,c,m-V,m),i.arc(e+d,s+a,c,m,m+V),i.arc(e-u,s+r,c,m+V,m+R),i.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=pt;case"rectRot":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+u,s-r),i.lineTo(e+d,s+a),i.lineTo(e-u,s+r),i.closePath();break;case"crossRot":m+=pt;case"cross":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r),m+=pt,d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+u,s-r),i.lineTo(e-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*g,a=Math.sin(m)*g,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:g),s+Math.sin(m)*g);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function qt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function ye(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function ve(i){i.restore()}function $n(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Yn(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Un(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),T(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Xn(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Kn(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Jt(i,t,e,s,n,o={}){const r=H(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,Un(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Kn(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),T(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Xn(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function me(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*R,R,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,R,V,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,V,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-V,!0),i.lineTo(e+r.topLeft,s)}const Gn=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,qn=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Jn(i,t){const e=(""+i).match(Gn);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Qn=i=>+i||0;function Qe(i,t){const e={},s=O(t),n=s?Object.keys(t):t,o=O(i)?s?r=>P(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=Qn(o(r));return e}function Ls(i){return Qe(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Ct(i){return Qe(i,["topLeft","topRight","bottomLeft","bottomRight"])}function Z(i){const t=Ls(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function U(i,t){i=i||{},t=t||W.font;let e=P(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=P(i.style,t.style);s&&!(""+s).match(qn)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:P(i.family,t.family),lineHeight:Jn(P(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:P(i.weight,t.weight),string:""};return n.string=jn(n),n}function ee(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function Zn(i,t,e){const{min:s,max:n}=i,o=Ms(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function kt(i,t){return Object.assign(Object.create(i),t)}function Ze(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=zs("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>Ze([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Is(a,l,()=>ao(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return xi(a).includes(l)},ownKeys(a){return xi(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function At(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Rs(i,s),setContext:o=>At(i,o,e,s),override:o=>At(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Is(o,r,()=>eo(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Rs(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ut(e)?e:()=>e,isIndexable:ut(s)?s:()=>s}}const to=(i,t)=>i?i+Xe(t):t,ti=(i,t)=>O(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Is(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function eo(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return ut(a)&&r.isScriptable(t)&&(a=io(t,a,i,e)),H(a)&&a.length&&(a=so(t,a,i,r.isIndexable)),ti(t,a)&&(a=At(a,n,o&&o[t],r)),a}function io(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),ti(i,l)&&(l=ei(n._scopes,n,i,l)),l}function so(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(O(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=ei(c,n,i,h);t.push(At(d,o,r&&r[i],a))}}return t}function Es(i,t,e){return ut(i)?i(t,e):i}const no=(i,t)=>i===!0?t:typeof i=="string"?Mt(t,i):void 0;function oo(i,t,e,s,n){for(const o of t){const r=no(e,o);if(r){i.add(r);const a=Es(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function ei(i,t,e,s){const n=t._rootScopes,o=Es(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=_i(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=_i(a,r,o,l,s),l===null)?!1:Ze(Array.from(a),[""],n,o,()=>ro(t,e,s))}function _i(i,t,e,s,n){for(;e;)e=oo(i,t,e,s,n);return e}function ro(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return H(n)&&O(e)?e:n||{}}function ao(i,t,e,s){let n;for(const o of t)if(n=zs(to(o,i),e),typeof n<"u")return ti(i,n)?ei(e,s,i,n):n}function zs(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function xi(i){let t=i._keys;return t||(t=i._keys=lo(i._scopes)),t}function lo(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const co=Number.EPSILON||1e-14,Tt=(i,t)=>t<i.length&&!i[t].skip&&i[t],Fs=i=>i==="x"?"y":"x";function ho(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=He(o,n),l=He(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function uo(i,t,e){const s=i.length;let n,o,r,a,l,c=Tt(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Tt(i,h+1),!(!l||!c)){if(jt(t[h],0,co)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function fo(i,t,e="x"){const s=Fs(e),n=i.length;let o,r,a,l=Tt(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Tt(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function go(i,t="x"){const e=Fs(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Tt(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Tt(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?nt(n[r-1])!==nt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}uo(i,n,o),fo(i,o,t)}function ie(i,t,e){return Math.max(Math.min(i,e),t)}function po(i,t){let e,s,n,o,r,a=qt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&qt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=ie(n.cp1x,t.left,t.right),n.cp1y=ie(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=ie(n.cp2x,t.left,t.right),n.cp2y=ie(n.cp2y,t.top,t.bottom)))}function mo(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")go(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=ho(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&po(i,e)}function ii(){return typeof window<"u"&&typeof document<"u"}function si(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function be(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Me=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function bo(i,t){return Me(i).getPropertyValue(t)}const _o=["top","right","bottom","left"];function vt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=_o[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const xo=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function yo(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(xo(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function _t(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Me(e),o=n.boxSizing==="border-box",r=vt(n,"padding"),a=vt(n,"border","width"),{x:l,y:c,box:h}=yo(i,e),d=r.left+(h&&a.left),u=r.top+(h&&a.top);let{width:f,height:p}=t;return o&&(f-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/p*e.height/s)}}function vo(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&si(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Me(o),l=vt(a,"border","width"),c=vt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=be(a.maxWidth,o,"clientWidth"),n=be(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||pe,maxHeight:n||pe}}const se=i=>Math.round(i*10)/10;function Mo(i,t,e,s){const n=Me(i),o=vt(n,"margin"),r=be(n.maxWidth,i,"clientWidth")||pe,a=be(n.maxHeight,i,"clientHeight")||pe,l=vo(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=vt(n,"border","width"),f=vt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=se(Math.min(c,r,l.maxWidth)),h=se(Math.min(h,a,l.maxHeight)),c&&!h&&(h=se(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=se(Math.floor(h*s))),{width:c,height:h}}function yi(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const So=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};ii()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function vi(i,t){const e=bo(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function xt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function ko(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function wo(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=xt(i,n,e),a=xt(n,o,e),l=xt(o,t,e),c=xt(r,a,e),h=xt(a,l,e);return xt(c,h,e)}const Po=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Do=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Ot(i,t,e){return i?Po(t,e):Do()}function Bs(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function Ws(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function Hs(i){return i==="angle"?{between:Gt,compare:wn,normalize:J}:{between:ct,compare:(t,e)=>t-e,normalize:t=>t}}function Mi({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Co(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=Hs(s),l=t.length;let{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&r(a(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function Vs(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=Hs(s),{start:h,end:d,loop:u,style:f}=Co(i,t,e),p=[];let g=!1,m=null,b,_,y;const v=()=>l(n,y,b)&&a(n,y)!==0,x=()=>a(o,b)===0||l(o,y,b),S=()=>g||v(),k=()=>!g||x();for(let M=h,w=h;M<=d;++M)_=t[M%r],!_.skip&&(b=c(_[s]),b!==y&&(g=l(b,n,o),m===null&&S()&&(m=a(b,n)===0?M:w),m!==null&&k()&&(p.push(Mi({start:m,end:M,loop:u,count:r,style:f})),m=null),w=M,y=b));return m!==null&&p.push(Mi({start:m,end:d,loop:u,count:r,style:f})),p}function Ns(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=Vs(s[n],i.points,t);o.length&&e.push(...o)}return e}function Oo(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Ao(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function To(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=Oo(e,n,o,s);if(s===!0)return Si(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return Si(i,Ao(e,r,l,c),e,t)}function Si(i,t,e,s){return!s||!s.setContext||!e?t:Lo(i,t,e,s)}function Lo(i,t,e,s){const n=i._chart.getContext(),o=ki(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,u=d;function f(p,g,m,b){const _=a?-1:1;if(p!==g){for(p+=l;e[p%l].skip;)p-=_;for(;e[g%l].skip;)g+=_;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:m,style:b}),h=b,d=g%l)}}for(const p of t){d=a?d:p.start;let g=e[d%l],m;for(u=d+1;u<=p.end;u++){const b=e[u%l];m=ki(s.setContext(kt(n,{type:"segment",p0:g,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),Ro(m,h)&&f(d,u-1,p.loop,h),g=b,h=m}d<u-1&&f(d,u-1,p.loop,h)}return c}function ki(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Ro(i,t){if(!t)return!1;const e=[],s=function(n,o){return qe(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function ne(i,t,e){return i.options.clip?i[e]:t[e]}function Io(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:ne(e,t,"left"),right:ne(e,t,"right"),top:ne(s,t,"top"),bottom:ne(s,t,"bottom")}:t}function js(i,t){const e=t._clip;if(e.disabled)return!1;const s=Io(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Eo{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Cs.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var rt=new Eo;const wi="transparent",zo={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=gi(i||wi),n=s.valid&&gi(t||wi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Fo{constructor(t,e,s,n){const o=e[s];n=ee([t.to,n,o,t.from]);const r=ee([t.from,o,n]);this._active=!0,this._fn=t.fn||zo[t.type||typeof r],this._easing=$t[t.easing]||$t.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ee([t.to,e,n,t.from]),this._from=ee([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class $s{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!O(t))return;const e=Object.keys(W.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!O(o))return;const r={};for(const a of e)r[a]=o[a];(H(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=Wo(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Bo(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,a);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new Fo(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return rt.add(this._chart,s),!0}}function Bo(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Wo(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Pi(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Ho(i,t,e){if(e===!1)return!1;const s=Pi(i,e),n=Pi(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Vo(i){let t,e,s,n;return O(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Ys(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Di(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],X(c)&&(o||t===0||nt(t)===nt(c))&&(t+=c)}return!h&&!s.all?0:t}function No(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function Oe(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function jo(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function $o(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Yo(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Ci(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Oi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=jo(o,r,s),d=t.length;let u;for(let f=0;f<d;++f){const p=t[f],{[l]:g,[c]:m}=p,b=p._stacks||(p._stacks={});u=b[c]=Yo(n,h,g),u[a]=m,u._top=Ci(u,r,!0,s.type),u._bottom=Ci(u,r,!1,s.type);const _=u._visualValues||(u._visualValues={});_[a]=m}}function Ae(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Uo(i,t){return kt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Xo(i,t,e){return kt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Et(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Te=i=>i==="reset"||i==="none",Ai=(i,t)=>t?i:Object.assign({},i),Ko=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Ys(e,!0),values:null};class Se{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Oe(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Et(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,p)=>d==="x"?u:d==="r"?p:f,o=e.xAxisID=P(s.xAxisID,Ae(t,"x")),r=e.yAxisID=P(s.yAxisID,Ae(t,"y")),a=e.rAxisID=P(s.rAxisID,Ae(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&di(this._data,this),t._stacked&&Et(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(O(e)){const n=this._cachedMeta;this._data=No(e,n)}else if(s!==e){if(s){di(s,this);const n=this._cachedMeta;Et(n),n._parsed=[]}e&&Object.isExtensible(e)&&On(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Oe(e.vScale,e),e.stack!==s.stack&&(n=!0,Et(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(Oi(this,e._parsed),e._stacked=Oe(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{H(n[t])?u=this.parseArrayData(s,n,t,e):O(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}r&&Oi(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let u,f,p;for(u=0,f=n;u<f;++u)p=u+s,d[u]={[a]:h||o.parse(c[p],p),[l]:r.parse(e[p],p)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(Mt(f,a),u),y:r.parse(Mt(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Ys(n,!0),values:e._stacks[t.axis]._visualValues};return Di(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Di(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Ko(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=$o(a);let u,f;function p(){f=n[u];const g=f[a.axis];return!X(f[t.axis])||h>g||d<g}for(u=0;u<r&&!(!p()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=r-1;u>=0;--u)if(!p()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],X(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Vo(P(this.options.clip,Ho(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Xo(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Uo(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Xt(s);if(a)return Ai(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(W.elements[t]),p=()=>this.getContext(s,n,e),g=c.resolveNamedOptions(u,f,p,d);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(Ai(g,l))),g}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new $s(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Te(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Te(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Te(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Et(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function Go(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Ds(s.sort((n,o)=>n-o))}return i._cache.$bar}function qo(i){const t=i.iScale,e=Go(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(Xt(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function Jo(i,t,e,s){const n=e.barThickness;let o,r;return T(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function Qo(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function Zo(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function Us(i,t,e,s){return H(i)?Zo(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Ti(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(Us(u,d,o,c));return l}function Le(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function tr(i,t,e){return i!==0?nt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function er(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function ir(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=er(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Li(h,r,a,l)]=!0,n=c)),o[Li(n,r,a,l)]=!0,i.borderSkipped=o}function Li(i,t,e,s){return s?(i=sr(i,t,e),i=Ri(i,e,t)):i=Ri(i,t,e),i}function sr(i,t,e){return i===t?e:i===e?t:i}function Ri(i,t,e){return i==="start"?t:i==="end"?e:i}function nr(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Sl extends Se{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,s,n){return Ti(t,e,s,n)}parseArrayData(t,e,s,n){return Ti(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let u,f,p,g;for(u=s,f=s+n;u<f;++u)g=e[u],p={},p[o.axis]=o.parse(Mt(g,c),u),d.push(Us(Mt(g,h),p,r,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=Le(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const p=this.getParsed(f),g=o||T(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(p._stacks||{})[a.axis],_={horizontal:c,base:g.base,enableBorderRadius:!b||Le(p._custom)||r===b._top||r===b._bottom,x:c?g.head:m.center,y:c?m.center:g.head,height:c?m.size:Math.abs(g.size),width:c?Math.abs(g.size):m.size};u&&(_.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const y=_.options||t[f].options;ir(_,y,b,r),nr(_,y,h.ratio),this.updateElement(t[f],f,_,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[s.axis],c=h=>{const d=h._parsed.find(f=>f[s.axis]===l),u=d&&d[h.vScale.axis];if(T(u)||isNaN(u))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(s=>t[s].axis===e).shift()}_getAxis(){const t={},e=this.getFirstScaleIdForIndexAxis();for(const s of this.chart.data.datasets)t[P(this.chart.options.indexAxis==="x"?s.xAxisID:s.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||qo(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=Le(c);let d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,p,g;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&nt(d)!==nt(c.barEnd)&&(u=0),u+=d);const m=!T(o)&&!h?o:u;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?p=e.getPixelForValue(u+f):p=b,g=p-b,Math.abs(g)<r){g=tr(g,e,a)*r,d===a&&(b-=g/2);const _=e.getPixelForDecimal(0),y=e.getPixelForDecimal(1),v=Math.min(_,y),x=Math.max(_,y);b=Math.max(Math.min(b,x),v),p=b+g,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(p)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const _=nt(g)*e.getLineWidthForValue(a)/2;b+=_,g-=_}return{size:g,base:b,head:p,center:p+g/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=P(n.maxBarThickness,1/0);let a,l;const c=this._getAxisCount();if(e.grouped){const h=o?this._getStackCount(t):e.stackCount,d=n.barThickness==="flex"?Qo(t,e,n,h*c):Jo(t,e,n,h*c),u=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,f=this._getAxis().indexOf(P(u,this.getFirstScaleIdForIndexAxis())),p=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0)+f;a=d.start+d.chunk*p+d.chunk/2,l=Math.min(r,d.chunk*d.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}function or(i,t,e){let s=1,n=1,o=0,r=0;if(t<F){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(y,v,x)=>Gt(y,a,l,!0)?1:Math.max(v,v*e,x,x*e),p=(y,v,x)=>Gt(y,a,l,!0)?-1:Math.min(v,v*e,x,x*e),g=f(0,c,d),m=f(V,h,u),b=p(R,c,d),_=p(R+V,h,u);s=(g-b)/2,n=(m-_)/2,o=-(g+b)/2,r=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class kl extends Se{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(O(s[t])){const{key:l="value"}=this._parsing;o=c=>+Mt(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return lt(this.options.rotation-90)}_getCircumference(){return lt(this.options.circumference)}_getRotationExtents(){let t=F,e=-F;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(un(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:p,offsetY:g}=or(d,h,l),m=(s.width-r)/u,b=(s.height-r)/f,_=Math.max(Math.min(m,b)/2,0),y=Ms(this.options.radius,_),v=Math.max(y*l,0),x=(y-v)/this._getVisibleDatasetWeightTotal();this.offsetX=p*y,this.offsetY=g*y,n.total=this.calculateTotal(),this.outerRadius=y-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/F)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,p=u?0:this.outerRadius,{sharedOptions:g,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),_;for(_=0;_<e;++_)b+=this._circumference(_,o);for(_=e;_<e+s;++_){const y=this._circumference(_,o),v=t[_],x={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+y,circumference:y,outerRadius:p,innerRadius:f};m&&(x.options=g||this.resolveDataElementOptions(_,v.active?"active":n)),b+=y,this.updateElement(v,_,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?F*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Je(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(P(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class wl extends Se{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,r=this.chart._animationsDisabled;let{start:a,count:l}=Ln(e,n,r);this._drawStart=a,this._drawCount=l,Rn(e)&&(a=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(n,a,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=r.axis,f=a.axis,{spanGaps:p,segment:g}=this.options,m=Kt(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",_=e+s,y=t.length;let v=e>0&&this.getParsed(e-1);for(let x=0;x<y;++x){const S=t[x],k=b?S:{};if(x<e||x>=_){k.skip=!0;continue}const M=this.getParsed(x),w=T(M[f]),C=k[u]=r.getPixelForValue(M[u],x),D=k[f]=o||w?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,M,l):M[f],x);k.skip=isNaN(C)||isNaN(D)||w,k.stop=x>0&&Math.abs(M[u]-v[u])>m,g&&(k.parsed=M,k.raw=c.data[x]),d&&(k.options=h||this.resolveDataElementOptions(x,S.active?"active":n)),b||this.updateElement(S,x,k,n),v=M}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}function bt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ni{static override(t){Object.assign(ni.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return bt()}parse(){return bt()}format(){return bt()}add(){return bt()}diff(){return bt()}startOf(){return bt()}endOf(){return bt()}}var rr={_date:ni};function ar(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?Dn:yt;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=c(o,t,e-d),f=c(o,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(g=>!T(g[d.axis]));h.lo-=Math.max(0,f);const p=u.slice(h.hi).findIndex(g=>!T(g[d.axis]));h.hi+=Math.max(0,p)}return h}}return{lo:0,hi:o.length-1}}function ke(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:u}=ar(o[a],t,r,n);for(let f=d;f<=u;++f){const p=h[f];p.skip||s(p,c,f)}}}function lr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Re(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||ke(i,e,t,function(a,l,c){!n&&!qt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function cr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=ws(r,{x:t.x,y:t.y});Gt(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return ke(i,e,t,o),n}function hr(i,t,e,s,n,o){let r=[];const a=lr(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const p=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(p))&&!f)return;const m=a(t,p);m<l?(r=[{element:h,datasetIndex:d,index:u}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:u})}return ke(i,e,t,c),r}function Ie(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?cr(i,t,e,n):hr(i,t,e,s,n,o)}function Ii(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return ke(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var dr={modes:{index(i,t,e,s){const n=_t(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?Re(i,n,o,s,r):Ie(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?Re(i,n,o,s,r):Ie(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Re(i,n,o,s,r)},nearest(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ie(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=_t(t,i);return Ii(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=_t(t,i);return Ii(i,n,"y",e.intersect,s)}}};const Xs=["left","top","right","bottom"];function zt(i,t){return i.filter(e=>e.pos===t)}function Ei(i,t){return i.filter(e=>Xs.indexOf(e.pos)===-1&&e.box.axis===t)}function Ft(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function ur(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function fr(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!Xs.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function gr(i,t){const e=fr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function pr(i){const t=ur(i),e=Ft(t.filter(c=>c.box.fullSize),!0),s=Ft(zt(t,"left"),!0),n=Ft(zt(t,"right")),o=Ft(zt(t,"top"),!0),r=Ft(zt(t,"bottom")),a=Ei(t,"x"),l=Ei(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:zt(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function zi(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Ks(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function mr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!O(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Ks(r,o.getPadding());const a=Math.max(0,t.outerWidth-zi(r,i,"left","right")),l=Math.max(0,t.outerHeight-zi(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function br(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function _r(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function Ht(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,_r(a.horizontal,t));const{same:d,other:u}=mr(t,e,a,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(a)}return c&&Ht(n,t,e,s)||h}function oe(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Fi(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,u=c.size||l.height;Xt(c.start)&&(r=c.start),l.fullSize?oe(l,n.left,r,e.outerWidth-n.right-n.left,u):oe(l,t.left+c.placed,r,d,u),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,u=c.size||l.width;Xt(c.start)&&(o=c.start),l.fullSize?oe(l,o,n.top,u,e.outerHeight-n.bottom-n.top):oe(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var Q={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=Z(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=pr(i.boxes),l=a.vertical,c=a.horizontal;L(i.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=l.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),u=Object.assign({},n);Ks(u,Z(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),p=gr(l.concat(c),d);Ht(a.fullSize,f,d,p),Ht(l,f,d,p),Ht(c,f,d,p)&&Ht(l,f,d,p),br(f),Fi(a.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,Fi(a.rightAndBottom,f,d,p),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},L(a.chartArea,g=>{const m=g.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Gs{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class xr extends Gs{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const de="$chartjs",yr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Bi=i=>i===null||i==="";function vr(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[de]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Bi(n)){const o=vi(i,"width");o!==void 0&&(i.width=o)}if(Bi(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=vi(i,"height");o!==void 0&&(i.height=o)}return i}const qs=So?{passive:!0}:!1;function Mr(i,t,e){i&&i.addEventListener(t,e,qs)}function Sr(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,qs)}function kr(i,t){const e=yr[i.type]||i.type,{x:s,y:n}=_t(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function _e(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function wr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||_e(a.addedNodes,s),r=r&&!_e(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Pr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||_e(a.removedNodes,s),r=r&&!_e(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Qt=new Map;let Wi=0;function Js(){const i=window.devicePixelRatio;i!==Wi&&(Wi=i,Qt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Dr(i,t){Qt.size||window.addEventListener("resize",Js),Qt.set(i,t)}function Cr(i){Qt.delete(i),Qt.size||window.removeEventListener("resize",Js)}function Or(i,t,e){const s=i.canvas,n=s&&si(s);if(!n)return;const o=Os((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),Dr(i,o),r}function Ee(i,t,e){e&&e.disconnect(),t==="resize"&&Cr(i)}function Ar(i,t,e){const s=i.canvas,n=Os(o=>{i.ctx!==null&&e(kr(o,i))},i);return Mr(s,t,n),n}class Tr extends Gs{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(vr(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[de])return!1;const s=e[de].initial;["height","width"].forEach(o=>{const r=s[o];T(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[de],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:wr,detach:Pr,resize:Or}[e]||Ar;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ee,detach:Ee,resize:Ee}[e]||Sr)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Mo(t,e,s,n)}isAttached(t){const e=t&&si(t);return!!(e&&e.isConnected)}}function Lr(i){return!ii()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?xr:Tr}class ht{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Kt(this.x)&&Kt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function Rr(i,t){const e=i.options.ticks,s=Ir(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?zr(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return Fr(t,c,o,r/n),c;const h=Er(o,t,n);if(r>0){let d,u;const f=r>1?Math.round((l-a)/(r-1)):null;for(re(t,c,h,T(f)?0:a-f,a),d=0,u=r-1;d<u;d++)re(t,c,h,o[d],o[d+1]);return re(t,c,h,l,T(f)?t.length:l+f),c}return re(t,c,h),c}function Ir(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Er(i,t,e){const s=Br(i),n=t.length/e;if(!s)return Math.max(n,1);const o=yn(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function zr(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Fr(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function re(i,t,e,s,n){const o=P(s,0),r=Math.min(P(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function Br(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Wr=i=>i==="left"?"right":i==="right"?"left":i,Hi=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Vi=(i,t)=>Math.min(t||i,i);function Ni(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Hr(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function Vr(i,t){L(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Bt(i){return i.drawTicks?i.tickLength:0}function ji(i,t){if(!i.display)return 0;const e=U(i.font,t),s=Z(i.padding);return(H(i.text)?i.text.length:1)*e.lineHeight+s.height}function Nr(i,t){return kt(i,{scale:t,type:"scale"})}function jr(i,t,e){return kt(i,{tick:e,index:t,type:"tick"})}function $r(i,t,e){let s=Ge(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Wr(s)),s}function Yr(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,p;const g=r-n,m=a-o;if(i.isHorizontal()){if(f=$(s,o,a),O(e)){const b=Object.keys(e)[0],_=e[b];p=h[b].getPixelForValue(_)+g-t}else e==="center"?p=(c.bottom+c.top)/2+g-t:p=Hi(i,e,t);u=a-o}else{if(O(e)){const b=Object.keys(e)[0],_=e[b];f=h[b].getPixelForValue(_)-m+t}else e==="center"?f=(c.left+c.right)/2-m+t:f=Hi(i,e,t);p=$(s,r,n),d=e==="left"?-V:V}return{titleX:f,titleY:p,maxWidth:u,rotation:d}}class Lt extends ht{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=et(t,Number.POSITIVE_INFINITY),e=et(e,Number.NEGATIVE_INFINITY),s=et(s,Number.POSITIVE_INFINITY),n=et(n,Number.NEGATIVE_INFINITY),{min:et(t,s),max:et(e,n),minDefined:X(t),maxDefined:X(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:et(e,et(s,e)),max:et(s,et(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){E(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Zn(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Ni(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Rr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){E(this.options.afterUpdate,[this])}beforeSetDimensions(){E(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){E(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),E(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){E(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=E(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){E(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){E(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Vi(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=Y(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:f/(s-1),d+6>a&&(a=f/(s-(t.offset?.5:1)),l=this.maxHeight-Bt(t.grid)-e.padding-ji(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),r=kn(Math.min(Math.asin(Y((h.highest.height+6)/a,-1,1)),Math.asin(Y(l/c,-1,1))-Math.asin(Y(u/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){E(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){E(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=ji(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Bt(o)+l):(t.height=this.maxHeight,t.width=Bt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,p=lt(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(a){const b=s.mirror?0:m*d.width+g*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:g*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,m,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){E(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)T(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Ni(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Vi(e,s));let c=0,h=0,d,u,f,p,g,m,b,_,y,v,x;for(d=0;d<e;d+=l){if(p=t[d].label,g=this._resolveTickFontOptions(d),n.font=m=g.string,b=o[m]=o[m]||{data:{},gc:[]},_=g.lineHeight,y=v=0,!T(p)&&!H(p))y=mi(n,b.data,b.gc,y,p),v=_;else if(H(p))for(u=0,f=p.length;u<f;++u)x=p[u],!T(x)&&!H(x)&&(y=mi(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),c=Math.max(y,c),h=Math.max(v,h)}Vr(o,e);const S=r.indexOf(c),k=a.indexOf(h),M=w=>({width:r[w]||0,height:a[w]||0});return{first:M(0),last:M(e-1),widest:M(S),highest:M(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Pn(this._alignToPixels?mt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=jr(this.getContext(),t,s))}return this.$context||(this.$context=Nr(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=lt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=Bt(o),f=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,b=function(z){return mt(s,z,g)};let _,y,v,x,S,k,M,w,C,D,A,N;if(r==="top")_=b(this.bottom),k=this.bottom-u,w=_-m,D=b(t.top)+m,N=t.bottom;else if(r==="bottom")_=b(this.top),D=t.top,N=b(t.bottom)-m,k=_+m,w=this.top+u;else if(r==="left")_=b(this.right),S=this.right-u,M=_-m,C=b(t.left)+m,A=t.right;else if(r==="right")_=b(this.left),C=t.left,A=b(t.right)-m,S=_+m,M=this.left+u;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(O(r)){const z=Object.keys(r)[0],B=r[z];_=b(this.chart.scales[z].getPixelForValue(B))}D=t.top,N=t.bottom,k=_+m,w=k+u}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(O(r)){const z=Object.keys(r)[0],B=r[z];_=b(this.chart.scales[z].getPixelForValue(B))}S=_-m,M=S-u,C=t.left,A=t.right}const q=P(n.ticks.maxTicksLimit,d),I=Math.max(1,Math.ceil(d/q));for(y=0;y<d;y+=I){const z=this.getContext(y),B=o.setContext(z),tt=a.setContext(z),j=B.lineWidth,wt=B.color,Zt=tt.dash||[],Pt=tt.dashOffset,Rt=B.tickWidth,ft=B.tickColor,It=B.tickBorderDash||[],gt=B.tickBorderDashOffset;v=Hr(this,y,l),v!==void 0&&(x=mt(s,v,j),c?S=M=C=A=x:k=w=D=N=x,f.push({tx1:S,ty1:k,tx2:M,ty2:w,x1:C,y1:D,x2:A,y2:N,width:j,color:wt,borderDash:Zt,borderDashOffset:Pt,tickWidth:Rt,tickColor:ft,tickBorderDash:It,tickBorderDashOffset:gt}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=Bt(s.grid),f=u+h,p=d?-h:f,g=-lt(this.labelRotation),m=[];let b,_,y,v,x,S,k,M,w,C,D,A,N="middle";if(n==="top")S=this.bottom-p,k=this._getXAxisLabelAlignment();else if(n==="bottom")S=this.top+p,k=this._getXAxisLabelAlignment();else if(n==="left"){const I=this._getYAxisLabelAlignment(u);k=I.textAlign,x=I.x}else if(n==="right"){const I=this._getYAxisLabelAlignment(u);k=I.textAlign,x=I.x}else if(e==="x"){if(n==="center")S=(t.top+t.bottom)/2+f;else if(O(n)){const I=Object.keys(n)[0],z=n[I];S=this.chart.scales[I].getPixelForValue(z)+f}k=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-f;else if(O(n)){const I=Object.keys(n)[0],z=n[I];x=this.chart.scales[I].getPixelForValue(z)}k=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?N="top":l==="end"&&(N="bottom"));const q=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const I=o.setContext(this.getContext(b));M=this.getPixelForTick(b)+o.labelOffset,w=this._resolveTickFontOptions(b),C=w.lineHeight,D=H(v)?v.length:1;const z=D/2,B=I.color,tt=I.textStrokeColor,j=I.textStrokeWidth;let wt=k;r?(x=M,k==="inner"&&(b===_-1?wt=this.options.reverse?"left":"right":b===0?wt=this.options.reverse?"right":"left":wt="center"),n==="top"?c==="near"||g!==0?A=-D*C+C/2:c==="center"?A=-q.highest.height/2-z*C+C:A=-q.highest.height+C/2:c==="near"||g!==0?A=C/2:c==="center"?A=q.highest.height/2-z*C:A=q.highest.height-D*C,d&&(A*=-1),g!==0&&!I.showLabelBackdrop&&(x+=C/2*Math.sin(g))):(S=M,A=(1-D)*C/2);let Zt;if(I.showLabelBackdrop){const Pt=Z(I.backdropPadding),Rt=q.heights[b],ft=q.widths[b];let It=A-Pt.top,gt=0-Pt.left;switch(N){case"middle":It-=Rt/2;break;case"bottom":It-=Rt;break}switch(k){case"center":gt-=ft/2;break;case"right":gt-=ft;break;case"inner":b===_-1?gt-=ft:b>0&&(gt-=ft/2);break}Zt={left:gt,top:It,width:ft+Pt.width,height:Rt+Pt.height,color:I.backdropColor}}m.push({label:v,font:w,textOffset:A,options:{rotation:g,color:B,strokeColor:tt,strokeWidth:j,textAlign:wt,textBaseline:N,translation:[x,S],backdrop:Zt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-lt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=mt(t,this.left,r)-r/2,h=mt(t,this.right,a)+a/2,d=u=l):(d=mt(t,this.top,r)-r/2,u=mt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&ye(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Jt(s,c,0,h,l,a)}n&&ve(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=U(s.font),r=Z(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||O(e)?(l+=r.bottom,H(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=Yr(this,l,e,a);Jt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:$r(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=P(t.grid&&t.grid.z,-1),n=P(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Lt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return U(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class ae{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Kr(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Ur(t,r,s),this.override&&W.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in W[n]&&(delete W[n][s],this.override&&delete St[s])}}function Ur(i,t,e){const s=Ut(Object.create(null),[e?W.get(e):{},W.get(t),i.defaults]);W.set(t,s),i.defaultRoutes&&Xr(t,i.defaultRoutes),i.descriptors&&W.describe(t,i.descriptors)}function Xr(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");W.route(o,n,l,a)})}function Kr(i){return"id"in i&&"defaults"in i}class Gr{constructor(){this.controllers=new ae(Se,"datasets",!0),this.elements=new ae(ht,"elements"),this.plugins=new ae(Object,"plugins"),this.scales=new ae(Lt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):L(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=Xe(t);E(s["before"+n],[],s),e[t](s),E(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var st=new Gr;class qr{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(E(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){T(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=P(s.options&&s.options.plugins,{}),o=Jr(s);return n===!1&&!e?[]:Zr(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function Jr(i){const t={},e=[],s=Object.keys(st.plugins.items);for(let o=0;o<s.length;o++)e.push(st.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function Qr(i,t){return!t&&i===!1?null:i===!0?{}:i}function Zr(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=Qr(s[l],n);c!==null&&o.push({plugin:a,options:ta(i.config,{plugin:a,local:e[l]},c,r)})}return o}function ta(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function je(i,t){const e=W.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function ea(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function ia(i,t){return i===t?"_index_":"_value_"}function $i(i){if(i==="x"||i==="y"||i==="r")return i}function sa(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function $e(i,...t){if($i(i))return i;for(const e of t){const s=e.axis||sa(e.position)||i.length>1&&$i(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Yi(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function na(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Yi(i,"x",e[0])||Yi(i,"y",e[0])}return{}}function oa(i,t){const e=St[i.type]||{scales:{}},s=t.scales||{},n=je(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!O(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=$e(r,a,na(r,i),W.scales[a.type]),c=ia(l,n),h=e.scales||{};o[r]=Nt(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||je(a,t),h=(St[a]||{}).scales||{};Object.keys(h).forEach(d=>{const u=ea(d,l),f=r[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),Nt(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Nt(a,[W.scales[a.type],W.scale])}),o}function Qs(i){const t=i.options||(i.options={});t.plugins=P(t.plugins,{}),t.scales=oa(i,t)}function Zs(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function ra(i){return i=i||{},i.data=Zs(i.data),Qs(i),i}const Ui=new Map,tn=new Set;function le(i,t){let e=Ui.get(i);return e||(e=t(),Ui.set(i,e),tn.add(e)),e}const Wt=(i,t,e)=>{const s=Mt(t,e);s!==void 0&&i.add(s)};class aa{constructor(t){this._config=ra(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Zs(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Qs(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return le(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return le(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return le(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return le(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>Wt(l,t,d))),h.forEach(d=>Wt(l,n,d)),h.forEach(d=>Wt(l,St[o]||{},d)),h.forEach(d=>Wt(l,W,d)),h.forEach(d=>Wt(l,Ve,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),tn.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,St[e]||{},W.datasets[e]||{},{type:e},W,Ve]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Xi(this._resolverCache,t,n);let l=r;if(ca(r,e)){o.$shared=!1,s=ut(s)?s():s;const c=this.createResolver(t,s,a);l=At(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=Xi(this._resolverCache,t,s);return O(e)?At(o,e,void 0,n):o}}function Xi(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:Ze(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const la=i=>O(i)&&Object.getOwnPropertyNames(i).some(t=>ut(i[t]));function ca(i,t){const{isScriptable:e,isIndexable:s}=Rs(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(ut(a)||la(a))||r&&H(a))return!0}return!1}var ha="4.5.0";const da=["top","bottom","left","right","chartArea"];function Ki(i,t){return i==="top"||i==="bottom"||da.indexOf(i)===-1&&t==="x"}function Gi(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function qi(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),E(e&&e.onComplete,[i],t)}function ua(i){const t=i.chart,e=t.options.animation;E(e&&e.onProgress,[i],t)}function en(i){return ii()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ue={},Ji=i=>{const t=en(i);return Object.values(ue).filter(e=>e.canvas===t).pop()};function fa(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function ga(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}class pa{static defaults=W;static instances=ue;static overrides=St;static registry=st;static version=ha;static getChart=Ji;static register(...t){st.add(...t),Qi()}static unregister(...t){st.remove(...t),Qi()}constructor(t,e){const s=this.config=new aa(e),n=en(t),o=Ji(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Lr(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=dn(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new qr,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=An(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],ue[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}rt.listen(this,"complete",qi),rt.listen(this,"progress",ua),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return T(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return st}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():yi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return bi(this.canvas,this.ctx),this}stop(){return rt.stop(this),this}resize(t,e){rt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,yi(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),E(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};L(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=$e(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),L(o,r=>{const a=r.options,l=a.id,c=$e(l,a),h=P(a.type,r.dtype);(a.position===void 0||Ki(a.position,c)!==Ki(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=st.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),L(n,(r,a)=>{r||delete s[a]}),L(s,r=>{Q.configure(this,r,r.options),Q.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(Gi("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||je(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=st.getController(a),{datasetElementType:c,dataElementType:h}=W.datasets[a];Object.assign(l,{dataElementType:st.getElement(h),datasetElementType:c&&st.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){L(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||L(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Gi("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){L(this.scales,t=>{Q.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!ai(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;fa(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!ai(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Q.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],L(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ut(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(rt.has(this)?this.attached&&!rt.running(this)&&rt.start(this):(this.draw(),qi({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=js(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&ye(e,n),t.controller.draw(),n&&ve(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return qt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=dr.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=kt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Xt(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),rt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),bi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ue[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};L(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){L(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},L(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!fe(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=bn(t),c=ga(t,this._lastEvent,s,l);s&&(this._lastEvent=null,E(o.onHover,[t,a,this],this),l&&E(o.onClick,[t,a,this],this));const h=!fe(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}function Qi(){return L(pa.instances,i=>i._plugins.invalidate())}function ma(i,t,e){const{startAngle:s,x:n,y:o,outerRadius:r,innerRadius:a,options:l}=t,{borderWidth:c,borderJoinStyle:h}=l,d=Math.min(c/r,J(s-e));if(i.beginPath(),i.arc(n,o,r-c/2,s+d/2,e-d/2),a>0){const u=Math.min(c/a,J(s-e));i.arc(n,o,a+c/2,e-u/2,s+u/2,!0)}else{const u=Math.min(c/2,r*J(s-e));if(h==="round")i.arc(n,o,u,e-R/2,s+R/2,!0);else if(h==="bevel"){const f=2*u*u,p=-f*Math.cos(e+R/2)+n,g=-f*Math.sin(e+R/2)+o,m=f*Math.cos(s+R/2)+n,b=f*Math.sin(s+R/2)+o;i.lineTo(p,g),i.lineTo(m,b)}}i.closePath(),i.moveTo(0,0),i.rect(0,0,i.canvas.width,i.canvas.height),i.clip("evenodd")}function ba(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+V,s-V),i.closePath(),i.clip()}function _a(i){return Qe(i,["outerStart","outerEnd","innerStart","innerEnd"])}function xa(i,t,e,s){const n=_a(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return Y(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:Y(n.innerStart,0,r),innerEnd:Y(n.innerEnd,0,r)}}function Dt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function xe(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0;let f=0;const p=n-l;if(s){const I=h>0?h-s:0,z=d>0?d-s:0,B=(I+z)/2,tt=B!==0?p*B/(B+s):p;f=(p-tt)/2}const g=Math.max(.001,p*d-e/R)/d,m=(p-g)/2,b=l+m+f,_=n-m-f,{outerStart:y,outerEnd:v,innerStart:x,innerEnd:S}=xa(t,u,d,_-b),k=d-y,M=d-v,w=b+y/k,C=_-v/M,D=u+x,A=u+S,N=b+x/D,q=_-S/A;if(i.beginPath(),o){const I=(w+C)/2;if(i.arc(r,a,d,w,I),i.arc(r,a,d,I,C),v>0){const j=Dt(M,C,r,a);i.arc(j.x,j.y,v,C,_+V)}const z=Dt(A,_,r,a);if(i.lineTo(z.x,z.y),S>0){const j=Dt(A,q,r,a);i.arc(j.x,j.y,S,_+V,q+Math.PI)}const B=(_-S/u+(b+x/u))/2;if(i.arc(r,a,u,_-S/u,B,!0),i.arc(r,a,u,B,b+x/u,!0),x>0){const j=Dt(D,N,r,a);i.arc(j.x,j.y,x,N+Math.PI,b-V)}const tt=Dt(k,b,r,a);if(i.lineTo(tt.x,tt.y),y>0){const j=Dt(k,w,r,a);i.arc(j.x,j.y,y,b-V,w)}}else{i.moveTo(r,a);const I=Math.cos(w)*d+r,z=Math.sin(w)*d+a;i.lineTo(I,z);const B=Math.cos(C)*d+r,tt=Math.sin(C)*d+a;i.lineTo(B,tt)}i.closePath()}function ya(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){xe(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%F||F))}return xe(i,t,e,s,l,n),i.fill(),l}function va(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u,borderRadius:f}=l,p=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,p?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){xe(i,t,e,s,g,n);for(let m=0;m<o;++m)i.stroke();isNaN(a)||(g=r+(a%F||F))}p&&ba(i,t,g),l.selfJoin&&g-r>=R&&f===0&&h!=="miter"&&ma(i,t,g),o||(xe(i,t,e,s,g,n),i.stroke())}class Pl extends ht{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.getProps(["x","y"],s),{angle:o,distance:r}=ws(n,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),u=(this.options.spacing+this.options.borderWidth)/2,f=P(d,l-a),p=Gt(o,a,l)&&a!==l,g=f>=F||p,m=ct(r,c+u,h+u);return g&&m}getCenterPoint(t){const{x:e,y:s,startAngle:n,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(r+a+c+l)/2;return{x:e+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:s}=this,n=(e.offset||0)/4,o=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=s>F?Math.floor(s/F):0,s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*n,Math.sin(a)*n);const l=1-Math.sin(Math.min(R,s||0)),c=n*l;t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,ya(t,this,c,o,r),va(t,this,c,o,r),t.restore()}}function sn(i,t,e=t){i.lineCap=P(e.borderCapStyle,t.borderCapStyle),i.setLineDash(P(e.borderDash,t.borderDash)),i.lineDashOffset=P(e.borderDashOffset,t.borderDashOffset),i.lineJoin=P(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=P(e.borderWidth,t.borderWidth),i.strokeStyle=P(e.borderColor,t.borderColor)}function Ma(i,t,e){i.lineTo(e.x,e.y)}function Sa(i){return i.stepped?$n:i.tension||i.cubicInterpolationMode==="monotone"?Yn:Ma}function nn(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function ka(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=nn(n,e,s),h=Sa(o);let{move:d=!0,reverse:u}=s||{},f,p,g;for(f=0;f<=c;++f)p=n[(a+(u?c-f:f))%r],!p.skip&&(d?(i.moveTo(p.x,p.y),d=!1):h(i,g,p,u,o.stepped),g=p);return l&&(p=n[(a+(u?c:0))%r],h(i,g,p,u,o.stepped)),!!l}function wa(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=nn(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,u,f,p,g,m,b;const _=v=>(r+(c?a-v:v))%o,y=()=>{g!==m&&(i.lineTo(h,m),i.lineTo(h,g),i.lineTo(h,b))};for(l&&(f=n[_(0)],i.moveTo(f.x,f.y)),u=0;u<=a;++u){if(f=n[_(u)],f.skip)continue;const v=f.x,x=f.y,S=v|0;S===p?(x<g?g=x:x>m&&(m=x),h=(d*h+v)/++d):(y(),i.lineTo(v,x),p=S,d=0,g=m=x),b=x}y()}function Ye(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?wa:ka}function Pa(i){return i.stepped?ko:i.tension||i.cubicInterpolationMode==="monotone"?wo:xt}function Da(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),sn(i,t.options),i.stroke(n)}function Ca(i,t,e,s){const{segments:n,options:o}=t,r=Ye(t);for(const a of n)sn(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Oa=typeof Path2D=="function";function Aa(i,t,e,s){Oa&&!t.options.segment?Da(i,t,e,s):Ca(i,t,e,s)}class oi extends ht{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;mo(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=To(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=Ns(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Pa(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:u}=r[c],f=o[d],p=o[u];if(f===p){a.push(f);continue}const g=Math.abs((n-f[e])/(p[e]-f[e])),m=l(f,p,g,s.stepped);m[e]=t[e],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,e,s){return Ye(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=Ye(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Aa(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function Zi(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class Dl extends ht{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.options,{x:o,y:r}=this.getProps(["x","y"],s);return Math.pow(t-o,2)+Math.pow(e-r,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return Zi(this,t,"x",e)}inYRange(t,e){return Zi(this,t,"y",e)}getCenterPoint(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);const s=e&&t.borderWidth||0;return(e+s)*2}draw(t,e){const s=this.options;this.skip||s.radius<.1||!qt(this,e,this.size(s)/2)||(t.strokeStyle=s.borderColor,t.lineWidth=s.borderWidth,t.fillStyle=s.backgroundColor,Ne(t,s,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function on(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function dt(i,t,e,s){return i?0:Y(t,e,s)}function Ta(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Ls(s);return{t:dt(n.top,o.top,0,e),r:dt(n.right,o.right,0,t),b:dt(n.bottom,o.bottom,0,e),l:dt(n.left,o.left,0,t)}}function La(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Ct(n),r=Math.min(t,e),a=i.borderSkipped,l=s||O(n);return{topLeft:dt(!l||a.top||a.left,o.topLeft,0,r),topRight:dt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:dt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:dt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Ra(i){const t=on(i),e=t.right-t.left,s=t.bottom-t.top,n=Ta(i,e/2,s/2),o=La(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function ze(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&on(i,s);return a&&(n||ct(t,a.left,a.right))&&(o||ct(e,a.top,a.bottom))}function Ia(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function Ea(i,t){i.rect(t.x,t.y,t.w,t.h)}function Fe(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Cl extends ht{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=Ra(this),a=Ia(r.radius)?me:Ea;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Fe(r,e,o)),t.clip(),a(t,Fe(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Fe(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return ze(this,t,e,s)}inXRange(t,e){return ze(this,t,null,e)}inYRange(t,e){return ze(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}function za(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=we(l,c,n);const h=Ue(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=Ns(t,h);for(const u of d){const f=Ue(e,o[u.start],o[u.end],u.loop),p=Vs(a,n,f);for(const g of p)r.push({source:g,target:u,start:{[e]:ts(h,f,"start",Math.max)},end:{[e]:ts(h,f,"end",Math.min)}})}}return r}function Ue(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=J(n),o=J(o)),{property:i,start:n,end:o}}function Fa(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=we(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function we(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function ts(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function rn(i,t){let e=[],s=!1;return H(i)?(s=!0,e=i):e=Fa(i,t),e.length?new oi({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function es(i){return i&&i.fill!==!1}function Ba(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!X(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function Wa(i,t,e){const s=ja(i);if(O(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return X(n)&&Math.floor(n)===n?Ha(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Ha(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Va(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:O(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Na(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:O(i)?s=i.value:s=t.getBaseValue(),s}function ja(i){const t=i.options,e=t.fill;let s=P(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function $a(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=Ya(t,e);a.push(rn({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)Ua(n,r[h],a)}return new oi({points:n,options:{}})}function Ya(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function Ua(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=Xa(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function Xa(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],u=r[h.end][e];if(ct(n,d,u)){a=n===d,l=n===u;break}}return{first:a,last:l,point:s}}class an{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:F},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function Ka(i){const{chart:t,fill:e,line:s}=i;if(X(e))return Ga(t,e);if(e==="stack")return $a(i);if(e==="shape")return!0;const n=qa(i);return n instanceof an?n:rn(n,s)}function Ga(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function qa(i){return(i.scale||{}).getPointPositionForValue?Qa(i):Ja(i)}function Ja(i){const{scale:t={},fill:e}=i,s=Va(e,t);if(X(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function Qa(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=Na(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new an({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function Be(i,t,e){const s=Ka(t),{chart:n,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,d=c.backgroundColor,{above:u=d,below:f=d}=h||{},p=n.getDatasetMeta(o),g=js(n,p);s&&r.points.length&&(ye(i,e),Za(i,{line:r,target:s,above:u,below:f,area:e,scale:a,axis:l,clip:g}),ve(i))}function Za(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;i.save();let h=o;o!==n&&(c==="x"?(is(i,s,r.top),We(i,{line:e,target:s,color:n,scale:a,property:c,clip:l}),i.restore(),i.save(),is(i,s,r.bottom)):c==="y"&&(ss(i,s,r.left),We(i,{line:e,target:s,color:o,scale:a,property:c,clip:l}),i.restore(),i.save(),ss(i,s,r.right),h=n)),We(i,{line:e,target:s,color:h,scale:a,property:c,clip:l}),i.restore()}function is(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[we(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function ss(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[we(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(e,h.y),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(e,d.y)}i.lineTo(e,t.first().y),i.closePath(),i.clip()}function We(i,t){const{line:e,target:s,property:n,color:o,scale:r,clip:a}=t,l=za(e,s,n);for(const{source:c,target:h,start:d,end:u}of l){const{style:{backgroundColor:f=o}={}}=c,p=s!==!0;i.save(),i.fillStyle=f,tl(i,r,a,p&&Ue(n,d,u)),i.beginPath();const g=!!e.pathSegment(i,c);let m;if(p){g?i.closePath():ns(i,s,u,n);const b=!!s.pathSegment(i,h,{move:g,reverse:!0});m=g&&b,m||ns(i,s,d,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function tl(i,t,e,s){const n=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=r,c=n.top,h=a,d=n.bottom):(l=n.left,c=r,h=n.right,d=a),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function ns(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var Ol={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof oi&&(l={visible:i.isDatasetVisible(r),index:r,fill:Wa(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=Ba(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Be(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;es(o)&&Be(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!es(s)||e.drawTime!=="beforeDatasetDraw"||Be(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const os=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},el=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class rs extends ht{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=E(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=U(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=os(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((p,g)=>{const m=s+e/2+o.measureText(p.text).width;(g===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(g>0?0:1)]=0,f+=h,u++),l[g]={left:0,top:f,row:u,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,u=0,f=0,p=0,g=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=il(s,e,o,m,n);b>0&&f+y+2*a>h&&(d+=u+a,c.push({width:u,height:f}),p+=u+a,g++,u=f=0),l[b]={left:p,top:f,col:g,width:_,height:y},u=Math.max(u,_),f+=y+a}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Ot(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=$(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=$(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;ye(t,this),this._draw(),ve(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=W.color,l=Ot(t.rtl,this.left,this.width),c=U(r.font),{padding:h}=r,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=os(r,d),b=function(S,k,M){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;n.save();const w=P(M.lineWidth,1);if(n.fillStyle=P(M.fillStyle,a),n.lineCap=P(M.lineCap,"butt"),n.lineDashOffset=P(M.lineDashOffset,0),n.lineJoin=P(M.lineJoin,"miter"),n.lineWidth=w,n.strokeStyle=P(M.strokeStyle,a),n.setLineDash(P(M.lineDash,[])),r.usePointStyle){const C={radius:g*Math.SQRT2/2,pointStyle:M.pointStyle,rotation:M.rotation,borderWidth:w},D=l.xPlus(S,p/2),A=k+u;Ts(n,C,D,A,r.pointStyleWidth&&p)}else{const C=k+Math.max((d-g)/2,0),D=l.leftForLtr(S,p),A=Ct(M.borderRadius);n.beginPath(),Object.values(A).some(N=>N!==0)?me(n,{x:D,y:C,w:p,h:g,radius:A}):n.rect(D,C,p,g),n.fill(),w!==0&&n.stroke()}n.restore()},_=function(S,k,M){Jt(n,M.text,S,k+m/2,c,{strikethrough:M.hidden,textAlign:l.textAlign(M.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?f={x:$(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:f={x:this.left+h,y:$(o,this.top+v+h,this.bottom-e[0].height),line:0},Bs(this.ctx,t.textDirection);const x=m+h;this.legendItems.forEach((S,k)=>{n.strokeStyle=S.fontColor,n.fillStyle=S.fontColor;const M=n.measureText(S.text).width,w=l.textAlign(S.textAlign||(S.textAlign=r.textAlign)),C=p+u+M;let D=f.x,A=f.y;l.setWidth(this.width),y?k>0&&D+C+h>this.right&&(A=f.y+=x,f.line++,D=f.x=$(o,this.left+h,this.right-s[f.line])):k>0&&A+x>this.bottom&&(D=f.x=D+e[f.line].width+h,f.line++,A=f.y=$(o,this.top+v+h,this.bottom-e[f.line].height));const N=l.x(D);if(b(N,A,S),D=Tn(w,D+p+u,y?D+C:this.right,t.rtl),_(l.x(D),A,S),y)f.x+=C+h;else if(typeof S.text!="string"){const q=c.lineHeight;f.y+=ln(S,q)+h}else f.y+=x}),Ws(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=U(e.font),n=Z(e.padding);if(!e.display)return;const o=Ot(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=$(t.align,d,this.right-u);else{const p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);h=c+$(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const f=$(a,d,d+u);r.textAlign=o.textAlign(Ge(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Jt(r,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=U(t.font),s=Z(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(ct(t,this.left,this.right)&&ct(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],ct(t,n.left,n.left+n.width)&&ct(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!ol(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=el(n,s);n&&!o&&E(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&E(e.onHover,[t,s,this],this)}else s&&E(e.onClick,[t,s,this],this)}}function il(i,t,e,s,n){const o=sl(s,i,t,e),r=nl(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function sl(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function nl(i,t,e){let s=i;return typeof t.text!="string"&&(s=ln(t,e)),s}function ln(i,t){const e=i.text?i.text.length:0;return t*e}function ol(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Al={id:"legend",_element:rs,start(i,t,e){const s=i.legend=new rs({ctx:i.ctx,options:e,chart:i});Q.configure(i,s,e),Q.addBox(i,s)},stop(i){Q.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Q.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=Z(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class cn extends ht{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=H(s.text)?s.text.length:1;this._padding=Z(s.padding);const o=n*U(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=$(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=$(a,n,e),l=R*-.5):(h=o-t,d=$(a,e,n),l=R*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=U(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Jt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:Ge(e.align),textBaseline:"middle",translation:[r,a]})}}function rl(i,t){const e=new cn({ctx:i.ctx,options:t,chart:i});Q.configure(i,e,t),Q.addBox(i,e),i.titleBlock=e}var Tl={id:"title",_element:cn,start(i,t,e){rl(i,e)},stop(i){const t=i.titleBlock;Q.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Q.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Vt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=He(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function it(i,t){return t&&(H(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function at(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function al(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function as(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=U(t.bodyFont),c=U(t.titleFont),h=U(t.footerFont),d=o.length,u=n.length,f=s.length,p=Z(t.padding);let g=p.height,m=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(g+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=f*v+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(g+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let _=0;const y=function(v){m=Math.max(m,e.measureText(v).width+_)};return e.save(),e.font=c.string,L(i.title,y),e.font=l.string,L(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,L(s,v=>{L(v.before,y),L(v.lines,y),L(v.after,y)}),_=0,e.font=h.string,L(i.footer,y),e.restore(),m+=p.width,{width:m,height:g}}function ll(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function cl(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function hl(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),cl(c,i,t,e)&&(c="center"),c}function ls(i,t,e){const s=e.yAlign||t.yAlign||ll(i,e);return{xAlign:e.xAlign||t.xAlign||hl(i,t,e,s),yAlign:s}}function dl(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function ul(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function cs(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Ct(r);let p=dl(t,a);const g=ul(t,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(h,u)+n:a==="right"&&(p+=Math.max(d,f)+n),{x:Y(p,0,s.width-t.width),y:Y(g,0,s.height-t.height)}}function ce(i,t,e){const s=Z(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function hs(i){return it([],at(i))}function fl(i,t,e){return kt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function ds(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const hn={beforeTitle:ot,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:ot,beforeBody:ot,beforeLabel:ot,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return T(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ot,afterBody:ot,beforeFooter:ot,footer:ot,afterFooter:ot};function K(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?hn[t].call(e,s):n}class us extends ht{static positioners=Vt;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new $s(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=fl(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=K(s,"beforeTitle",this,t),o=K(s,"title",this,t),r=K(s,"afterTitle",this,t);let a=[];return a=it(a,at(n)),a=it(a,at(o)),a=it(a,at(r)),a}getBeforeBody(t,e){return hs(K(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return L(t,o=>{const r={before:[],lines:[],after:[]},a=ds(s,o);it(r.before,at(K(a,"beforeLabel",this,o))),it(r.lines,K(a,"label",this,o)),it(r.after,at(K(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return hs(K(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=K(s,"beforeFooter",this,t),o=K(s,"footer",this,t),r=K(s,"afterFooter",this,t);let a=[];return a=it(a,at(n)),a=it(a,at(o)),a=it(a,at(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(al(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),L(a,h=>{const d=ds(t.callbacks,h);n.push(K(d,"labelColor",this,h)),o.push(K(d,"labelPointStyle",this,h)),r.push(K(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Vt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=as(this,s),c=Object.assign({},a,l),h=ls(this.chart,s,c),d=cs(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Ct(a),{x:u,y:f}=t,{width:p,height:g}=e;let m,b,_,y,v,x;return o==="center"?(v=f+g/2,n==="left"?(m=u,b=m-r,y=v+r,x=v-r):(m=u+p,b=m+r,y=v-r,x=v+r),_=m):(n==="left"?b=u+Math.max(l,h)+r:n==="right"?b=u+p-Math.max(c,d)-r:b=this.caretX,o==="top"?(y=f,v=y-r,m=b-r,_=b+r):(y=f+g,v=y+r,m=b+r,_=b-r),x=y),{x1:m,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Ot(s.rtl,this.x,this.width);for(t.x=ce(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=U(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=U(o.bodyFont),d=ce(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,p=e.y+f;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(u,c)+c/2,b=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Ne(t,g,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Ne(t,g,m,b)}else{t.lineWidth=O(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=n.leftForLtr(u,c),m=n.leftForLtr(n.xPlus(u,1),c-2),b=Ct(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,me(t,{x:g,y:p,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),me(t,{x:m,y:p+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,p,c,l),t.strokeRect(g,p,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=U(s.bodyFont);let u=d.lineHeight,f=0;const p=Ot(s.rtl,this.x,this.width),g=function(M){e.fillText(M,p.x(t.x+f),t.y+u/2),t.y+=u+o},m=p.textAlign(r);let b,_,y,v,x,S,k;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=ce(this,m,s),e.fillStyle=s.bodyColor,L(this.beforeBody,g),f=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,S=n.length;v<S;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,L(b.before,g),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,p,s),u=Math.max(d.lineHeight,l)),x=0,k=y.length;x<k;++x)g(y[x]),u=d.lineHeight;L(b.after,g)}f=0,u=d.lineHeight,L(this.afterBody,g),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Ot(s.rtl,this.x,this.width);for(t.x=ce(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=U(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:p}=Ct(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-u,l),e.quadraticCurveTo(a+c,l,a+c,l+u),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-p),e.quadraticCurveTo(a+c,l+h,a+c-p,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+f,l+h),e.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Vt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=as(this,t),l=Object.assign({},r,this._size),c=ls(e,t,l),h=cs(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=Z(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),Bs(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Ws(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!fe(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!fe(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Vt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}var Ll={id:"tooltip",_element:us,positioners:Vt,afterInit(i,t,e){e&&(i.tooltip=new us({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:hn},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const gl=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function pl(i,t,e,s){const n=i.indexOf(t);if(n===-1)return gl(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const ml=(i,t)=>i===null?null:Y(Math.round(i),0,t);function fs(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Rl extends Lt{static id="category";static defaults={ticks:{callback:fs}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(T(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:pl(s,t,P(e,t),this._addedLabels),ml(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return fs.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function bl(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,p=h-1,{min:g,max:m}=t,b=!T(r),_=!T(a),y=!T(c),v=(m-g)/(d+1);let x=ci((m-g)/p/f)*f,S,k,M,w;if(x<1e-14&&!b&&!_)return[{value:g},{value:m}];w=Math.ceil(m/x)-Math.floor(g/x),w>p&&(x=ci(w*x/p/f)*f),T(l)||(S=Math.pow(10,l),x=Math.ceil(x*S)/S),n==="ticks"?(k=Math.floor(g/x)*x,M=Math.ceil(m/x)*x):(k=g,M=m),b&&_&&o&&Mn((a-r)/o,x/1e3)?(w=Math.round(Math.min((a-r)/x,h)),x=(a-r)/w,k=r,M=a):y?(k=b?r:k,M=_?a:M,w=c-1,x=(M-k)/w):(w=(M-k)/x,jt(w,Math.round(w),x/1e3)?w=Math.round(w):w=Math.ceil(w));const C=Math.max(hi(x),hi(k));S=Math.pow(10,T(l)?C:l),k=Math.round(k*S)/S,M=Math.round(M*S)/S;let D=0;for(b&&(u&&k!==r?(e.push({value:r}),k<r&&D++,jt(Math.round((k+D*x)*S)/S,r,gs(r,v,i))&&D++):k<r&&D++);D<w;++D){const A=Math.round((k+D*x)*S)/S;if(_&&A>a)break;e.push({value:A})}return _&&u&&M!==a?e.length&&jt(e[e.length-1].value,a,gs(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||M===a)&&e.push({value:M}),e}function gs(i,t,{horizontal:e,minRotation:s}){const n=lt(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class _l extends Lt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return T(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=nt(n),c=nt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=bl(n,o);return t.bounds==="ticks"&&Sn(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Je(t,this.chart.options.locale,this.options.ticks.format)}}class Il extends _l{static id="linear";static defaults={ticks:{callback:As.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=X(t)?t:0,this.max=X(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=lt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const Pe={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},G=Object.keys(Pe);function ps(i,t){return i-t}function ms(i,t){if(T(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),X(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(Kt(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function bs(i,t,e,s){const n=G.length;for(let o=G.indexOf(i);o<n-1;++o){const r=Pe[G[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return G[o]}return G[n-1]}function xl(i,t,e,s,n){for(let o=G.length-1;o>=G.indexOf(e);o--){const r=G[o];if(Pe[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return G[e?G.indexOf(e):0]}function yl(i){for(let t=G.indexOf(i)+1,e=G.length;t<e;++t)if(Pe[G[t]].common)return G[t]}function _s(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Ke(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function vl(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function xs(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:vl(i,s,n,e)}class ys extends Lt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new rr._date(t.adapters.date);n.init(e),Nt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:ms(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=X(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=X(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Cn(n,o,r);return this._unit=e.unit||(s.autoSkip?bs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):xl(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:yl(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),xs(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=Y(e,0,r),s=Y(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||bs(o.minUnit,e,s,this._getLabelCapacity(e)),a=P(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Kt(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const p=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,a,r),f++)_s(h,u,p);return(u===s||n.bounds==="ticks"||f===1)&&_s(h,u,p),Object.keys(h).sort(ps).map(g=>+g)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return E(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=lt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,xs(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(ms(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Ds(t.sort(ps))}}function he(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=yt(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=yt(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class El extends ys{static id="timeseries";static defaults=ys.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=he(e,this.min),this._tableRange=he(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(he(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return he(this._table,s*this._tableRange+this._minPos,!0)}}export{Pl as A,Sl as B,pa as C,kl as D,wl as L,Dl as P,Rl as a,Il as b,oi as c,Cl as d,Ll as e,Al as f,Ol as i,Tl as p};
